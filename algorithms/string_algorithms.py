#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符串算法集合
包含KMP字符串匹配、最长公共子串等字符串处理算法
"""

from typing import List


class StringAlgorithms:
    """字符串算法集合"""

    @staticmethod
    def kmp_search(text: str, pattern: str) -> List[int]:
        """
        KMP字符串匹配算法
        时间复杂度: O(n + m)
        空间复杂度: O(m)

        原理: 利用已匹配的信息，避免重复比较
        """
        def build_lps(pattern):
            """构建最长前缀后缀数组"""
            lps = [0] * len(pattern)
            length = 0
            i = 1

            while i < len(pattern):
                if pattern[i] == pattern[length]:
                    length += 1
                    lps[i] = length
                    i += 1
                else:
                    if length != 0:
                        length = lps[length - 1]
                    else:
                        lps[i] = 0
                        i += 1
            return lps

        if not pattern:
            return []

        lps = build_lps(pattern)
        result = []
        i = j = 0

        while i < len(text):
            if text[i] == pattern[j]:
                i += 1
                j += 1

            if j == len(pattern):
                result.append(i - j)
                j = lps[j - 1]
            elif i < len(text) and text[i] != pattern[j]:
                if j != 0:
                    j = lps[j - 1]
                else:
                    i += 1

        return result

    @staticmethod
    def longest_common_substring(text1: str, text2: str) -> int:
        """
        最长公共子串
        时间复杂度: O(m * n)
        空间复杂度: O(m * n)

        原理: 动态规划。
        dp[i][j] 表示以 text1[i-1] 和 text2[j-1] 结尾的最长公共子串的长度。
        如果 text1[i-1] == text2[j-1]，则 dp[i][j] = dp[i-1][j-1] + 1。
        如果 text1[i-1] != text2[j-1]，则以这两个字符结尾的公共子串中断，dp[i][j] = 0。
        最终结果是所有 dp[i][j] 中的最大值。
        """
        m, n = len(text1), len(text2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        max_len = 0

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                    max_len = max(max_len, dp[i][j])
                else:
                    dp[i][j] = 0

        return max_len
