# 网络I/O模型 (面试速查版)

## 五种I/O模型对比

| I/O模型 | 阻塞性 | 编程难度 | 性能 | 适用场景 |
|---|---|---|---|---|
| **阻塞I/O (BIO)** | 阻塞 | 简单 | 低 | 简单或连接数少的应用 |
| **非阻塞I/O (NIO)** | 非阻塞 | 简单 | 中等 | 轮询场景，需要同时处理多个I/O |
| **I/O多路复用** | 非阻塞 | 中等 | 高 | **高并发服务器 (Nginx, Redis)** |
| **信号驱动I/O** | 非阻塞 | 复杂 | 高 | 实时系统 (用得少) |
| **异步I/O (AIO)** | 非阻塞 | 复杂 | 最高 | **高性能应用 (Node.js)** |

---

## 核心模型详解

### 1. 阻塞I/O (Blocking I/O)
- **模型:** 一个线程处理一个连接。发起I/O调用后，线程被**阻塞**，直到数据准备好并复制完成。
- **缺点:** 并发能力差，大量线程导致上下文切换开销大。

### 2. 非阻塞I/O (Non-blocking I/O)
- **模型:** 发起I/O调用后立即返回，但数据没准备好时会返回错误。线程需要**轮询**检查数据是否就绪。
- **缺点:** 轮询消耗大量CPU。

### 3. I/O多路复用 (I/O Multiplexing)
- **核心思想:** **单个线程/进程监控多个文件描述符(FD)**。当任何一个FD就绪时，系统通知应用程序进行处理。
- **优势:** 用少量线程处理大量连接，开销小，是高并发网络编程的基石。
- **实现:** `select`, `poll`, `epoll`。

### 4. 异步I/O (Asynchronous I/O)
- **模型:** 发起I/O请求后立即返回。内核**独立完成所有I/O操作**（数据准备和从内核复制到用户空间），完成后通过**回调函数**通知应用。
- **优势:** 真正的异步，性能极高。

---

## I/O多路复用：select vs poll vs epoll

| 特性 | select | poll | epoll |
|---|---|---|---|
| **底层实现** | 轮询 (数组) | 轮询 (链表) | 事件驱动 (红黑树+就绪队列) |
| **性能** | O(n) | O(n) | **O(1)** |
| **描述符限制** | **有 (默认1024)** | 无 | 无 |
| **内存拷贝** | 每次调用都拷贝FD集合 | 每次调用都拷贝FD集合 | **只在添加时拷贝，之后共享内存** |
| **返回内容** | 就绪的FD数量，需再次遍历 | 就绪的FD数量，需再次遍历 | **直接返回就绪的FD列表** |
| **跨平台** | **是** | 否 | 否 (Linux特有) |

**总结:** `epoll` 是Linux下高并发网络编程的最佳选择，碾压 `select` 和 `poll`。

---

## epoll 核心精讲

### 1. 两种工作模式

- **水平触发 (LT - Level Triggered) - 默认**
  - **特点:** 只要缓冲区有数据，就会**一直通知**。
  - **优点:** 编程简单，不易出错，类似 `poll`。
- **边缘触发 (ET - Edge Triggered)**
  - **特点:** 只有在状态从未就绪变为就绪时，才**通知一次**。
  - **优点:** 效率更高，减少了事件通知的次数。
  - **要求:** 必须一次性将缓冲区数据读完 (循环read直到返回`EAGAIN`)，否则可能丢失事件。需要配合非阻塞I/O使用。

### 2. epoll 为什么快？
- **O(1) 复杂度:** 不随监控的FD数量增加而变慢。
- **事件驱动:** 内核维护一个就绪队列，`epoll_wait`直接返回就绪的FD，无需轮询全部FD。
- **内存优化:** 通过 `mmap` 共享内存，避免了用户空间和内核空间之间不必要的FD集合拷贝。

---

## 面试速记 Q&A

**Q: select、poll、epoll的区别？**
A:
1.  **性能:** `epoll` 是 `O(1)`，`select/poll` 是 `O(n)`。
2.  **FD限制:** `select` 有数量限制 (1024)，`epoll/poll` 没有。
3.  **数据拷贝:** `epoll` 使用共享内存，避免了 `select/poll` 每次调用的重复拷贝。
4.  **返回机制:** `epoll` 直接返回就绪的FD列表，`select/poll` 返回后需要用户自己遍历查找。

**Q: epoll的LT和ET模式有什么区别？**
A:
- **LT (水平触发):** 只要有数据就一直通知，安全但效率稍低。
- **ET (边缘触发):** 状态变化时才通知一次，效率高但编程要求高，必须一次读完数据，否则事件会丢失。

**Q: 为什么Nginx、Redis这类高并发中间件都用I/O多路复用？**
A: 因为它们需要用极少的线程（通常是单线程）来处理海量的并发连接和I/O请求。I/O多路复用（特别是`epoll`）使得单个线程可以高效地管理成千上万个连接，而不会因线程阻塞和频繁的上下文切换而导致性能瓶颈。这是实现高并发、低延迟网络服务的关键技术。