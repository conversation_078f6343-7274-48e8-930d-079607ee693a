## CDN工作原理与优化 (精简版)

### 核心概念
- **CDN (内容分发网络)**: 在全球部署边缘节点，缓存内容，使用户就近获取，从而提高访问速度、降低延迟。
- **核心价值**: 加速访问、降低源站负载、提高可用性。

### 工作流程
1.  **用户请求**: 用户向域名发起请求。
2.  **DNS解析**: DNS将域名解析到CDN的智能调度系统。
3.  **智能调度**: 根据用户位置、网络状况、节点负载，选择最优的边缘节点，返回其IP。
4.  **节点访问**: 用户向最优节点发起请求。
5.  **内容响应**:
    - **缓存命中**: 节点直接返回缓存内容。
    - **缓存未命中**: 节点向上层缓存（区域节点）或直接向源站请求内容，缓存后返回给用户。

### 核心技术
- **缓存策略**:
    - **层次**: L1(边缘) -> L2(区域) -> 源站。
    - **算法**: LRU (最久未使用), LFU (最少使用), TTL (过期时间)。
    - **更新**: 主动刷新(手动)、被动更新(TTL过期)、版本号更新。
- **负载均衡/智能调度**: 确保请求被导向最合适的节点。
- **内容分发**: 从源站到各级节点的同步机制。

### 应用场景
- **静态内容加速**: 图片、CSS/JS文件、软件包等。
- **动态内容加速**: 通过边缘计算，在边缘节点处理动态请求。
- **流媒体加速**: 视频直播、点播 (HLS/DASH协议)。

### 性能优化
- **缓存优化**: 提高缓存命中率，减少回源请求，设置合理的缓存策略，内容预取。
- **网络优化**: HTTP/2, QUIC, TLS 1.3, Brotli压缩。
- **边缘计算**: 将计算任务下沉到边缘，减少回源延迟（例如：动态页面生成、风控等）。

### 安全
- **DDoS防护**: 清洗恶意流量，分散攻击。
- **WAF (Web应用防火墙)**: 防御SQL注入、XSS等应用层攻击。
- **访问控制**: 防盗链、Token鉴权、地理位置限制。

### 面试核心 Q&A

**Q: CDN如何提升性能？**
A:
- **就近访问**: 减少物理距离和网络跳数，降低延迟。
- **缓存**: 边缘节点直接响应，减少回源，降低源站压力。
- **高可用**: 多节点互为备份，源站故障时仍可提供服务。

**Q: 缓存更新策略有哪些？**
A:
- **主动刷新 (Purge)**: 手动触发，立即删除指定内容的缓存。
- **被动更新**: 等待缓存根据TTL（生存时间）过期后自动更新。
- **URL版本号**: 资源URL带上版本号 (`style.css?v=1.1`)，发布新版时修改版本号即可强制更新。

**Q: 动态内容如何加速？**
A:
- **路径优化**: CDN通过智能路由选择最优回源路径。
- **协议优化**: 优化TCP连接、使用HTTP/2等。
- **边缘计算 (Edge Computing)**: 在边缘节点执行部分业务逻辑，无需回源。

**Q: CDN有哪些局限性？**
A:
- **首次访问延迟**: 冷启动时需回源，速度较慢。
- **动态内容缓存难**: 个性化内容缓存命中率低。
- **成本问题**: 大流量或功能复杂的CDN服务费用较高。
- **架构复杂性**: 增加缓存管理、刷新等运维工作。
