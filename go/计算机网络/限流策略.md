## 限流策略核心概念

### 限流的目的
- **保护系统**：防止因瞬时流量过高导致服务过载、崩溃。
- **保证SLA**：确保服务质量，维持稳定的响应时间。
- **资源公平**：防止部分用户或应用滥用资源，确保公平性。
- **成本控制**：在云环境中，限制流量可以有效控制资源使用成本。

### 核心限流算法

#### 1. 固定窗口 (Fixed Window)
- **原理**：在固定的时间窗口内，限制请求不能超过一个阈值。
- **特点**：
    - ✅ 实现简单，易于理解。
    - ❌ 在窗口边界可能出现两倍于阈值的瞬时流量。

#### 2. 滑动窗口 (Sliding Window)
- **原理**：将时间窗口划分为更小的格子，每次向右滑动一格，计算当前窗口内的请求总数。
- **特点**：
    - ✅ 解决了固定窗口的边界问题，限流更平滑。
    - ❌ 实现相对复杂，需要更多存储空间。

#### 3. 令牌桶 (Token Bucket)
- **原理**：系统以恒定速率向桶中放入令牌，请求需要先获取令牌才能被处理。桶满则令牌丢弃。
- **特点**：
    - ✅ 允许并能有效处理突发流量（桶内积攒的令牌可以应对）。
    - ✅ 控制的是平均速率，对突发流量友好。
    - 💡 Go标准库 `golang.org/x/time/rate` 提供了实现。

#### 4. 漏桶 (Leaky Bucket)
- **原理**：请求像水一样进入漏桶，桶以恒定的速率漏出（处理请求），如果桶满了则后续请求被丢弃。
- **特点**：
    - ✅ 强制限制了请求处理速率，输出流量非常平滑。
    - ❌ 无法应对突发流量，即使系统有处理能力。

### 算法对比

| 算法 | 突发流量支持 | 实现复杂度 | 内存占用 | 适用场景 |
| :--- | :--- | :--- | :--- | :--- |
| **固定窗口** | 部分支持 | 低 | 低 | 简单、不要求精确的场景 |
| **滑动窗口** | 较好支持 | 中 | 中 | 需要平滑限流的场景 |
| **令牌桶** | 很好支持 | 中 | 低 | 需要应对突发流量的API网关等 |
| **漏桶** | 不支持 | 中 | 中 | 要求严格、平滑速率的场景（如MQ消费） |


### 分布式限流方案
- **核心思想**：将限流的计数器、状态等信息存储在所有实例都能访问的集中式存储中。
- **常用实现**：**Redis + Lua 脚本**
    - **Redis**：作为集中式存储，性能高。
    - **Lua 脚本**：将 "读取当前计数值、判断、更新计数值" 等多个操作打包成一个原子操作，避免并发问题。

---

### 面试核心 Q&A

**Q: 令牌桶和漏桶的核心区别？**
- **令牌桶**：主要目的是控制请求的 **平均速率**，并允许一定程度的 **突发流量**。只要桶里有令牌，请求就能被快速处理。
- **漏桶**：主要目的是平滑请求，以一个 **恒定的速率** 处理请求，不管请求来得多快多慢。它无法应对突发流量。

**Q: 如何设计一个分布式限流系统？**
1.  **中心化存储**：选择 Redis 或类似的高性能KV存储，用于存放限流相关的计数器或状态。
2.  **保证原子性**：必须使用 Lua 脚本（或 Redis 事务）来封装 `read-modify-write` 逻辑，防止在分布式环境下出现竞态条件。
3.  **算法选择**：根据业务场景选择合适的算法。例如，用 Redis 的 `INCR` + `EXPIRE` 可以简单实现固定窗口；用 ZSET 可以实现滑动窗口。

**Q: 如果限流触发了，后续该如何处理？**
1.  **直接拒绝**：最简单的策略，直接向客户端返回错误码，如 `429 Too Many Requests`。
2.  **排队等待**：将请求放入消息队列，等待后续处理。适合异步或对延迟不敏感的业务。
3.  **服务降级**：当核心服务被限流时，可以返回一个兜底数据或缓存数据，保证部分可用性。
4.  **熔断**：如果限流频繁触发，说明下游服务可能已存在问题，可以结合熔断器，在一段时间内直接断开对下游服务的调用，防止雪崩。

**Q: 如何监控限流效果？**
1.  **限流命中率**：记录并监控被限流的请求占比。
2.  **系统负载**：观察限流策略生效后，服务的CPU、内存、QPS等指标是否变得平稳。
3.  **业务指标**：分析限流对核心业务成功率、用户体验等的影响。
4.  **告警**：当限流率超过某个阈值时，需要及时告警，以便人工介入分析原因（是正常流量高峰还是恶意攻击）。
