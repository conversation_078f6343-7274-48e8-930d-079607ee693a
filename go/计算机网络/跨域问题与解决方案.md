## 跨域问题核心速查

### 1. 核心概念
- **同源策略**: 浏览器安全机制，禁止脚本访问不同源的资源。
- **同源**: **协议、域名、端口**均相同。
- **跨域**: 三者任一不同即为跨域。

### 2. 主要解决方案
| 方案 | 原理与特点 | 适用场景 |
| :--- | :--- | :--- |
| **CORS** | **主流方案**。服务端设置`Access-Control-Allow-*`响应头，浏览器据此放行。 | 通用，支持所有请求方法。 |
| **JSONP**| 利用`<script>`标签无跨域限制的特性。 | **只支持GET**，兼容老浏览器。|
| **代理** | 浏览器与同源代理服务器通信，由代理转发请求到目标服务器。 | 开发环境(webpack)，生产环境(Nginx)。彻底解决跨域。|
| **PostMessage**| `window.postMessage()` API。 | `iframe`、`window`间通信。 |
| **WebSocket**| WebSocket协议本身不受同源策略限制。 | 实时通信。 |

### 3. CORS 详解
- **简单请求**:
    - 方法: `GET`, `POST`, `HEAD`
    - 头部: 无自定义头部
    - 特点: 直接发请求，服务端返回`Access-Control-Allow-Origin`即可。
- **预检请求 (Preflight)**:
    - 触发条件: `PUT/DELETE`等方法、带自定义头部 (如 `X-Token`)、`Content-Type`为`application/json`等。
    - 过程: 先发`OPTIONS`请求到服务端进行"预检"，服务端返回允许后，再发送真实请求。
- **关键响应头**:
    - `Access-Control-Allow-Origin`: `https://example.com` (允许的源，**安全起见不用`*`**)
    - `Access-Control-Allow-Methods`: `GET, POST, PUT` (允许的方法)
    - `Access-Control-Allow-Headers`: `Content-Type, X-Token` (允许的头部)
    - `Access-Control-Allow-Credentials`: `true` (允许携带Cookie，需客户端配合`withCredentials: true`)
    - `Access-Control-Max-Age`: `86400` (预检请求缓存时间/秒)

### 4. 面试核心问题
- **Q: 什么是跨域？为什么需要同源策略？**
    - **跨域**: 协议、域名、端口任一不同，浏览器会限制Ajax等请求。
    - **目的**: 防止恶意网站读取其他网站的敏感数据（如Cookie），保护用户安全。
- **Q: 解决跨域有几种方式？**
    - **CORS**: 主流方案，服务端配置。
    - **代理**: Nginx反向代理，一劳永逸。
    - **JSONP**: 利用script标签，仅GET。
    - **其他**: `PostMessage` (iframe通信), `WebSocket` (实时通信)。
- **Q: CORS中简单请求和预检请求的区别？**
    - **简单请求**直接发。**非简单请求**（如`PUT`或自定义头）会先发一次`OPTIONS`预检请求，确认服务器是否允许，允许后再发实际请求。
- **Q: 如何实现携带Cookie的跨域请求？**
    - **服务端**: 设置`Access-Control-Allow-Credentials: true`，且 `Access-Control-Allow-Origin`不能为`*`，必须是精确域名。
    - **前端**: Ajax请求需设置 `withCredentials: true`。
