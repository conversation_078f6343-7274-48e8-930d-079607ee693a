## ARP协议精简版

### 核心概念
- **作用**: 将局域网内的 **IP地址** 解析为 **MAC地址**。
- **目的**: 数据链路层（L2）使用MAC地址进行通信，而网络层（L3）使用IP地址。ARP是连接L2和L3的桥梁。

### 工作流程
1.  **ARP请求 (广播)**
    - 主机A想发数据给同一网络下的主机B，但只知道B的IP。
    - A向全网广播一个ARP请求包，内容为："IP为xxx.xxx.xxx.xxx的机器，你的MAC地址是什么？"

2.  **ARP响应 (单播)**
    - 局域网内所有主机收到请求，但只有IP匹配的主机B会响应。
    - B通过单播向A发送ARP响应包，内容为："我的MAC地址是..."。

3.  **缓存更新**
    - A收到响应后，将B的IP-MAC映射存入自己的 **ARP缓存表**。
    - B在收到A的请求时，也会将A的IP-MAC映射存入自己的ARP缓存。

4.  **数据传输**
    - A现在可以使用B的MAC地址来封装数据帧并发送数据。

### ARP缓存
- **作用**: 存储IP与MAC的映射关系，避免每次通信都广播ARP请求，提高效率。
- **特点**:
    - **动态学习**: 通过ARP请求/响应自动建立。
    - **老化机制**: 缓存条目有生命周期（通常几分钟），到期删除以保证时效性。
- **查看命令**: `arp -a`

### 安全威胁与防护
- **ARP欺骗 (中间人攻击)**
    - **原理**: 攻击者发送伪造的ARP响应，将网关或其他主机的IP映射到自己的MAC地址。
    - **危害**: 流量被攻击者嗅探、篡改或丢弃。
- **防护措施**:
    - **静态ARP绑定**: 手动将关键IP（如网关）与MAC地址绑定。
    - **网络分段 (VLAN)**: 减小广播域，限制攻击范围。
    - **交换机安全策略**: 如DAI (Dynamic ARP Inspection) 和 DHCP Snooping。

### 核心面试点
- **ARP作用?**
  > IP地址 -> MAC地址 的解析。
- **为什么请求是广播，响应是单播?**
  > 请求时不知道对方MAC，只能广播寻找。响应时已经知道请求方MAC，所以单播回复。
- **ARP缓存的作用?**
  > 缓存IP-MAC映射，减少广播，提高效率。
- **如何防范ARP欺骗?**
  > 核心是静态绑定网关的MAC地址。其他还有网络分段、交换机安全策略等。
- **ARP在哪一层?**
  > 属于数据链路层（L2）协议，但它解析网络层（L3）的地址，是连接两层的桥梁。