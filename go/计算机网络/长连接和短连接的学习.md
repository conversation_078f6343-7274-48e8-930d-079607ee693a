## HTTP长连接与短连接：面试速记版

### 1. 核心概念对比

| 特性 | 短连接 (Short-lived) | 长连接 (Keep-Alive) |
|---|---|---|
| **方式** | 每次请求都新建TCP连接 | 复用TCP连接，减少握手开销 |
| **性能** | 开销大 (频繁握手挥手) | 开销小 |
| **场景** | 低频、一次性请求 | 高频、需要快速响应的API |

### 2. Go HTTP客户端核心陷阱

根本问题在于 `http.DefaultClient` 的传输层 `http.Transport` 配置非常保守：
- **`MaxIdleConnsPerHost` 默认仅为 2**: 这是最常见的性能瓶颈！在高并发场景下，此设置导致连接无法被有效复用，强制创建新连接，从而引发大量 `TIME_WAIT`。

### 3. TIME_WAIT 过多：原因与解决方案

#### 现象
- `netstat -an | grep TIME_WAIT | wc -l` 数量巨大。
- 服务端响应延迟（RT）增加，客户端出现连接超时错误。

#### 根本原因
**连接未能有效复用**，导致被频繁创建和关闭。
1.  **Go客户端配置不当**: `MaxIdleConnsPerHost` 过小（主要原因）。
2.  **代码错误**: `resp.Body` 未被正确关闭 (`defer resp.Body.Close()`)，导致连接未归还连接池。
3.  **服务端禁用Keep-Alive**: 服务端主动关闭连接。

#### 解决方案：优化 `http.Client`
**永远不要使用 `http.DefaultClient`**，必须自定义一个可复用的 `Client` 实例。

```go
// 全局可复用的优化配置 Client
var httpClient = &http.Client{
    Transport: &http.Transport{
        // 核心优化点:
        MaxIdleConns:        100,              // 整个客户端支持的最大空闲连接数
        MaxIdleConnsPerHost: 100,              // 关键：针对每个host的最大空闲连接数
        MaxConnsPerHost:     100,              // 关键：针对每个host的最大连接数
        
        // 其他推荐配置:
        IdleConnTimeout:     90 * time.Second, // 空闲连接超时
        DisableKeepAlives:   false,            // 确保启用Keep-Alive
    },
    Timeout: 30 * time.Second, // 请求总超时
}

func main() {
    // 在应用中复用此 httpClient 实例
}
```
**关键**：确保 `resp.Body` 被关闭，否则连接会泄漏，无法回到池中。

### 4. 系统层面辅助优化

当应用层已优化但 `TIME_WAIT` 依然是问题时，可考虑调整内核参数：
```bash
# 查看 TIME_WAIT 数量
netstat -an | grep TIME_WAIT | wc -l

# 增加文件描述符限制 (非常重要)
ulimit -n 65536

# 调整TCP参数（作为最后手段，谨慎使用）
# echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse  # 开启 TIME_WAIT 复用
# echo 30 > /proc/sys/net/ipv4/tcp_fin_timeout # 缩短 TIME_WAIT 时间
```

### 5. 面试核心 Q&A

**Q: Go默认HTTP客户端有什么问题？**
A: `MaxIdleConnsPerHost` 默认值是2。在高并发下，连接池形同虚设，导致性能瓶颈和大量`TIME_WAIT`。

**Q: 为什么会产生大量TIME_WAIT？**
A: 根本原因是TCP连接没有被复用，被频繁地创建和销毁。客户端主动关闭连接后会进入`TIME_WAIT`状态。具体原因：
1.  **客户端**：`MaxIdleConnsPerHost`太小；`resp.Body`没关。
2.  **服务端**：不支持或关闭了Keep-Alive。

**Q: 如何优化HTTP客户端？**
A: 
1.  **代码层面 (首选)**：绝不使用默认Client。创建并复用一个自定义`http.Client`，将`MaxIdleConnsPerHost`调大（如100），并确保每次请求后都关闭`resp.Body`。
2.  **系统层面 (辅助)**：适当增加文件描述符限制，并在必要时开启`tcp_tw_reuse`。