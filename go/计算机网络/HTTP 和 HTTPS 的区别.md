## HTTP vs. HTTPS 核心速查

### 核心差异
| 对比项 | HTTP | HTTPS (HTTP + SSL/TLS) |
|---|---|---|
| **安全性** | 明文传输，不安全 | 加密传输，安全 |
| **端口** | 80 | 443 |
| **证书** | 不需要 | 需要CA证书验证服务器身份 |
| **性能开销**| 低 | 较高 (握手和加解密过程) |

---

### HTTPS 如何保证安全？
通过 **SSL/TLS** 协议，在HTTP下层加了一层安全层，实现了三大安全目标：

1.  **机密性 (防窃听)**
    - **方法**: 对称加密 (如: `AES`, `ChaCha20`)
    - **作用**: 对传输的数据进行加密，即使被截获也无法读取。
    - **密钥**: 通信双方在握手阶段协商出的一个**临时会话密钥**。

2.  **完整性 (防篡改)**
    - **方法**: 消息认证码 (MAC) / 数字签名
    - **作用**: 确保数据在传输过程中没有被修改。接收方会根据数据和密钥重新计算MAC并与收到的MAC比较。

3.  **身份认证 (防冒充)**
    - **方法**: 数字证书 (依赖CA信任链)
    - **作用**: 客户端验证服务器的身份是真实可信的，而非伪造的。

---

### HTTPS 工作流程 (简化握手)
**核心目标**: 安全地协商出用于**对称加密**的**会话密钥**。

1.  **客户端请求**: 发起`https`请求 (`ClientHello`)。
2.  **服务器响应**: 返回**数字证书** (内含公钥) (`ServerHello`, `Certificate`)。
3.  **客户端验证与密钥交换**:
    - 验证证书有效性。
    - 生成一个随机数 (预主密钥 `pre-master secret`)。
    - 用服务器的**公钥**加密这个随机数，发给服务器。
4.  **生成会话密钥**:
    - 服务器用自己的**私钥**解密，得到预主密钥。
    - **双方**根据相同的算法，使用预主密钥生成最终的**会话密钥**。
5.  **加密通信**: 使用**会话密钥**对后续所有HTTP数据进行**对称加密**通信。

> **小结**: 用**非对称加密**的安全性，来协商出**对称加密**的密钥，兼顾了安全与性能。