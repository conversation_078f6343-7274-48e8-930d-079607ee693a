## 负载均衡核心策略

### 核心概念
负载均衡（Load Balancing）是将网络请求均匀分发到多个后端服务器，以提高系统**可用性**、**性能**和**扩展性**。

### 关键算法对比

| 算法 | 原理 | 优点 | 缺点 | 适用场景 |
|---|---|---|---|---|
| **轮询 (Round Robin)** | 逐一按顺序分配 | 简单绝对公平 | 不关心服务器状态 | 后端性能相近 |
| **加权轮询 (Weighted)** | 按权重比例分配 | 应对性能差异 | 权重静态，无法应对瞬时变化 | 后端性能有差异 |
| **最少连接 (Least Conn)** | 分配给连接数最少的 | 动态感知负载压力 | 维护连接数有开销 | 长连接、请求耗时不一 |
| **IP哈希 (IP Hash)** | 基于客户端IP哈希 | 实现会话保持 | 可能负载不均 | 需要会-话保持 |
| **最短响应时间** | 响应最快者优先 | 用户体验最优 | 需监控和计算响应时间 | 对响应延迟敏感 |


### L4 vs L7 负载均衡

| 类型 | 工作层级 | 决策依据 | 核心特点 | 代表 |
|---|---|---|---|---|
| **四层 (L4)** | 传输层 | IP + 端口 | 性能极高，无内容感知 | LVS, F5 |
| **七层 (L7)** | 应用层 | HTTP报文(URL, Header) | 功能丰富，可做复杂路由、SSL卸载 | Nginx, HAProxy |


### 实现方式

| 类型 | 优点 | 缺点 | 代表 |
|---|---|---|---|
| **DNS** | 简单、实现全局负载 | 缓存导致更新延迟、无健康检查 | DNS轮询 |
| **硬件** | 性能强、功能全面 | 昂贵、扩展性差 | F5, A10 |
| **软件** | 成本低、灵活、易扩展 | 性能有上限 | Nginx, LVS, HAProxy |


### 高可用与健康检查

- **健康检查**: 通过TCP、HTTP或自定义接口探测后端服务状态，实现故障节点的自动摘除与恢复。
- **会话保持**:
  - **IP绑定**: 基于IP哈希，简单但可能因NAT导致不均。
  - **Cookie注入**: LB在响应中注入Cookie，最常用、灵活。
  - **Session共享**: 借助Redis等中间件集中存储Session。
- **LB自身高可用**:
    - **主备/集群**: Keepalived + VIP漂移，实现LB故障切换。
    - **DNS轮询**: 配置多个LB入口IP。

### 面试核心 Q&A

**1. 如何选择算法？**
   - **无状态应用**: 轮询、最少连接。
   - **有状态应用**: IP哈希、Cookie注入等。
   - **性能差异大**: 加权类算法。

**2. L4 和 L7 如何选择？**
   - **L4**: 追求极致性能、TCP/UDP协议转发场景。
   - **L7**: 需要内容路由（如URL路径、请求头）、SSL卸载、Web缓存等复杂功能。

**3. 监控哪些指标？**
   - **流量**: QPS、带宽、连接数。
   - **延迟**: 平均响应时间、P99/P95分位延迟。
   - **健康度**: 后端服务器健康检查成功率、错误率。
