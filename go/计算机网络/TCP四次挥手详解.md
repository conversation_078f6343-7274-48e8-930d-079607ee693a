# TCP四次挥手速查

## 核心过程

1.  **FIN (客户端 → 服务器)**: 客户端请求关闭，发送`FIN`。
    - `客户端: ESTABLISHED → FIN_WAIT_1`
2.  **ACK (服务器 → 客户端)**: 服务器确认收到请求，发送`ACK`。
    - `服务器: ESTABLISHED → CLOSE_WAIT`
    - 此时服务器可能还有数据要发送。
3.  **FIN (服务器 → 客户端)**: 服务器数据发送完毕，请求关闭，发送`FIN`。
    - `服务器: CLOSE_WAIT → LAST_ACK`
4.  **ACK (客户端 → 服务器)**: 客户端确认关闭，发送`ACK`。
    - `客户端: FIN_WAIT_2 → TIME_WAIT`

**状态变化路径:**
- **主动关闭方**: `ESTABLISHED → FIN_WAIT_1 → FIN_WAIT_2 → TIME_WAIT → CLOSED`
- **被动关闭方**: `ESTABLISHED → CLOSE_WAIT → LAST_ACK → CLOSED`

---

## 核心面试点

### 为什么是四次挥手而不是三次？
- **TCP是全双工的**: 连接需要双向关闭。
- **延迟关闭**: 被动方收到`FIN`后，可能还有数据未发完，不能立即关闭。所以先回复`ACK`表示收到，等数据发完后再发送`FIN`。

### 关键状态详解

#### TIME_WAIT (主动关闭方)
- **目的**:
    1.  **确保最后的ACK能到达对方**: 如果最终的`ACK`丢失，对方会重传`FIN`，客户端可在`TIME_WAIT`状态下重新发送`ACK`。
    2.  **防止旧连接的数据串扰**: 等待 `2MSL` (Maximum Segment Lifetime，报文最大生存时间)，确保本次连接中所有延迟的报文都从网络中消失。
- **问题**: 大量`TIME_WAIT`状态会占用系统端口和内存资源。
- **如何解决/优化**:
    - **开启 `SO_REUSEADDR`**: 允许端口复用。
    - **调整内核参数**: 缩短`TIME_WAIT`时长（例如 `net.ipv4.tcp_tw_reuse` 和 `net.ipv4.tcp_fin_timeout`）。
    - **使用连接池**: 减少短连接的创建和销毁。

#### CLOSE_WAIT (被动关闭方)
- **产生原因**: 收到对方的关闭请求（`FIN`）后，己方应用程序没有执行`close()`操作。
- **问题定位**: 大量`CLOSE_WAIT`通常是 **应用程序的Bug**，导致连接资源无法释放。需要排查代码，确保连接在处理完毕后被正确关闭。
