## OSI 与 TCP/IP 模型核心速记

本文档旨在帮助您在面试前快速回顾OSI和TCP/IP模型的关键概念，重点在于两者的对应关系和核心功能。

### 核心模型对比

| OSI 七层模型 | TCP/IP 四层模型 | 核心功能与协议 | 数据单元 | 地址 |
| :--- | :--- | :--- | :--- | :--- |
| **应用层** (Application) | **应用层** (Application) | 为应用提供网络服务 (HTTP, FTP, DNS) | 数据 (Data) | 域名 |
| **表示层** (Presentation) | | 数据格式转换、加解密 (SSL/TLS, JPEG) | | |
| **会话层** (Session) | | 建立、管理和终止会话 | | |
| **传输层** (Transport) | **传输层** (Transport) | 端到端可靠/不可靠传输、流量控制 (TCP, UDP) | 段 (Segment) | 端口号 |
| **网络层** (Network) | **网络层** (Internet) | 路由选择、逻辑寻址 (IP, ICMP, ARP) | 包 (Packet) | IP地址 |
| **数据链路层** (Data Link) | **网络接口层** (Link) | 物理寻址、错误检测 (Ethernet, PPP) | 帧 (Frame) | MAC地址 |
| **物理层** (Physical) | | 比特流传输 (RJ45, 光纤) | 比特 (Bit) | 无 |


### 数据封装过程：从URL到比特流

当你在浏览器访问一个网站时，数据是这样被层层打包的：

1.  **应用层**: 你输入 `http://example.com`，应用层生成 HTTP 请求数据。
2.  **传输层**: 为 HTTP 请求添加 TCP 头部（包含源/目的端口号），形成 **TCP段 (Segment)**。
3.  **网络层**: 为 TCP 段添加 IP 头部（包含源/目的 IP 地址），形成 **IP包 (Packet)**。
4.  **网络接口层 (数据链路层)**: 为 IP 包添加以太网头部（包含源/目的 MAC 地址），形成 **以太网帧 (Frame)**。
5.  **网络接口层 (物理层)**: 将帧转换为 **比特流 (Bits)**，通过网线、光纤或无线电波发送出去。

*接收方则执行相反的**解封装**过程，层层剥离头部，最终将数据交给应用程序。*

### 面试核心问答

**Q: OSI 和 TCP/IP 模型最大的区别是什么？**
A:
- **分层不同**: OSI 是理论化的七层模型，TCP/IP 是事实标准的四层模型，更贴近实际应用。
- **关注点不同**: OSI 精确定义了服务、协议和接口，而 TCP/IP 在设计时未严格区分它们。
- **协议关系**: OSI 模型先于协议出现，作为设计指导；TCP/IP 的协议先出现，模型是对协议簇的总结。

**Q: 如何利用分层模型排查网络故障？**
A: 采用 **自底向上** 的方法：
1.  **物理层**: 网线插好了吗？设备通电了吗？
2.  **数据链路层**: MAC 地址冲突了吗？交换机端口正常吗？
3.  **网络层**: `ping` 一下网关/目标地址，能通吗？路由配置对吗？
4.  **传输层**: 端口开放吗？（用 `telnet` 或 `nc` 命令测试）
5.  **应用层**: 应用程序的配置或日志有问题吗？DNS 解析正常吗？

**Q: 不同层的设备有哪些？**
A:
- **网络接口层**: **交换机** (Switch, L2)、**集线器** (Hub, L1)、网卡
- **网络层**: **路由器** (Router, L3)
- **应用层**: **网关** (Gateway, L4-L7)、防火墙、负载均衡器

**Q: 为什么需要 MAC 地址和 IP 地址？**
A:
- **IP 地址 (逻辑地址)**: 用于在广域网中找到目标**网络**，是可变的，类似于收件地址中的"城市和小区"。
- **MAC 地址 (物理地址)**: 用于在局域网（同一网段）中找到目标**设备**，是固化在网卡上的，类似于收件地址中的"门牌号"。数据包在跨网络传输时，IP 地址不变，但 MAC 地址在每一跳都会改变。
