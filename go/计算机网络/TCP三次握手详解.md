# TCP三次握手（面试精简版）

### 核心目的
确保双方的 **收、发** 能力都正常，并同步双方的初始序列号 (ISN)。

---

### 三次握手过程

1.  **客户端 → 服务器 (SYN)**
    *   **动作**: 客户端发送一个 `SYN` 包，请求建立连接。
    *   **标志位**: `SYN=1`
    *   **序列号**: `seq=x` (一个随机的初始序列号)
    *   **状态**: 客户端 `CLOSED` → `SYN_SENT`

2.  **服务器 → 客户端 (SYN+ACK)**
    *   **动作**: 服务器收到 `SYN` 后，回复一个 `SYN+ACK` 包，表示同意连接，并请求客户端确认。
    *   **标志位**: `SYN=1`, `ACK=1`
    *   **序列号**: `seq=y` (服务器的随机初始序列号)
    *   **确认号**: `ack=x+1` (期望收到客户端下一个包的序列号)
    *   **状态**: 服务器 `LISTEN` → `SYN_RCVD`

3.  **客户端 → 服务器 (ACK)**
    *   **动作**: 客户端收到 `SYN+ACK` 后，发送一个 `ACK` 包作为确认。
    *   **标志位**: `ACK=1`
    *   **序列号**: `seq=x+1`
    *   **确认号**: `ack=y+1`
    *   **状态**: 客户端 `SYN_SENT` → `ESTABLISHED`；服务器收到ACK后 `SYN_RCVD` → `ESTABLISHED`。

---

### 常见面试题

#### Q1: 为什么是三次握手，而不是两次或四次？

*   **两次不够**: 无法确认 **客户端的接收能力**。服务器发出 `SYN+ACK` 后，无法知道客户端是否收到，可能造成资源浪费（服务器已分配资源，但客户端无响应）。也无法防止 **已失效的连接请求**（网络延迟导致旧的`SYN`请求先于新的到达）。
*   **三次正好**: 刚好能验证双方的收发能力，并可靠地同步序列号。
*   **四次多余**: 三次已经能建立可靠连接，四次没必要，浪费资源。

#### Q2: 第三次握手的ACK丢失了怎么办？

*   **服务器**: 在一定时间后没收到 `ACK`，会 **超时重传 `SYN+ACK`** 包。
*   **客户端**: 客户端认为连接已建立（`ESTABLISHED`状态），可能会开始发送数据。
*   **结果**:
    *   如果客户端的数据包先到达服务器，服务器会处理数据，连接建立。
    *   如果服务器的重传 `SYN+ACK` 先到达客户端，客户端会再次发送 `ACK`。
    *   连接最终能建立，但会有延迟。

#### Q3: 什么是 SYN Flood 攻击？如何防范？

*   **攻击原理**: 攻击者发送大量伪造IP地址的 `SYN` 包，服务器回应 `SYN+ACK` 后等待 `ACK`。由于源IP是伪造的，服务器永远等不到 `ACK`，导致大量连接处于 `SYN_RCVD` 状态，耗尽服务器资源，无法响应正常请求。
*   **防范措施**:
    *   **SYN Cookies**: 服务器不立即分配资源，而是用一个特殊的Cookie（基于IP、端口、时间戳等计算）来回应 `SYN`。收到客户端的 `ACK` 时，通过校验Cookie来恢复连接信息，再分配资源。
    *   **减少SYN_RCVD超时时间**: 让连接更快地释放。
    *   **限制SYN并发数**: 限制单位时间内同一IP的 `SYN` 请求数。
    *   **使用防火墙/负载均衡**: 过滤恶意流量。

#### Q4: 握手过程中协商了哪些主要参数？

*   **MSS (Maximum Segment Size)**: 最大报文段长度，通信双方能够接收的最大数据分段。
*   **Window Scale**: 窗口缩放因子，用于扩大TCP窗口大小，支持超过64KB的大窗口。
*   **SACK (Selective Acknowledgment)**: 选择性确认，允许接收方只确认收到的数据段，提高重传效率。
*   **Timestamp**: 时间戳，用于计算RTT(往返时间)和防止序列号回绕问题(PAWS)。
