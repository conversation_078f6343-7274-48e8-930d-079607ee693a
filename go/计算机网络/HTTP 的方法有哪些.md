## HTTP请求方法

### 常用HTTP方法对比

| 方法 | 用途 | 幂等性 | 安全性 | 请求体 | 缓存 |
|------|------|--------|--------|--------|------|
| **GET** | 获取资源 | ✓ | ✓ | 无 | 可缓存 |
| **POST** | 创建资源 | ✗ | ✗ | 有 | 不可缓存 |
| **PUT** | 更新/创建资源 | ✓ | ✗ | 有 | 不可缓存 |
| **DELETE** | 删除资源 | ✓ | ✗ | 无 | 不可缓存 |
| **HEAD** | 获取元数据 | ✓ | ✓ | 无 | 可缓存 |
| **OPTIONS** | 查询支持方法 | ✓ | ✓ | 无 | 可缓存 |
| **PATCH** | 部分更新 | ✗ | ✗ | 有 | 不可缓存 |

### 方法详解

#### GET - 获取资源
- **用途**：获取指定资源
- **特点**：参数在URL中，可缓存，幂等
- **限制**：URL长度限制，不能传递敏感信息

#### POST - 创建资源
- **用途**：创建新资源，提交表单
- **特点**：数据在请求体中，不可缓存，非幂等

#### PUT - 更新资源
- **用途**：完整更新资源，如果不存在则创建
- **特点**：幂等，替换整个资源

#### DELETE - 删除资源
- **用途**：删除指定资源
- **特点**：幂等，多次删除结果相同

#### PATCH - 部分更新
- **用途**：部分更新资源
- **特点**：只更新指定字段，非幂等

#### HEAD - 获取元数据
- **用途**：获取资源的元信息（响应头）
- **特点**：不返回响应体，用于检查资源是否存在

#### OPTIONS - 预检请求
- **用途**：查询服务器支持的方法，CORS预检
- **响应**：返回Allow头部列出支持的方法

### RESTful API设计

#### 资源操作映射
```
GET    /users          # 获取用户列表
GET    /users/123      # 获取特定用户
POST   /users          # 创建新用户
PUT    /users/123      # 更新用户（完整）
PATCH  /users/123      # 更新用户（部分）
DELETE /users/123      # 删除用户
```

#### 嵌套资源
```
GET    /users/123/posts     # 获取用户的文章
POST   /users/123/posts     # 为用户创建文章
DELETE /users/123/posts/456 # 删除用户的特定文章
```

### 幂等性和安全性

#### 幂等性
- **幂等**：多次执行相同操作，结果相同
- **GET、PUT、DELETE、HEAD、OPTIONS**：幂等
- **POST、PATCH**：非幂等

#### 安全性
- **安全**：不会修改服务器状态
- **GET、HEAD、OPTIONS**：安全
- **POST、PUT、DELETE、PATCH**：不安全

### 面试要点

**Q: GET和POST的区别？**
A:
1. **用途**：GET获取数据，POST提交数据
2. **参数位置**：GET在URL，POST在请求体
3. **缓存**：GET可缓存，POST不可缓存
4. **幂等性**：GET幂等，POST非幂等
5. **安全性**：GET安全，POST不安全

**Q: PUT和PATCH的区别？**
A:
- **PUT**：完整更新，替换整个资源，幂等
- **PATCH**：部分更新，只修改指定字段，非幂等

**Q: 什么是幂等性？为什么重要？**
A: 多次执行相同操作结果相同。重要性：
- 网络重试安全
- 分布式系统一致性
- 缓存策略制定

**Q: OPTIONS方法的作用？**
A:
- 查询服务器支持的HTTP方法
- CORS预检请求
- 检查服务器能力