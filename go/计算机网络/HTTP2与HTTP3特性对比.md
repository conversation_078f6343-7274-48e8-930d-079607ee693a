## HTTP/2 vs HTTP/3 面试核心

### 1. 核心差异速查表

| 特性 | HTTP/1.1 | HTTP/2 | HTTP/3 |
|:---|:---|:---|:---|
| **底层协议** | TCP | TCP | UDP (QUIC) |
| **多路复用** | ❌ | ✅ (单个流阻塞会影响所有流) | ✅ (流之间完全独立) |
| **队头阻塞** | 严重 (HTTP层) | 部分解决 (TCP层仍存在) | ✅ 彻底解决 |
| **头部压缩** | ❌ | HPACK | QPACK |
| **连接建立** | ~3-RTT | ~3-RTT | **1-RTT / 0-RTT** |
| **连接迁移** | ❌ | ❌ | ✅ |

---

### 2. HTTP/2: H1.1的加强版

HTTP/2 主要解决 HTTP/1.1 的**性能瓶颈**，核心是**多路复用**。

*   **✅ 优点:**
    *   **多路复用**: 单个TCP连接承载多个请求/响应流，打破浏览器6-8个连接限制，减少了TCP握手开销。
    *   **二进制分帧**: 解析效率更高，不易出错。
    *   **头部压缩 (HPACK)**: 维护动态字典，压缩请求/响应头，减少传输体积。
    *   **服务器推送**: 服务器可主动推送资源，降低延迟。

*   **❌ 弱点:**
    *   **TCP队头阻塞**: 底层TCP协议只有一个序列号，丢包需要等待重传，会阻塞该连接上的所有HTTP流。网络不稳定时，性能反而可能劣化。

---

### 3. HTTP/3: QUIC带来的革命

HTTP/3 抛弃 TCP，基于 **QUIC (Quick UDP Internet Connections)**，彻底解决队头阻塞。

*   **✅ 优点:**
    *   **基于QUIC**: 在UDP之上实现了可靠传输、拥塞控制、加密(TLS 1.3)，功能强大。
    *   **无队头阻塞**: QUIC的流各自独立，丢包只影响当前流。
    *   **快速握手**: 0-RTT 或 1-RTT 即可建立连接，延迟极低。
    *   **连接迁移**: 手机在Wi-Fi和4G/5G间切换时，IP地址改变，但QUIC连接可无缝迁移，不中断。

---

### 4. 面试快问快答

**Q: H2 相比 H1.1 最大的改进是什么？**
A: **多路复用**。它允许在单个TCP连接上并行处理多个请求，解决了HTTP/1.1的队头阻塞问题和连接数限制。

**Q: H3 为什么比 H2 更快？**
A:
1.  **解决了TCP队头阻塞**：H2的多路复用运行在单个TCP上，TCP丢包会阻塞所有流。H3基于QUIC(UDP)，一个流的丢包不影响其他流。
2.  **连接建立更快**：QUIC握手仅需0-RTT或1-RTT，而TCP+TLS需要约3-RTT。
3.  **连接迁移**：网络切换时，连接不中断，体验更好。

**Q: 什么是队头阻塞 (HOL Blocking)?**
A:
*   **HTTP/1.1层**: 请求必须按顺序发送和接收，前一个没完成，后面就得等。
*   **TCP层**: TCP协议要求数据按序到达。如果中间一个包丢了，接收方必须等待它重传，即使后面的包已经到了，也无法先处理，导致整个连接停顿。**这是H2的瓶颈**。

**Q: 如何选择使用哪个版本？**
A:
*   **HTTP/2**: 已经是Web标准，广泛支持，适用于绝大多数场景（网站、API）。Go标准库原生支持。
*   **HTTP/3**: 适用于对延迟和网络波动非常敏感的场景，如**视频直播、在线游戏、移动网络环境**。需要使用第三方库（如 `quic-go`）。
