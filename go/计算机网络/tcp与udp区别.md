## TCP vs UDP 速查

### 核心区别

| 特性 | TCP | UDP |
|---|---|---|
| **连接方式** | 面向连接 | 无连接 |
| **可靠性** | 可靠、有序 | 不可靠、无序 |
| **传输方式** | 字节流 | 数据报 |
| **速度** | 慢 | 快 |
| **开销** | 大（头部20B+，维护连接状态） | 小（头部8B，无连接状态） |
| **控制** | 有流量控制、拥塞控制 | 无 |

### 适用场景

| 场景 | 协议 |
|---|---|
| **要求可靠** (Web, 文件传输, 邮件) | TCP (HTTP, FTP, SMTP) |
| **要求高速** (直播, 游戏, VoIP) | UDP |
| **查询等** (域名解析, 动态IP) | UDP (DNS, DHCP) |

### TCP vs. UDP 核心面试点

#### 1. 核心对比与选型
- **最核心区别是什么?**
  - **TCP**: 面向连接、可靠、有序、慢、开销大。
  - **UDP**: 无连接、不可靠、无序、快、开销小。
- **如何选择?**
  - **可靠性优先**: 选TCP。如HTTP、FTP。
  - **实时性/速度优先**: 选UDP。如视频会议、游戏、DNS查询。
- **UDP如何实现可靠传输?**
  - 在应用层实现。通过引入序列号、确认应答(ACK)、超时重传等机制来模拟TCP的可靠性。例如KCP协议。

#### 2. TCP 连接管理
- **三次握手 (建连)**:
  - **目的**: 确认双方收发能力正常，同步序列号。
  - **为何三次**: 防止历史连接请求被错误初始化。
- **四次挥手 (断连)**:
  - **为何四次**: TCP是全双工，服务端收到客户端的`FIN`后，可能仍有数据要发送，所以`ACK`和`FIN`分开发送。
  - **TIME_WAIT作用**: 确保最后的`ACK`可靠送达，并防止旧连接的数据包干扰新连接。

#### 3. TCP 可靠性与流量控制
- **如何保证可靠?**: 序列号、确认应答、超时重传、数据校验。
- **流量控制 vs 拥塞控制**:
  - **流量控制 (滑动窗口)**: 点对点，接收方控制发送方，防止自身缓冲区溢出。
  - **拥塞控制 (慢启动等)**: 全局，发送方感知网络拥堵，避免拖垮整个网络。