# Cookie 与 Session 快速参考

## 核心区别：Cookie vs Session

| 特性 | Cookie | Session |
|---|---|---|
| **存储位置** | 客户端（浏览器） | 服务器端 |
| **安全性** | 较低，易被篡改 | 较高，数据在服务端 |
| **存储大小** | 小，约4KB | 较大，受服务器内存限制 |
| **服务器压力**| 无 | 有，需存储和管理 |
| **网络传输** | 每次HTTP请求都携带 | 仅传输Session ID（通常存在Cookie中）|
| **生命周期** | 可设置长期有效 | 随会话结束而销毁（可配置） |

## Cookie 详解

- **定义**: 服务器发送到浏览器并保存在本地的小块数据，用于跟踪用户状态。
- **关键属性**:
    - `Expires/Max-Age`: 控制过期时间。不设置则为会话Cookie，浏览器关闭即失效。
    - `HttpOnly`: 禁止JavaScript访问，有效防止XSS攻击窃取Cookie。
    - `Secure`: 仅在HTTPS连接下发送，防止中间人攻击。
    - `SameSite`: 控制跨站请求时Cookie的发送，有效防止CSRF攻击。
        - `Strict`: 完全禁止第三方Cookie。
        - `Lax`: 允许部分安全的跨站请求携带（如GET请求）。
        - `None`: 任何跨站请求都携带（必须同时设置`Secure`）。

## Session 详解

- **定义**: 服务器为客户端创建并维护的一个会话数据存储区。
- **工作原理**:
    1. **创建**: 用户首次访问，服务器创建Session对象并生成唯一的`Session ID`。
    2. **传递ID**: 服务器通过HTTP响应头（`Set-Cookie`）将`Session ID`发送给客户端。
    3. **客户端存储**: 浏览器将`Session ID`保存在Cookie中。
    4. **验证**: 后续请求中，浏览器自动携带`Session ID`，服务器根据ID查找对应Session数据。
- **优点**: 安全性高，因为敏感数据存储在服务器端。

## 分布式Session解决方案

当应用部署在多台服务器上时，需要确保不同服务器能共享Session信息。

1.  **Session粘滞（Sticky Session）**
    - **原理**: 负载均衡器通过策略（如IP Hash）将同一用户的请求始终转发到同一台服务器。
    - **优点**: 简单，无需改造应用。
    - **缺点**: 单点故障（服务器宕机则Session丢失），负载不均。

2.  **Session复制（Session Replication）**
    - **原理**: 在集群内所有服务器之间广播和同步Session数据。
    - **优点**: 高可用，一台服务器宕机不影响。
    - **缺点**: 网络开销大，同步延迟，只适合小规模集群。

3.  **Session集中存储（Centralized Storage）**
    - **原理**: 将Session统一存储在外部系统中，如Redis、Memcached。所有服务器都到该系统存取Session。
    - **优点**: 高可用、扩展性好、无单点问题（存储系统本身高可用）。**（业界主流方案）**
    - **缺点**: 增加了一次网络调用，依赖外部存储系统的稳定性。

4.  **基于Token的无状态方案（JWT）**
    - **原理**: 服务器不保存Session状态。用户信息加密后生成Token（如JWT）返回给客户端，客户端每次请求携带此Token。服务器解密Token验证身份。
    - **优点**: 无状态，扩展性极好，天然支持分布式。
    - **缺点**:
        - Token体积可能较大。
        - 无法主动使Token失效（只能等其过期）。
        - 安全性依赖于签名密钥的保密。

## 安全最佳实践

- **防XSS**: 设置Cookie的`HttpOnly`属性。
- **防CSRF**: 设置Cookie的`SameSite`属性，并使用CSRF Token。
- **防劫持**:
    - 全站使用HTTPS。
    - Cookie设置`Secure`属性。
    - 登录成功后，立即重新生成Session ID。
    - Session ID绑定到IP地址或User-Agent。
- **常规**:
    - 不在Cookie中存储敏感信息。
    - 为Session设置合理的超时时间。
    - 用户登出时主动销毁Session。
