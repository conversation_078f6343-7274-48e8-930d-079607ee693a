## HTTP缓存机制

### 缓存类型

#### 1. 强缓存
**不需要向服务器发送请求，直接从缓存中读取**

**Cache-Control**：
- `max-age=3600`：缓存3600秒
- `no-cache`：需要验证缓存
- `no-store`：不缓存任何内容
- `private`：只能被客户端缓存
- `public`：可以被代理服务器缓存

**Expires**：
- HTTP/1.0的缓存控制
- 指定过期时间：`Expires: Wed, 21 Oct 2025 07:28:00 GMT`
- 优先级低于Cache-Control

#### 2. 协商缓存
**需要向服务器验证缓存是否有效**

**Last-Modified / If-Modified-Since**：
- 基于文件修改时间
- 精度只能到秒级
- 可能存在文件修改但内容未变的情况

**ETag / If-None-Match**：
- 基于文件内容的唯一标识
- 精度更高，优先级高于Last-Modified
- 强ETag：`"123456"`，弱ETag：`W/"123456"`

### 缓存流程

```
1. 浏览器请求资源
2. 检查强缓存
   ├─ 命中且未过期 → 直接使用缓存 (200 from cache)
   └─ 未命中或过期 → 发送请求
3. 服务器检查协商缓存
   ├─ 资源未修改 → 返回304 Not Modified
   └─ 资源已修改 → 返回200和新资源
```

### 缓存策略

#### 不同资源的缓存策略

| 资源类型 | 缓存策略 | 示例 |
|----------|----------|------|
| **HTML** | 协商缓存 | `Cache-Control: no-cache` |
| **CSS/JS** | 强缓存+版本号 | `Cache-Control: max-age=31536000` |
| **图片** | 强缓存 | `Cache-Control: max-age=86400` |
| **API数据** | 短时间缓存或不缓存 | `Cache-Control: max-age=300` |

#### 缓存更新策略
1. **文件名哈希**：`app.abc123.js`
2. **查询参数**：`app.js?v=1.0.1`
3. **路径版本**：`/v1/app.js`

### 浏览器缓存位置

#### 缓存优先级（从高到低）
1. **Service Worker Cache**
2. **Memory Cache**：内存缓存，页面关闭即失效
3. **Disk Cache**：磁盘缓存，持久化存储
4. **Push Cache**：HTTP/2的推送缓存

### 实际应用

#### 服务端设置缓存头
- `Cache-Control: max-age=3600`：设置缓存时间
- `ETag: "123456"`：设置内容标识
- `Last-Modified`：设置修改时间

#### 客户端缓存控制
- `cache: 'no-cache'`：强制刷新，跳过缓存
- `cache: 'only-if-cached'`：只使用缓存

### 缓存问题与解决

| 问题 | 描述 | 解决方案 |
|------|------|----------|
| **缓存穿透** | 请求不存在的数据 | 布隆过滤器、空值缓存 |
| **缓存雪崩** | 大量缓存同时失效 | 随机过期时间、缓存预热 |
| **缓存击穿** | 热点数据缓存失效 | 互斥锁、永不过期 |

### 面试要点

**Q: 强缓存和协商缓存的区别？**
A: 
- **强缓存**：不发请求，直接使用缓存，返回200
- **协商缓存**：发请求验证，未修改返回304

**Q: ETag和Last-Modified哪个优先级高？**
A: ETag优先级更高，因为它基于内容而非时间，更准确

**Q: 如何解决缓存更新问题？**
A: 
1. 文件名加哈希值
2. 版本号控制
3. 设置合理的缓存时间

**Q: 什么是缓存穿透，如何解决？**
A: 
- **问题**：请求不存在的数据，绕过缓存直接查询数据库
- **解决**：布隆过滤器、空值缓存、参数校验

**Q: HTTP/1.1和HTTP/2的缓存有什么区别？**
A: HTTP/2增加了Server Push功能，可以主动推送资源到客户端缓存
