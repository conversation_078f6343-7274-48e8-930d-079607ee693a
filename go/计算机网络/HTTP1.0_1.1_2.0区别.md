# HTTP/1.0 vs 1.1 vs 2.0 核心区别 (面试速查)

## HTTP/1.0 vs HTTP/1.1

- **连接管理**:
  - **1.0**: 短连接，每个请求-响应都建立新的TCP连接，开销大。
  - **1.1**: 默认**持久连接 (Keep-Alive)**，TCP连接可复用。
- **管道化 (Pipelining)**:
  - **1.0**: 不支持。
  - **1.1**: 支持，但响应需按序返回，存在**队头阻塞 (Head-of-line blocking)**。
- **Host 头部**:
  - **1.0**: 不支持。
  - **1.1**: 强制要求，支持虚拟主机。
- **缓存机制**:
  - **1.0**: 依赖 `Last-Modified`, `Expires`。
  - **1.1**: 引入更强的 `ETag`, `Cache-Control` 等。
- **其他**:
  - **1.1** 新增了`PUT`, `DELETE`, `OPTIONS`等方法。

## HTTP/1.1 vs HTTP/2.0

- **多路复用 (Multiplexing)**:
  - **1.1**: 存在队头阻塞。
  - **2.0**: 在**单个TCP连接**上通过多路复用，并行处理多个请求和响应，**彻底解决队头阻塞**。
- **数据格式**:
  - **1.1**: **文本协议**，可读性好但解析复杂。
  - **2.0**: **二进制分帧 (Binary Framing)**，将消息分解为帧，解析高效。
- **头部压缩 (Header Compression)**:
  - **1.1**: 头部信息是未经压缩的纯文本，存在冗余。
  - **2.0**: 使用 **HPACK** 算法压缩头部，减少请求大小。
- **服务器推送 (Server Push)**:
  - **1.1**: 不支持。
  - **2.0**: 服务器可以**主动推送**资源给客户端，减少延迟。

## 核心特性对比

| 特性 | HTTP/1.0 | HTTP/1.1 | HTTP/2.0 |
|------|----------|----------|----------|
| **连接方式** | 短连接 | 持久连接 | 多路复用 |
| **队头阻塞** | 严重 | 存在 | 解决 |
| **数据格式** | 文本 | 文本 | 二进制 |
| **头部压缩** | 无 | 无 | HPACK |
| **服务器推送** | 无 | 无 | 支持 |

## 面试核心问答

**Q: HTTP/2.0 如何解决队头阻塞？**
A: 通过**多路复用**。HTTP/2 将所有请求和响应数据包分割成更小的帧，并为它们打上流ID。这使得属于不同流的帧可以在同一个TCP连接上交错传输，一个流的阻塞不会影响其他流。

**Q: 什么是服务器推送 (Server Push)？**
A: 服务器可以预测客户端需要的资源，并在客户端请求之前主动将这些资源发送给客户端。这可以减少请求的往返次数，加快页面加载速度。例如，在发送`index.html`后，服务器可以立即推送`style.css`和`script.js`。

**Q: 为什么 HTTP/2.0 通常要求 HTTPS？**
A: 这不是协议本身的强制要求，而是主流浏览器（如Chrome, Firefox）的实现要求。为了推动Web加密，它们只为`https://`网站启用HTTP/2。技术上HTTP/2可以运行在HTTP上（h2c），但`https://`上的HTTP/2（h2）是主流。
