## HTTP协议结构

### HTTP消息格式

HTTP协议基于请求-响应模式，包含请求消息和响应消息两种格式。

#### HTTP请求结构
**组成部分**：请求行 + 请求头部 + 空行 + 请求体（可选）

**1. 请求行**：`方法 URI HTTP版本`
- 示例：`GET /api/users HTTP/1.1`

**2. 请求头部**：键值对形式的元数据
- Host: www.example.com
- User-Agent: Mozilla/5.0
- Content-Type: application/json

**3. 请求体**：POST/PUT等方法的数据内容

#### HTTP响应结构
**组成部分**：状态行 + 响应头部 + 空行 + 响应体（可选）

**1. 状态行**：`HTTP版本 状态码 状态描述`
- 示例：`HTTP/1.1 200 OK`

**2. 响应头部**：服务器返回的元数据
- Content-Type: application/json
- Content-Length: 456
- Server: nginx/1.18.0

**3. 响应体**：实际返回的数据内容

### 常用请求头

| 头部字段 | 作用 | 示例 |
|----------|------|------|
| **Host** | 指定服务器域名 | Host: www.example.com |
| **User-Agent** | 客户端信息 | User-Agent: Chrome/91.0 |
| **Accept** | 可接受的内容类型 | Accept: application/json |
| **Content-Type** | 请求体内容类型 | Content-Type: application/json |
| **Authorization** | 认证信息 | Authorization: Bearer token |
| **Cookie** | 会话信息 | Cookie: sessionid=abc123 |

### 常用响应头

| 头部字段 | 作用 | 示例 |
|----------|------|------|
| **Content-Type** | 响应体内容类型 | Content-Type: text/html |
| **Content-Length** | 响应体长度 | Content-Length: 1024 |
| **Set-Cookie** | 设置Cookie | Set-Cookie: id=123; Path=/ |
| **Cache-Control** | 缓存控制 | Cache-Control: no-cache |
| **Location** | 重定向地址 | Location: /new-url |

### 面试要点

**Q: HTTP请求和响应的基本结构？**
A:
- **请求**：请求行 + 请求头 + 空行 + 请求体
- **响应**：状态行 + 响应头 + 空行 + 响应体

**Q: GET和POST请求的区别？**
A:
- **GET**：无请求体，参数在URL中，幂等，可缓存
- **POST**：有请求体，参数在body中，非幂等，不可缓存

**Q: 常见的Content-Type有哪些？**
A:
- `application/json` - JSON数据
- `application/x-www-form-urlencoded` - 表单数据
- `multipart/form-data` - 文件上传
- `text/html` - HTML文档