### HTTP/3 与 QUIC 核心精简版

**QUIC (Quick UDP Internet Connections): 基于UDP的新一代传输协议**

- **目标**: 解决HTTP/2在TCP上的延迟和队头阻塞问题。
- **核心优势**:
    - **0-RTT 快速握手**: 首次连接几乎无延迟，客户端第一个包就可以带上应用数据。
    - **无队头阻塞 (No Head-of-Line Blocking)**: 多个流并行传输，一个流的丢包(网络抖动)不影响其他流。这是相对于TCP的最大改进之一。
    - **连接迁移**: 切换网络（如Wi-Fi到4G）时，连接不中断。IP变了，连接依旧有效。
    - **内置TLS 1.3加密**: 传输层原生支持加密，更安全、更高效。

**为什么QUIC选择UDP？**

- **灵活性高**: UDP像一张"白纸"，没有TCP复杂的连接管理、拥塞控制。QUIC可以在应用层根据需要自定义这些机制，实现更优化的控制，绕开内核对TCP的实现。
- **天然无队头阻塞**: UDP本身是数据包级别的，没有顺序保证，为QUIC实现流的独立性提供了基础。

**HTTP/3: 运行在QUIC上的HTTP**

- **一句话概括**: HTTP/3 = HTTP/2的应用层语义 + QUIC的传输层。
- **关键改进**: 继承QUIC所有优点，尤其在弱网、移动环境下，显著减少延迟，提升加载速度。

---

### 面试核心摘要

- **HTTP/3为什么快？**
  > 因为它的底层传输协议是QUIC。

- **QUIC为什么快？**
  > 1.  **0-RTT**快速建连；
  > 2.  基于UDP实现了多路复用，解决了TCP的**队头阻塞**问题；
  > 3.  有**连接迁移**特性，网络切换时连接不中断。

- **为什么QUIC要基于UDP而不是TCP？**
  > 因为TCP在内核中实现，优化困难，且有固有的队头阻塞问题。UDP更灵活，像一张白纸，允许QUIC在应用层实现一套更高效、更可控的传输机制。