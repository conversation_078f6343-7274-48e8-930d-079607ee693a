## TCP滑动窗口与流量控制（面试精简版）

### 1. 核心概念

-   **滑动窗口**: TCP实现**流量控制**和**可靠传输**的核心机制。允许发送方在未收到确认前，连续发送多个数据包，以提高传输效率。
-   **窗口分类**:
    -   **发送窗口**: 发送方可以发送但尚未收到确认的数据范围。
    -   **接收窗口 (rwnd)**: 接收方能接收的数据范围，用于**流量控制**。
    -   **拥塞窗口 (cwnd)**: 发送方根据网络拥塞状况计算出的窗口大小，用于**拥塞控制**。
-   **实际发送窗口**: `EffectiveWindow = min(rwnd, cwnd)`

---

### 2. 流量控制 (Flow Control)

-   **目的**: 防止发送方发送速度过快，导致接收方缓冲区溢出。
-   **实现**: 接收方通过TCP头部的`Window`字段，告知发送方自己的接收窗口 `rwnd` 大小。
-   **零窗口问题**:
    -   **现象**: 接收方缓冲区满，通告窗口为0，发送方停止发送数据。
    -   **解决**: 发送方启动**持续计时器(Persistence Timer)**，超时后发送**窗口探测报文(Window Probe)**（通常为1字节），强制接收方回复ACK并更新窗口大小。

---

### 3. 可靠传输 (Reliable Transmission)

-   **超时重传 (RTO)**: 发送数据后启动计时器，在规定时间 `RTO` 内未收到ACK，则重传数据。
-   **快速重传**: 发送方连续收到**3个重复的ACK (Duplicate ACK)**，认为该序号的数据包已丢失，立即重传，无需等待RTO超时。
-   **选择性确认 (SACK)**: 对乱序到达的数据进行确认，使发送方仅重传真正丢失的数据段，提高重传效率。

---

### 4. 常见问题

-   **糊涂窗口综合症 (Silly Window Syndrome)**:
    -   **现象**: 收发双方一次只传输少量数据（如1字节），导致网络效率极低。
    -   **发送方解决 (Nagle算法)**: 数据未达到一定量（如MSS）或未收到上个包的ACK时，先缓存不发送。
    -   **接收方解决 (延迟ACK)**: 等待缓冲区有足够空间，或应用层读取数据后，再发送窗口更新。
-   **窗口缩放选项 (Window Scaling)**:
    -   **背景**: TCP头部窗口字段为16位，最大`65535`字节，在高带宽延迟（长肥）网络中成为瓶颈。
    -   **解决**: 握手时协商`Window Scale`缩放因子，`实际窗口 = 头部窗口值 << 缩放因子`。

---

### 5. 面试要点 (Quick Reference)

**Q: 滑动窗口的作用？**
A:
1.  **流量控制**: 匹配收发两端速度，防止接收方缓存被占满。
2.  **可靠传输**: 配合序列号和确认机制，保证数据有序、不丢失。
3.  **提高效率**: 实现批量发送和确认，无需对每个包都单独等待ACK。

**Q: 流量控制 vs 拥塞控制？**
A:
-   **流量控制**: **端到端**问题，关心**接收方**的处理能力。由**接收窗口 (rwnd)** 控制。
-   **拥塞控制**: **整个网络**的问题，关心**网络**的承载能力。由**拥塞窗口 (cwnd)** 控制。
-   **关系**: 发送方的实际发送窗口取两者的**最小值**。

**Q: TCP如何保证可靠传输？**
A:
1.  **序列号(Seq)**: 保证数据有序性，解决乱序问题。
2.  **确认应答(ACK)**: 告知数据已成功接收。
3.  **校验和(Checksum)**: 检查数据在传输过程中是否损坏。
4.  **重传机制**: **超时重传(RTO)** + **快速重传(3个DupACK)**。
5.  **流量控制(滑动窗口)**: 防止接收方过载。
6.  **拥塞控制**: 防止网络过载。

**Q: 快速重传的触发条件？**
A: 发送方连续收到**3个或以上**的重复ACK (Duplicate ACK)。
