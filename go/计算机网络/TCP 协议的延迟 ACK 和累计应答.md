### 延迟 ACK (Delayed Acknowledgment)

- **核心思想**: 收到数据后不立即发送ACK，而是等待一小段时间（如40ms）。
    - **目的**: 看能否将ACK与传给对方的数据包一起发出（捎带应答/Piggybacking），或者将多个ACK合并成一个，从而减少网络中的ACK包数量。
- **优点**: 减少ACK包的数量，降低网络开销和CPU处理开销。
- **缺点**: ACK的延迟可能导致发送方等待超时，触发不必要的重传，尤其是在交互式应用（如Telnet）或数据流末尾。

### 累计应答 (Cumulative Acknowledgment)

- **核心思想**: TCP的ACK确认的是**按序收到的最后一个字节的序列号+1**。意思是"这个序号之前的数据我都收到了，现在我期望收到这个序号的数据"。
    - **举例**: 接收方收到了序列号为1-1000和2001-3000的数据，它会发送ACK 1001，表示1001之前的数据都已收到。
- **优点**:
    - **高效**: 一个ACK可以确认多个数据包。
    - **容忍ACK丢失**: 即使中间的ACK丢失，只要后续的ACK到达，就能确认之前所有的数据。
- **缺点**:
    - **信息有限**: 发生丢包时，发送方只知道从哪个序列号开始丢失了，但不知道后面哪些数据包被成功接收。这可能导致不必要的重传（如Go-Back-N策略）。（SACK可以解决这个问题）