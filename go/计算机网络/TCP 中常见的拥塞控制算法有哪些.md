## TCP拥塞控制核心

### 四大基础机制
1.  **慢启动 (Slow Start)**
    *   **目标**: 在启动阶段快速探测网络可用带宽。
    *   **核心思想**: 连接刚建立时，网络状况未知，通过指数级增窗，快速找到网络容量的上限。
    *   **行为**: `cwnd` 指数级增长 (每收到1个ACK, `cwnd`+1)。
    *   **退出**: `cwnd` 达到 `ssthresh` (慢启动阈值)。

2.  **拥塞避免 (Congestion Avoidance)**
    *   **目标**: 探测到网络接近拥塞时，转为慢速增窗，避免过载。
    *   **核心思想**: 当`cwnd`达到阈值，说明网络可能接近饱和，需要"小心翼翼"地增加窗口，防止造成拥塞。
    *   **行为**: `cwnd` 线性增长 (每1个RTT, `cwnd`+1 MSS)。
    *   **进入**: `cwnd` >= `ssthresh`。

3.  **快速重传 (Fast Retransmit)**
    *   **目标**: 针对性地快速响应单个丢包。
    *   **核心思想**: 收到3个重复的ACK，意味着发送方发出的某个包对方没收到，但它之后的3个包都顺利到达了。这强烈暗示了丢包，而非网络瘫痪。
    *   **触发**: 收到3个或以上重复的ACK。
    *   **优势**: 无需等待RTO（重传超时），大大减少因丢包导致的延迟。

4.  **快速恢复 (Fast Recovery)**
    *   **目标**: 在丢包后，避免因`cwnd`降为1而导致的性能骤降。
    *   **核心思想**: 既然还能收到重复ACK，说明网络连接还在，只是丢了包。没必要像超时一样把速度降到最低。
    *   **行为**: `ssthresh` 减半, `cwnd` 设置为新的 `ssthresh`（或减半），然后进入拥塞避免。
    *   **触发**: 快速重传后。

### 主流拥塞控制算法
*   **Reno**: **经典四合一**（慢启动、拥塞避免、快重传、快恢复），但**高丢包时性能差**。
*   **NewReno**: **Reno的改进版**，能更高效处理**多重丢包**场景。
*   **Vegas**: **基于延迟**的算法，通过监控RTT变化来**主动预测和避免拥塞**。
*   **CUBIC**: **Linux默认**，使用**三次函数**增窗，在**高带宽、高延迟**网络中表现优异。
*   **BBR**: Google出品，**不依赖丢包**，通过实时估算**瓶颈带宽和RTT** (`BDP`)来控制发包，**最大化吞吐并最小化延迟**。
*   **Compound TCP**: **混合算法**（丢包+延迟），适用于**高BDP网络**。

### 面试速记
*   **慢启动 vs 拥塞避免?**
    *   慢启动：**指数增长**，暴力探测。
    *   拥塞避免：**线性增长**，精细控制。

*   **快速重传触发条件?**
    *   收到**3个重复ACK**。因为它说明后续包已到达，强烈暗示中间有包丢失。

*   **BBR的优势?**
    *   **不依赖丢包**作拥塞信号，在有一定丢包率的网络中仍能跑满带宽，延迟更低。

*   **算法如何选型?**
    *   **高带宽长延迟**: CUBIC, BBR
    *   **低延迟要求**: Vegas, BBR
    *   **通用场景**: Reno, NewReno (但现在CUBIC更普遍)