## HTTP请求过程核心步骤

1.  **DNS解析**: 域名 -> IP (浏览器缓存 -> 系统缓存 -> ... -> 根域名服务器)
2.  **TCP连接**: 三次握手 (SYN, SYN+ACK, ACK) 建立连接。
3.  **发送HTTP请求**: 客户端构建并发送请求报文。
4.  **服务器处理**: 服务器解析请求，执行业务逻辑。
5.  **返回HTTP响应**: 服务器构建并返回响应报文。
6.  **浏览器渲染**: 解析HTML/CSS，构建DOM/CSSOM，渲染页面。
7.  **TCP连接管理**:
    - **HTTP/1.0**: 请求后立即关闭。
    - **HTTP/1.1**: 默认长连接 (Keep-Alive)。
    - **HTTP/2**: 多路复用，单连接处理并发请求。

### 关键面试题

**Q: 为什么需要三次握手？**
A: 确保双方收发能力正常，并防止已失效的连接请求被服务端接收，造成资源浪费。

**Q: DNS解析过程？**
A: 顺序：浏览器缓存 → 系统缓存 → 路由器缓存 → ISP DNS → 根域名服务器 → ... 递归查询。

**Q: HTTP/1.1的优化？**
A: Keep-Alive长连接、管道化、缓存控制、分块传输编码。