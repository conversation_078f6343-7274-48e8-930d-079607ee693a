# TCP 可靠传输速查

### Q: TCP如何保证可靠传输？

A: 主要通过**五大核心机制**来保证：

1.  **确认应答 (ACK)**
    *   **作用**: 确认数据已送达。
    *   **机制**: 接收方回送ACK，发送方超时未收到则**重传**。超时时间 (RTO) 根据网络延迟 (RTT) 动态调整。

2.  **序列号 (Sequence Number)**
    *   **作用**: 保证数据包的**顺序性**和**完整性**。
    *   **机制**: 为每个字节分配序列号，接收方根据序列号重组数据，并用确认号 (ACK Number) 告知期望的下一字节。

3.  **流量控制 (Flow Control)**
    *   **作用**: 防止发送方速度过快，耗尽接收方缓冲区。
    *   **机制**: 使用**滑动窗口 (Sliding Window)**，接收方在TCP头部通告自己的接收窗口大小，发送方据此调整发送速率。

4.  **拥塞控制 (Congestion Control)**
    *   **作用**: 防止过多数据注入网络导致拥塞。
    *   **机制**: 发送方维护一个**拥塞窗口 (cwnd)**，通过以下算法动态调整：
        *   **慢启动**: 连接建立初期，窗口指数级增长。
        *   **拥塞避免**: 窗口大小达到阈值后，线性增长。
        *   **快速重传**: 收到3个重复ACK时，立即重传丢失报文，不等待超时。
        *   **快速恢复**: 快速重传后，窗口减半，然后线性增长，避免网络雪崩。

5.  **校验和 (Checksum)**
    *   **作用**: 检测数据在传输过程中是否出错。
    *   **机制**: 发送方和接收方计算TCP头部和数据的校验和，不一致则丢弃。

### Q: 流量控制和拥塞控制有什么区别？

| 对比项 | 流量控制 | 拥塞控制 |
|---|---|---|
| **目的** | 防止**接收方**被压垮 | 防止**网络**拥塞 |
| **控制方** | 接收方 | 发送方 |
| **窗口** | 接收窗口 (rwnd) | 拥塞窗口 (cwnd) |
| **衡量标准**| 接收方处理能力 | 网络状况 (丢包、延迟) |

### Q: 超时重传时间 (RTO) 如何确定？

A: **动态计算**。基于**往返时间 (RTT)** 及其偏差，经典算法为 `RTO = RTT + 4 * RTT偏差`。