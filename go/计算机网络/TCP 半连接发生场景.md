# TCP 半连接 (Half-Open Connection) 速查

## 核心概念

- **状态**: `SYN_RCVD`
- **定义**: 三次握手中，服务器发送 `SYN+ACK` 后，等待客户端 `ACK` 的中间状态。
- **本质**: 连接尚未完全建立，但服务器已为此分配部分资源。

## 主要产生场景

1.  **网络延迟**: 客户端 `ACK` 包在网络中延迟。
    - **特点**: 短暂状态，最终能成功建立连接。
2.  **客户端异常**: 客户端崩溃、断网，无法发送 `ACK`。
    - **后果**: 服务器超时后，重试几次（`tcp_synack_retries`）便会释放资源。
3.  **网络故障**: 中间路由、防火墙丢弃了客户端的 `ACK` 包。
    - **后果**: 连接建立失败。
4.  **SYN Flood 攻击**: 攻击者伪造源 IP 发送大量 `SYN` 包，但不响应 `SYN+ACK`。
    - **危害**: 占满服务器的半连接队列（`tcp_max_syn_backlog`），使其无法处理正常请求，导致服务拒绝（DoS）。

## 防护策略 (针对 SYN Flood)

1.  **SYN Cookies (首选)**
    - **原理**: 不在半连接队列中存储信息，而是通过一个特殊的 `cookie`（编码在 `SYN+ACK` 的序列号中）来验证客户端的 `ACK`。只有收到有效的 `ACK` 后才分配资源。
    - **开启**: `net.ipv4.tcp_syncookies = 1`
2.  **调整内核参数**
    - **增大半连接队列**: `net.ipv4.tcp_max_syn_backlog` (e.g., `2048` or higher)
        - 作用：容纳更多半连接，提高正常用户的连接成功率，但会消耗更多内存。
    - **减少重试次数**: `net.ipv4.tcp_synack_retries` (e.g., `1` or `2`)
        - 作用：让服务器更快地释放无效的半连接资源。
3.  **应用层/网络设备防护**
    - **限制单 IP 请求频率**: 使用防火墙、WAF 或应用逻辑。
    - **IP 黑白名单**: 封禁恶意 IP。

## 监控与诊断

- **查看半连接数量**:
  ```bash
  # SYN_RECV 状态的连接数
  ss -n state syn-recv | wc -l
  netstat -an | grep SYN_RECV | wc -l
  ```
- **关键指标**:
  - `SYN_RECV` 数量激增。
  - `/proc/net/netstat` 中 `syncookies_sent`, `syncookies_recv`, `syncookies_failed` 指标变化。
  - `TcpExtListenOverflows` 和 `TcpExtListenDrops` 计数器增长，表示半连接队列或全连接队列溢出。

## 面试核心回答

- **是什么?**
  - TCP 半连接是三次握手时，Server 发了 `SYN+ACK` 等待 Client `ACK` 的状态 (`SYN_RCVD`)。
- **为什么会产生?**
  - **正常**: 网络延迟、客户端断开。
  - **异常**: 网络故障、SYN Flood 攻击。
- **危害? (主要指 SYN Flood)**
  - 耗尽服务器半连接队列，导致无法响应正常请求，造成 DoS。
- **如何防护?**
  - **核心**: 开启 SYN Cookies (`tcp_syncookies=1`)，不预分配资源，用 Cookie 验证。
  - **辅助**: 增大半连接队列 (`tcp_max_syn_backlog`)，减少 `SYN+ACK` 重试次数 (`tcp_synack_retries`)。
  - **其他**: 防火墙限流、IP 黑名单。
- **如何排查?**
  - `ss` 或 `netstat` 看 `SYN_RECV` 连接数是否暴增。
  - `cat /proc/net/netstat` 看内核 TCP 统计，如 `ListenDrops` 是否增加。