## HTTPS 握手核心

### TLS 1.2 握手 (2-RTT)
1.  **ClientHello**: 客户端发送支持的TLS版本、加密套件和随机数 `C_Random`。
2.  **ServerHello & Certificate**: 服务端选择套件，并发送证书和随机数 `S_Random`。
3.  **客户端验证与密钥交换**:
    *   客户端验证证书有效性。
    *   生成`预主密钥(Pre-Master Secret)`，用服务器公钥加密后发送。
    *   通知服务端后续将加密通信。
4.  **服务端完成握手**:
    *   用私钥解密获取`预主密钥`。
    *   双方根据 `C_Random` + `S_Random` + `Pre-Master` 生成对称的`会话密钥`。
    *   服务端通知客户端后续将加密通信，握手完成。

### TLS 1.3 握手 (1-RTT)
- **核心优势**: 1-RTT完成握手，更快、更安全。
- **流程**:
    1.  **ClientHello**: 客户端直接发送支持的加密套件、密钥交换参数。
    2.  **ServerHello**: 服务端生成会话密钥，直接发送加密后的证书和 `Finished` 消息。
- **亮点**:
    - **性能**: 1-RTT 握手，支持 0-RTT 会话恢复。
    - **安全**: 移除过时加密算法，强制前向保密 (Perfect Forward Secrecy)。

### 证书验证
- **验证内容**:
    1.  **合法性**: 域名是否匹配、是否在有效期内、签名是否有效。
    2.  **吊销状态**: 检查证书是否已被吊销 (通过 CRL / OCSP)。
    3.  **信任链**: 验证证书是否由客户端信任的根CA签发 (服务器证书 ← 中级CA ← 根CA)。

### 性能优化
- **会话复用**:
    - **Session ID**: 服务端缓存会话状态，客户端请求时携带ID以恢复。
    - **Session Ticket**: 服务端将会话信息加密成Ticket发给客户端保存，减少服务端存储压力。
- **TLS 1.3 0-RTT**: 在会话恢复时，客户端在第一个包中就能发送加密的应用数据。
- **OCSP Stapling**: 服务端主动获取并附上证书状态，避免客户端再去查询，降低延迟。

### 面试速记
**Q: HTTPS握手关键步骤?**
A: 协商加密参数 -> 服务端发证书供验证 -> 客户端交换密钥材料 -> 双方生成会话密钥 -> 开始加密通信。

**Q: 如何优化HTTPS性能?**
A: 使用TLS 1.3 (1-RTT/0-RTT)、会话复用 (Session ID/Ticket)、OCSP Stapling、HTTP/2。

**Q: TLS 1.2 和 1.3 的主要区别?**
A: **1.2** 是2-RTT，协商和密钥交换分开。**1.3** 是1-RTT，将多个步骤合并，更快、更安全，并强制前向保密。

**Q: 证书验证都验证什么?**
A: 验证证书本身（域名、有效期、签名、是否被吊销）和证书的信任链（是否由受信任的根CA签发）。
