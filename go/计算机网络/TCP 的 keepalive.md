# TCP Keepalive vs HTTP Keep-Alive 速查

## 核心对比

| 特性 | TCP Keepalive | HTTP Keep-Alive |
| :--- | :--- | :--- |
| **层级** | 传输层 (TCP) | 应用层 (HTTP) |
| **目的** | **连接保活**：检测连接是否死亡，防止资源泄露。 | **连接复用**：减少TCP握手开销，提升性能。 |
| **机制** | **探测机制**：空闲时发送探测包，检查对端响应。 | **协议约定**：HTTP头部 `Connection: keep-alive`。 |
| **感知方** | 操作系统内核 | HTTP应用程序 (客户端/服务端) |

---

## TCP Keepalive 详解

- **作用**: 检测长时间空闲的TCP连接是否仍然有效。
- **场景**: 数据库连接池、SSH、WebSocket等长连接服务。
- **工作流程**:
    1.  **空闲计时**: 连接空闲超过 `tcp_keepalive_time` (默认2小时)。
    2.  **发送探针**: 发送TCP Keepalive探针报文。
    3.  **响应或重试**:
        -   **收到ACK**: 连接有效，重置计时器。
        -   **未收到ACK**: 每隔 `tcp_keepalive_intvl` (默认75秒) 重试。
    4.  **判定失败**: 连续失败 `tcp_keepalive_probes` (默认9次) 后，内核认为连接失效，通知应用。
- **优点**: 自动清理失效连接，对应用透明。
- **缺点**: 产生额外网络流量；网络抖动可能导致误判。

---

## HTTP Keep-Alive (持久连接) 详解

- **作用**: 在一个TCP连接上发送多个HTTP请求和响应，而不是每个请求都新建连接。
- **场景**: 绝大多数Web应用，API调用等。
- **工作流程**:
    1.  **开启**: HTTP/1.1 默认开启。客户端请求头包含 `Connection: keep-alive`。
    2.  **复用**: 服务端处理完一个请求后，不关闭TCP连接，等待下一个请求。
    3.  **关闭**:
        -   某一方在响应/请求头中包含 `Connection: close`。
        -   达到超时时间 (如Nginx的 `keepalive_timeout`) 或最大请求数。
- **优点**: 显著降低延迟，减少服务器和客户端的CPU和内存消耗。
- **缺点**: 对服务器而言，保持连接会消耗更多内存资源，尤其是在高并发场景下。

---

## 面试核心Q&A

**Q1: TCP Keepalive 和 HTTP Keep-Alive 有什么区别？**
A: **层级不同**：TCP在传输层，HTTP在应用层。**目的不同**：TCP用于检测连接死活，HTTP用于复用连接提升性能。**实现方式不同**：TCP是内核级的探针机制，HTTP是应用层的协议约定。

**Q2: 为什么需要 TCP Keepalive？**
A: 防止因客户端/服务端异常宕机、网络中断等原因导致的 "死连接" 长期占用系统资源。它是一种连接健康状态的巡检机制。

**Q3: TCP Keepalive 的探测过程是怎样的？**
A: 连接空闲超过阈值后，一端发送探测包。如果对方响应ACK，则连接正常。如果多次发送探测包（有重试间隔）都无响应，则认为连接已断开。