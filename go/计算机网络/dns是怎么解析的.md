# DNS 快速参考

## 核心解析流程

1.  **客户端缓存查询**: 浏览器缓存 → 系统缓存 (`/etc/hosts`) → 路由器缓存。
2.  **ISP DNS 缓存查询**: 查询 ISP提供的 DNS 服务器。
3.  **递归查询与迭代查询**:
    *   **本地 DNS (LDNS)** 发起**递归查询**。
    *   各级 DNS 服务器之间进行**迭代查询**:
        *   **根 DNS 服务器 (`.`)**: 返回顶级域 (TLD) DNS 服务器地址 (如 `.com`)。
        *   **TLD DNS 服务器 (`.com`)**: 返回权威 DNS 服务器地址 (如 `example.com`)。
        *   **权威 DNS 服务器 (`example.com`)**: 返回目标 IP 地址。
4.  **结果返回与缓存**: 本地 DNS 将结果返回给客户端并缓存。

---

## 关键概念

### 查询类型

*   **递归查询 (Recursive)**: 客户端发起，DNS 服务器代为完整查询并返回**最终结果**。
*   **迭代查询 (Iterative)**: DNS 服务器之间发生，返回**下一步要查询的服务器地址**。

### DNS 记录类型

| 类型 | 作用 | 示例 |
|---|---|---|
| **A** | 域名 → IPv4 | `example.com` → `*************` |
| **AAAA** | 域名 → IPv6 | `example.com` → `2606:2800:...` |
| **CNAME**| 别名 | `www.example.com` → `example.com` |
| **MX** | 邮件服务器 | `example.com` → `mail.example.com` |
| **NS** | 域名服务器 | `example.com` → `ns1.example.com` |

---

## 性能与安全

### 性能优化

*   **合理设置 TTL**:
    *   **稳定记录**: 3600s (1小时) 或更长。
    *   **频繁变更/CDN**: 60s - 300s。
*   **DNS 预解析**: 浏览器提前解析页面中可能用到的域名 `<link rel="dns-prefetch" href="//host.com">`。
*   **使用 CDN**: 将用户请求导向最近的节点，降低解析和访问延迟。
*   **选择高性能 DNS**: 如 `*******` (Cloudflare), `*******` (Google)。

### DNS 安全

| 威胁 | 防护措施 |
|---|---|
| **DNS 劫持/投毒** | **DNSSEC**: 验证 DNS 响应的真实性和完整性。 |
| **DNS 窃听** | **DoH/DoT**: 加密 DNS 查询流量。 |
| **放大攻击** | **限制递归范围**: 仅为授权客户端提供递归查询。 |

---

## 面试核心问题

**Q: DNS 解析的完整过程？**
A: 先查各级缓存 (浏览器 → 系统 → 路由器 → ISP)。若未命中，本地 DNS 服务器开始递归查询：从根服务器开始，通过迭代查询找到顶级域服务器，再到权威服务器，最终获取 IP 地址并层层返回、缓存。

**Q: 递归和迭代查询的区别？**
A: **递归**是客户端到本地DNS的请求方式，要求返回最终答案。**迭代**是DNS服务器之间的请求方式，只返回下一步线索。

**Q: 如何优化 DNS 解析？**
A: 1. 合理设置 TTL； 2. DNS 预解析； 3. 使用 CDN； 4. 选择高性能 DNS 服务。

**Q: DNSSEC 是什么？**
A: 一套安全扩展，通过数字签名确保 DNS 记录未被篡改，防止 DNS 劫持和投毒。