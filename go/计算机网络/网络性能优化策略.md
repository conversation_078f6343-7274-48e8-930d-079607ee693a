## 网络性能优化速查

### 优化核心
- **低延迟**：减少请求响应时间
- **高吞吐**：提升数据传输效率
- **优体验**：加速页面加载
- **低成本**：节省带宽与资源

---

### 前端优化

#### 1. 资源优化
- **压缩**:
  - **文件**: Gzip/Brotli
  - **代码**: JS/CSS压缩混淆
  - **图片**: WebP格式、压缩、懒加载、雪碧图
- **代码**:
  - 代码分割 (Code Splitting)
  - Tree Shaking (移除死代码)
  - 资源合并 (减少请求数)

#### 2. 缓存策略
- **浏览器缓存**:
  - **强缓存**: `Cache-Control`, `Expires`
  - **协商缓存**: `ETag`, `Last-Modified`
- **缓存体系**: 浏览器 → CDN → 代理 → 源站

#### 3. 请求优化
- **减少请求**: 资源合并、减少重定向
- **加速请求**: DNS预解析、预加载 (`preload`/`prefetch`)、并行请求

---

### 协议层优化

#### 1. HTTP/2
- **多路复用**: 单TCP连接并发请求，消除队头阻塞
- **头部压缩 (HPACK)**: 减少请求头大小
- **服务器推送**: 主动推送关键资源
- **二进制分帧**: 解析效率高

#### 2. HTTP/3 (QUIC)
- **核心**: 基于UDP，更快连接建立 (0/1-RTT)
- **优势**: 解决了TCP队头阻塞、连接迁移（网络切换不中断）

#### 3. 连接优化
- **Keep-Alive**: 复用TCP连接，减少握手开销
- **连接池**: 客户端预建连接，控制并发

---

### 服务端优化

#### 1. 负载均衡
- **算法**: 轮询、最少连接、IP哈希、加权
- **健康检查**: 自动摘除故障节点

#### 2. 数据库优化
- **查询**: 索引优化、SQL调优、慢查询分析
- **架构**: 读写分离、分库分表、连接池
- **缓存**: 集成Redis/Memcached，防缓存穿透/雪崩

#### 3. 异步处理
- **消息队列 (Kafka/RocketMQ)**: 异步任务、削峰填谷、系统解耦

---

### 网络层优化

#### 1. CDN (内容分发网络)
- **作用**: 边缘节点缓存，实现就近访问、加速静态资源
- **策略**: 动静分离，为不同资源设置不同缓存TTL

#### 2. DNS优化
- **加速**: DNS预解析、HTTPDNS（避免Local DNS劫持和延迟）
- **负载均衡**: DNS轮询，实现GSLB（全局负载均衡）

---

### 监控与分析

- **核心指标**:
  - **用户体验 (Core Web Vitals)**: LCP, FID, CLS
  - **网络**: TTFB, DNS解析, TCP连接, 传输时间
- **监控工具**:
  - **前端**: Lighthouse, WebPageTest, Performance API
  - **服务端**: APM (Skywalking, Prometheus), ELK

---

### 面试要点

**Q: 如何系统性地进行网络性能优化？**
A: 从四个层面入手：
1.  **前端**: 资源（压缩、合并）、缓存（浏览器、CDN）、请求（减少、加速）。
2.  **协议**: 升级HTTP/2或HTTP/3，利用其多路复用、头部压缩等特性。
3.  **服务端**: 部署负载均衡、优化数据库、使用缓存和消息队列进行异步化。
4.  **网络**: 全站CDN、优化DNS解析（如HTTPDNS）。

**Q: 如何选择合适的缓存策略？**
A:
- **静态资源 (JS, CSS, 图片)**: 长期有效，使用**强缓存** (`Cache-Control: max-age=...`) + 文件名带版本号/Hash。
- **动态内容 (HTML, API)**: 可能频繁变更，使用**协商缓存** (`ETag`, `Last-Modified`)，让浏览器每次都询问服务器是否更新。
- **个性化内容**: 不缓存或使用私有缓存 (`private`)。

**Q: HTTP/2相比HTTP/1.1的核心优势？**
A:
1.  **多路复用**: 根本上解决队头阻塞问题，允许单TCP连接并发处理多个请求。
2.  **头部压缩 (HPACK)**: 大幅减少HTTP头部的体积，节省带宽。
3.  **服务器推送**: 服务器可主动将客户端可能需要的资源推送到缓存，减少请求延迟。
4.  **二进制分帧**: 将数据封装在二进制帧中，传输和解析效率更高。

**Q: 如何监控和评估网络性能？**
A:
1.  **定义指标**: 关注核心用户体验指标 (Core Web Vitals) 和关键网络指标 (TTFB, LCP等)。
2.  **工具采集**:
    - **前端**: 使用Performance API、Lighthouse等工具收集真实用户数据(RUM)和实验室数据(Lab)。
    - **服务端**: 部署APM系统（如Skywalking）监控API响应时间、错误率和吞吐量。
3.  **分析和优化**: 建立性能基线，设定告警阈值，对性能瓶颈进行持续分析和迭代优化。
