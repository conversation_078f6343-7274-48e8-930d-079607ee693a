# TCP 粘包问题

## 什么是 TCP 粘包？
TCP 是一个面向字节流的协议，它没有消息边界的概念。当发送方连续发送多个小数据包时，TCP 为了提高网络效率（例如 Nagle 算法），可能会将这些包合并成一个大的数据包发送。接收方一次性从缓冲区读取数据时，可能会读到多个包的粘合体，导致无法区分原始消息的边界。

## 产生原因
1.  **发送方 (Nagle 算法)**：将多个小的数据包缓冲起来，合并成一个大的数据包再发送。
2.  **接收方**：应用程序从接收缓存区读取数据时不及时，多个数据包到达后一次性被读取。

## 解决方案
核心思想是**在应用层定义消息边界**。

1.  **定长消息**
    - **方法**：发送方和接收方约定好每个消息的固定长度。接收方每次读取固定长度的数据。
    - **优缺点**：实现简单，但浪费带宽，不灵活。

2.  **特殊分隔符**
    - **方法**：在每个消息的末尾添加一个特殊的分隔符（如 `\n`）。接收方通过扫描分隔符来切分消息。
    - **优缺点**：实现简单，灵活。但需保证数据本身不包含分隔符，或需要转义处理。

3.  **消息头 + 消息体**
    - **方法**：在每个消息前附加一个固定长度的头部，头部包含整个消息（或消息体）的长度。接收方先读取头部获取长度，再读取相应长度的消息体。这是最常用的方式。
    - **优缺点**：非常灵活，精确。但实现稍复杂。

4.  **使用标准协议/框架**
    - **方法**：直接使用如 Protobuf、gRPC、HTTP/2 等应用层协议。这些协议已经内置了消息分界机制。
    - **优缺点**：开发效率高，可靠。但会引入外部依赖。

### 实际应用场景
- 对于传输固定长度的数据，可以选择定长消息的方式。
- 对于文本数据，可以使用换行符作为分隔符。
- 对于复杂的通信协议，消息头的方式较为通用和灵活。
