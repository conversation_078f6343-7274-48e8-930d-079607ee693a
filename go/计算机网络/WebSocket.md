## WebSocket 核心面试要点

### 核心概念
- **定义**: 基于TCP的全双工通信协议，实现客户端与服务器的实时双向数据流。

### WebSocket vs HTTP

| 特性 | HTTP | WebSocket |
|------|------|-----------|
| **通信模式** | 请求-响应 | 全双工 |
| **连接方式** | 短连接 | 长连接 |
| **协议开销** | 每次请求都有头部 | 握手后开销小 |
| **实时性** | 需要轮询/长轮询 | 真正实时 |
| **服务器推送** | 不支持 (需变通) | 原生支持 |

### 握手过程 (Upgrade)
1. **客户端请求升级**: 发送 `Upgrade: websocket` HTTP头。
2. **服务器确认升级**: 返回 `101 Switching Protocols` 状态码。
3. **协议切换**: 连接升级为WebSocket。

### 关键操作码 (Opcode)
| 操作码 | 含义 |
|--------|------|
| `0x1` | 文本帧 (UTF-8) |
| `0x2` | 二进制帧 |
| `0x8` | 关闭帧 |
| `0x9` | Ping帧 (心跳) |
| `0xA` | Pong帧 (心跳响应) |

### 连接管理与心跳
- **连接管理**: 通过连接池统一管理客户端连接，处理注册、注销和消息广播。
- **心跳机制**:
    - **Ping/Pong**: 客户端与服务器定期发送Ping/Pong帧，检测连接是否存活。
    - **超时重连**: 设置读写超时，超时未收到心跳则关闭连接，客户端进行重连。

### 典型应用场景
- **即时通讯**: 聊天室、私信
- **实时通知**: 站内信、状态更新
- **在线协作**: 协同编辑、白板
- **实时监控**: 系统监控、日志查看
- **在线游戏**: 状态同步、实时对战

### 性能优化
- **连接管理**: 合理设置超时、使用连接池、及时清理无效连接。
- **消息处理**: 使用消息队列缓冲、批量处理、压缩消息。
- **水平扩展**: 结合Redis发布订阅、负载均衡等方案。

### 面试要点 (Q&A)

**Q: WebSocket和HTTP的主要区别？**
A:
1.  **连接**: WebSocket是持久长连接，HTTP通常是短连接。
2.  **通信**: WebSocket是全双工，HTTP是请求-响应。
3.  **开销**: WebSocket握手后数据帧头部小，HTTP请求头开销大。
4.  **实时性**: WebSocket是真实时，HTTP需轮询模拟。

**Q: 如何保持连接稳定？**
A:
1.  **心跳检测**: 定期发送Ping/Pong帧。
2.  **超时机制**: 设置读写超时，及时发现死连接。
3.  **断线重连**: 客户端实现自动重连逻辑。
4.  **服务端清理**: 服务器定期清理无效或断开的连接。

**Q: WebSocket的安全性如何保证？**
A:
1.  **WSS协议**: 基于TLS加密传输 (类似HTTPS)。
2.  **Origin检查**: 验证请求来源，防止CSRF攻击。
3.  **身份认证**: 握手阶段结合业务进行Token或Cookie认证。
4.  **消息过滤**: 对传输的数据进行安全校验。

**Q: 如何应对高并发？**
A:
1.  **连接池管理**: 限制单机连接数，防止资源耗尽。
2.  **消息队列**: 使用MQ异步处理业务逻辑，解耦和削峰。
3.  **负载均衡**: 使用Nginx等将连接分发到多台服务器。
4.  **水平扩展**: 设计无状态或共享状态的后端服务集群。