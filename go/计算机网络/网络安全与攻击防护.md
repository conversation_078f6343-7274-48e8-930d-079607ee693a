# 网络安全与攻击防护 (面试精要)

> 本文旨在梳理网络安全与攻击防护的核心概念，突出面试重点，便于快速查阅。

---

## 核心攻击类型与防御

### 1. DDoS (分布式拒绝服务)

- **核心原理**: 通过海量无效或高负载请求，耗尽目标服务器的带宽、连接或计算资源，使其无法响应正常服务。
- **主要类型**:
  - **网络层**: `SYN Flood` (TCP半连接攻击), `UDP Flood` (带宽消耗)
  - **应用层**: `HTTP Flood` (高负载请求)
  - **反射/放大攻击**: `DNS/NTP Amplification` (利用第三方服务放大流量)
- **防御策略**:
  - **流量清洗**: **CDN**、**DDoS防护服务** (如 BGP 牵引)
  - **资源管理**: **负载均衡**、**IP/接口限流**、**连接数限制**
  - **架构优化**: **黑洞路由** (丢弃攻击流量)

---

### 2. Web 应用攻击

| 攻击类型 | 核心原理 | 防护措施 |
| :--- | :--- | :--- |
| **SQL 注入** | 通过请求将恶意SQL代码注入后端，窃取/篡改数据。 | **参数化查询** (PreparedStatement)<br/>输入验证 (白名单)<br/>最小权限原则<br/>WAF (Web应用防火墙) |
| **XSS (跨站脚本)** | 攻击者在网页中注入恶意脚本，在用户浏览器上执行。 | **输出编码**: 对 `HTML/JS` 输出进行转义<br/>**CSP**: 内容安全策略，限制脚本来源<br/>**HttpOnly Cookie**: 防止脚本窃取Cookie |
| **CSRF (跨站请求伪造)** | 借用用户已登录的身份，以其名义发送恶意请求。 | **CSRF Token**: 验证请求来源<br/>**SameSite Cookie**: 限制Cookie跨站发送<br/>**Referer校验**: 检查请求来源 |

**XSS 类型**:
- **存储型**: 恶意代码存储于服务器，危害最大。
- **反射型**: 恶意代码存在于URL中，诱导用户点击。
- **DOM型**: 通过JavaScript修改DOM结构触发。

---

### 3. 中间人攻击 (MITM)

- **核心原理**: 攻击者在通信双方之间进行拦截和嗅探，甚至篡改通信内容。
- **主要防御**: **HTTPS**。通过加密、身份认证和完整性校验，有效防范MITM攻击。

---

## HTTPS: 安全基石

- **三大安全保障**:
  - **数据加密**: **对称加密** (如 AES) 加密传输内容，防止窃听。
  - **身份认证**: **非对称加密** (如 RSA) + **数字证书** (CA签发)，验证服务器身份，防止冒充。
  - **完整性校验**: **哈希算法** (如 SHA-256) + **数字签名**，确保数据未被篡改。

- **握手过程简述**:
  1. **ClientHello**: 客户端发送支持的加密套件、随机数。
  2. **ServerHello**: 服务器选择加密套件、返回证书、随机数。
  3. **客户端验证**: 验证证书，生成预主密钥 (Pre-Master Secret)，用服务器公钥加密发送。
  4. **服务器解密**: 用私钥解密，得到预主密钥。
  5. **生成会话密钥**: 双方根据协商的算法和三个随机数生成**对称会话密钥**。
  6. **加密通信**: 使用会话密钥进行加密通信。

---

## 网络纵深防御体系

- **分层防护**:
  - **网络层**: **防火墙** (访问控制), **IDS/IPS** (入侵检测/防御), **DDoS防护**
  - **应用层**: **WAF** (过滤Web攻击), **API网关** (统一安全策略)
- **安全最佳实践**:
  - **网络隔离**: **VLAN/子网划分**, **DMZ** (隔离区)
  - **访问控制**: **最小权限原则**, **白名单机制**, **多因素认证(MFA)**

---

## 安全开发与运维 (DevSecOps)

- **安全开发 (SDL)**:
  - **输入验证**: "所有用户输入都是不可信的"。
  - **输出编码**: 防止XSS。
  - **参数化查询**: 防止SQL注入。
  - **错误处理**: 避免泄露敏感信息。
- **安全监控与响应**:
  - **监控**: **SIEM** (安全信息与事件管理) 集中分析日志，**威胁情报**。
  - **响应**: 建立应急响应流程 (检测 -> 分析 -> 遏制 -> 恢复 -> 总结)。

---

## 高频面试题快问快答

**Q: XSS 和 CSRF 的区别？**
A:
- **XSS (攻击浏览器)**: 核心是**骗取用户在浏览器端执行恶意脚本**。目的是窃取Cookie、监听行为。防御靠**输入验证**和**输出编码**。
- **CSRF (攻击服务器)**: 核心是**借用用户的身份，发送非本意的请求给服务器**。目的是执行操作。防御靠 **Token** 或 **SameSite Cookie**。

**Q: SQL 注入如何防范？**
A: 根本原因是**代码和数据混淆**。最佳实践是**参数化查询/预编译语句**，让数据库驱动不把参数当作SQL指令。其次是输入验证和WAF。

**Q: HTTPS 握手过程，为什么需要三个随机数？**
A: **Client Random + Server Random + Pre-Master Secret**。三个随机数共同生成最终的会话密钥。**防止重放攻击**，确保每次会话的密钥都是唯一的，即使Pre-Master Secret被破解，以往的通信内容也无法解密。

**Q: 如何设计一个安全的系统？**
A: 从多个层面考虑：
- **安全开发 (SDL)**: 从源头避免漏洞。
- **纵深防御**: 网络层(防火墙)、应用层(WAF)、数据层(加密)层层设防。
- **权限管控**: 最小权限原则，严格的访问控制。
- **安全运维**: 建立完善的监控、告警和应急响应体系。
