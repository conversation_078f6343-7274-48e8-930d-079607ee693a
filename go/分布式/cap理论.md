# CAP 理论精简版 (面试速查)

## 一、核心定义

CAP 理论指出，任何分布式系统在以下三个核心特性中，最多只能同时满足两个。

- **一致性 (Consistency)**: 所有节点在同一时间具有相同的数据。每次读取都能获取到最新写入的数据。
- **可用性 (Availability)**: 系统始终处于可运行状态，对任何请求都能做出（非错误）响应。
- **分区容忍性 (Partition Tolerance)**: 在网络分区（节点间通信中断）的情况下，系统仍能继续运行。

> **关键前提**: 对于分布式系统，网络分区是必然会发生的，因此 **P (分区容忍性) 是必选项**。抉择的核心在于 C 和 A 之间。

## 二、核心抉择：CP vs AP

| 特性 | CP (一致性 + 分区容忍性) | AP (可用性 + 分区容忍性) |
| :--- | :--- | :--- |
| **核心** | **牺牲可用性，保证强一致性** | **牺牲强一致性，保证高可用** |
| **策略** | 网络分区发生时，为保证数据一致，节点会拒绝部分请求（例如，非主节点拒绝写请求），导致系统部分功能不可用。 | 网络分区发生时，为保证服务可用，节点仍会响应请求，但这可能导致返回的是过时数据。数据最终会通过同步机制达到一致。 |
| **典型系统** | ZooKeeper, etcd, HBase, MongoDB | Eureka, Consul, Cassandra, DynamoDB |
| **适用场景** | 对数据一致性要求极高的场景：<br/>- 金融交易 <br/>- 分布式锁 <br/>- 关键配置中心 | 对可用性要求极高的场景：<br/>- 社交媒体 Feed 流 <br/>- 服务发现 <br/>- 内容分发网络 (CDN) |

> **关于 CA**: 指的是在没有网络分区的情况下，同时保证一致性和可用性。这通常只适用于单体应用或不存在网络分区风险的局域网环境，对于现代分布式系统意义不大。

## 三、BASE 理论 (AP 的实践)

BASE 理论是 CAP 理论中 AP 方案的延伸和实践，它通过牺牲强一致性来获得可用性，其核心思想是：

- **基本可用 (Basically Available)**: 允许系统在出现故障时损失部分功能，保证核心功能可用。
- **软状态 (Soft State)**: 允许系统中的数据存在中间状态，该状态不影响系统整体可用性。
- **最终一致性 (Eventually Consistent)**: 系统中的所有数据副本，在经过一段时间的同步后，最终能够达到一个一致的状态。

## 四、面试核心要点

- **为什么P是必须的？**
  因为网络不可靠，分区是分布式系统的常态，必须容忍。

- **为什么C和A在分区时互斥？**
  如果发生网络分区（P），一个节点收到了写请求但无法同步给其他节点：
  - 如果要保证**一致性(C)**，必须阻止其他节点读取旧数据，甚至阻止自己响应，这就牺牲了**可用性(A)**。
  - 如果要保证**可用性(A)**，节点必须响应请求，但返回的可能是旧数据，这就牺牲了**一致性(C)**。

- **如何选择 CP 还是 AP？**
  根据业务场景对数据一致性的要求决定。如果业务无法容忍任何数据不一致（如支付），选择CP。如果业务可以容忍短暂的数据不一致以换取更好的用户体验（如服务发现、社交点赞），选择AP。

- **举例说明CP和AP系统？**
  - **CP**: ZooKeeper/etcd（用于服务协调、分布式锁），HBase（分布式数据库）。
  - **AP**: Eureka/Consul（用于服务发现），Cassandra（分布式NoSQL数据库）。

- **微服务架构如何应用CAP？**
  不同微服务可根据自身业务特性做不同选择。例如，订单和支付服务选择CP，而用户信息和产品推荐服务选择AP。

- **如何实现最终一致性？**
  常见方案包括：事件总线（如Kafka）、异步消息队列、数据库复制、定时任务校对等。