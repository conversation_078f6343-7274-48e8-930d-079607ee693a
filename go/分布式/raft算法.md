# Raft 核心精讲 (面试速查版)

## 1. Raft 概览

| 核心概念 | 描述 |
| :--- | :--- |
| **目标** | 实现分布式系统中的**共识** (Consensus)，保证数据一致性。 |
| **核心优势** | 相比 Paxos **更易于理解和实现**。 |
| **容错能力** | `2f + 1` 节点集群，可容忍 `f` 个节点失效 (e.g., 5节点可容忍2个宕机)。 |

### 节点角色

| 角色 | 职责 | 状态转换 |
| :--- | :--- | :--- |
| 👑 **Leader (领导者)** | 唯一处理客户端请求；管理日志复制。 | 由 Candidate 获得多数票后成为。 |
| 🧑‍💻 **Follower (跟随者)** | 被动接收并持久化日志；响应 Leader 和 Candidate 的请求。 | 所有节点初始状态；若收到更高 Term 的请求，会变为 Follower。 |
| 🙋 **Candidate (候选者)** | Leader 选举时的临时角色，用于竞选 Leader。 | Follower 超时未收到 Leader 心跳后转变而来。 |

---

## 2. 两大核心机制

### (1) Leader 选举 (Leader Election)

**目标：选出唯一的 Leader。**

**流程图:**
`Follower` --(选举超时)--> `Candidate` --(获得多数票)--> `Leader`

**详细步骤:**
1.  **触发**: Follower 在**随机的选举超时 (Election Timeout)** 内未收到 Leader 心跳。
2.  **转变**: Follower 转为 Candidate，**任期号 (Term) + 1**。
3.  **拉票**:
    -   给自己投一票。
    -   向集群中所有其他节点发送 `RequestVote` RPC。
4.  **结果判断**:
    -   ✅ **当选 Leader**: 收到**超过半数**节点的投票。立即发送心跳，宣告领导地位。
    -   🔄 **选举失败**:
        -   收到现任 Leader（Term ≥ 自己）的心跳，立即转回 Follower。
        -   收到更高 Term 的投票请求，承认对方，转回 Follower。
    -   ⏳ **选举超时**: 未获得多数票，Term + 1，发起新一轮选举。

**关键规则 (保证选举安全):**
- **一任一票**: 每个节点在同一 Term 内只能投一票（先到先得）。
- **日志最新原则**: 节点只会投票给**日志至少和自己一样新**的 Candidate。 (比较 `lastLogTerm` 和 `lastLogIndex`)

### (2) 日志复制 (Log Replication)

**目标：保证所有节点日志序列的一致性。**

**详细步骤:**
1.  **客户端请求**: 所有写请求都发给 Leader。
2.  **本地追加**: Leader 将请求作为新的日志条目 (Log Entry) 追加到**本地日志**中。
3.  **并行复制**: Leader 通过 `AppendEntries` RPC 将新日志条目并行发送给所有 Follower。
4.  **Follower 响应**:
    -   Follower 进行**一致性检查** (检查前一条日志的 Term 和 Index 是否匹配)。
    -   若检查通过，则将日志条目写入本地日志，并向 Leader 返回成功。
5.  **提交日志 (Commit)**:
    -   Leader 收到**超过半数** Follower 的成功响应后，将该日志条目**提交 (Commit)**。
    -   **已提交的日志是持久且不可变更的**。
6.  **应用到状态机**: Leader 将已提交的日志应用到自己的状态机，并响应客户端。
7.  **通知 Follower**: Leader 在后续的心跳或 `AppendEntries` RPC 中，会告知 Follower 哪些日志已被提交，Follower 收到后也将这些日志应用到自己的状态机。

---

## 3. 安全性与关键概念

### Raft 安全性保证

| 安全性原则 | 描述 |
| :--- | :--- |
| **选举安全 (Election Safety)** | 每个 Term **最多只有一个 Leader**。 |
| **Leader 只追加 (Leader Append-Only)** | Leader 绝不会覆盖或删除自己的日志，只会追加新条目。 |
| **日志匹配 (Log Matching)** | 如果两个日志在相同 Index 和 Term 上条目相同，那么它们在该 Index 之前的所有条目都相同。 |
| **Leader 完整性 (Leader Completeness)** | 新当选的 Leader **必须包含所有已提交的日志条目**。这是通过选举时的日志最新原则来保证的。 |
| **状态机安全 (State Machine Safety)** | 一旦某个日志条目被提交，它最终一定会被所有节点的状态机执行。 |

### 关键概念

| 概念 | 解释 |
| :--- | :--- |
| **Term (任期号)** | 逻辑时钟，单调递增。用于识别过期的 Leader 或 Candidate。 |
| **Commit Index** | 集群中**已提交**的最高日志条目索引。 |
| **Last Applied Index** | 节点状态机中**已执行**的最高日志条目索引。 (`lastApplied` ≤ `commitIndex`) |

---

## 4. 常见问题与应用

### 常见面试问题速览

-   **Raft 与 Paxos 的区别？**
    -   Raft 更易理解，通过强 Leader 简化了设计。Paxos 更灵活，但实现复杂。
-   **如何处理网络分区？**
    -   **多数派**分区继续正常工作。
    -   **少数派**分区无法选举 Leader，对外停止服务。分区恢复后，少数派节点会同步多数派的日志。
-   **如何防止脑裂 (Split Brain)？**
    -   Term 机制和"获得多数派"的原则。旧 Leader 所在的分区是少数派，无法提交日志；新 Leader 在多数派分区产生，Term 更高。
-   **Raft 性能瓶颈？**
    -   所有写操作都经过 Leader，Leader 成为瓶颈。
    -   网络延迟和磁盘 I/O。

### 实际应用
-   **etcd**: Kubernetes 的核心存储。
-   **Consul**: 服务发现与配置管理。
-   **TiKV**: 分布式 KV 数据库 (Multi-Raft)。
-   **ClickHouse**: 用于分布式元数据管理。