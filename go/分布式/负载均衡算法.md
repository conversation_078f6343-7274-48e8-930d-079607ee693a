# 负载均衡核心速查

## 1. 核心目标
- **流量分发**：将请求分散到多个服务器。
- **提升可用性**：避免单点故障，部分节点故障不影响整体服务。
- **提升性能与扩展性**：方便横向扩展服务器，应对高并发。

## 2. 核心算法分类

### 静态算法 (不关心后端服务器状态)
- **轮询 (Round Robin)**: 依次分配。
- **加权轮询 (Weighted RR)**: 按权重分配。
- **随机 (Random)**: 随机选择一个。
- **IP 哈希 (IP Hash)**: 基于客户端 IP 的哈希值分配，可实现会SH话保持。
- **一致性哈希 (Consistent Hashing)**: 环形哈希，增减服务器时影响最小。

### 动态算法 (根据服务器实时状态决策)
- **最少连接 (Least Connections)**: 选择当前连接数最少的服务器。
- **加权最少连接 (Weighted LC)**: `连接数/权重` 值最小者优先。
- **最短响应时间 (Least Response Time)**: 选择响应最快的服务器。

## 3. 算法精要对比

| 算法 | 类型 | 复杂度 | 效果 | 会话保持 | 动态性 | 关键点 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 轮询 | 静态 | 低 | 绝对均匀 | 否 | 否 | 简单，忽略服务器差异 |
| 加权轮询 | 静态 | 低 | 相对均匀 | 否 | 否 | 考虑权重，适用于异构服务器 |
| IP哈希 | 静态 | 低 | 可能不均 | 是 | 否 | 根据客户端IP哈希 |
| 一致性哈希 | 静态 | 高 | 可能不均 | 是 | 是 | 增减节点影响小，环形哈希 |
| 最少连接 | 动态 | 中 | 好 | 否 | 是 | 适合长连接、请求耗时差异大 |
| 最短响应时间| 动态 | 高 | 最好 | 否 | 是 | 基于真实响应时间，需监控 |

## 4. 实现层次与方案

| 层次 | 技术/产品 | 特点 |
| :--- | :--- | :--- |
| **DNS** | DNS A记录 | **优点**: 简单、全局负载均衡。 **缺点**: 缓存导致更新慢，故障切换不及时。|
| **硬件** | F5, A10 | **优点**: 性能强，功能全。 **缺点**: 昂贵，扩展性差。 |
| **软件 (L4)** | LVS | **优点**: 内核态，性能极高。 **缺点**: 只做转发，不处理应用层内容。 |
| **软件 (L7)** | Nginx, HAProxy | **优点**: 功能丰富(HTTP/SSL)，配置灵活。 **缺点**: 性能相对LVS较低。 |
| **客户端** | Ribbon, gRPC LB | **优点**: 服务直连，去中心化。 **缺点**: 客户端需实现逻辑，多语言支持成本高。 |


## 5. 关键设计与决策点

### Q1: 如何选择负载均衡算法？
- **无状态服务**: 轮询、随机、最少连接。
- **有状态服务 (会t保持)**: IP 哈希、一致性哈希，或应用层 Session 共享。
- **服务器性能差异大**: 加权类算法 (加权轮询、加权最少连接)。
- **请求耗时差异大/长连接**: 最少连接、最短响应时间。

### Q2: 如何处理服务器故障 (健康检查)？
- **机制**: LB 定期向后端服务器发起请求 (如 `ping`、`TCP`握手、`HTTP`请求)。
- **故障转移**: 若检查失败，则自动从可用池中移除节点 (熔断)。
- **故障恢复**: 节点恢复后，健康检查成功则自动加回池中。

### Q3: 负载均衡器自身如何保证高可用 (HA)？
- **主备模式 (Active-Passive)**: 例如 `Keepalived + Nginx/LVS`，通过虚拟IP (VIP) 漂移实现故障切换。
- **集群模式 (Active-Active)**: 多个LB同时工作，前端通过 DNS 轮询或 BGP 路由将流量分发到不同的LB。
