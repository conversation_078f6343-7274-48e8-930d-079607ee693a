# 分布式缓存 (精简版)

## 核心概念
分布式缓存是将缓存数据分布在多个节点上的缓存系统，提供高性能、高可用的数据访问服务。

## 核心优势与挑战
- **优势**: 高性能、高可用、可扩展、减轻数据库负载。
- **挑战**: 数据一致性、缓存穿透/击穿/雪崩、热点数据。

## 主流方案对比
- **Redis Cluster**: 官方集群，数据分片(16384槽)，主从复制，自动故障转移。
- **Redis Sentinel**: 高可用方案，通过监控、通知、自动主从切换实现。
- **Codis**: 基于代理的集群方案，对客户端透明，支持动态扩容。
- **Twemproxy**: Twitter开源的轻量级代理，专注分片和连接池。

## 数据分片策略
- **哈希分片**: `hash(key) % N`。优点: 简单。缺点: 扩容时数据迁移大。
- **一致性哈希**: 哈希环。优点: 扩容影响小。缺点: 可能数据倾斜。
- **范围分片**: 按Key范围。优点: 范围查询友好。缺点: 易产生热点。
- **目录分片**: 维护映射表。优点: 灵活。缺点: 映射表可能成为瓶颈。

## 常见问题与解决方案

### 1. 缓存穿透 (查询不存在的数据)
- **现象**: 请求绕过缓存，直达数据库。
- **方案**:
    - **布隆过滤器**: 快速判断数据是否存在。
    - **缓存空值**: 缓存空结果，设置短过期时间。
    - **接口层校验**: 拦截非法请求。

### 2. 缓存击穿 (热点Key失效)
- **现象**: 单个热点数据过期，大量请求瞬间打到数据库。
- **方案**:
    - **互斥锁/分布式锁**: 只允许一个线程重建缓存。
    - **热点数据永不过期**: 或后台线程异步刷新。

### 3. 缓存雪崩 (大量Key同时失效)
- **现象**: 大量缓存同时过期，数据库压力激增。
- **方案**:
    - **过期时间随机化**: 在基础过期时间上加一个随机值。
    - **多级缓存**: 本地缓存 + 分布式缓存。
    - **限流降级**: 保护数据库不被冲垮。
    - **数据预热**: 系统启动时加载热点数据。

### 4. 数据一致性
- **问题**: 缓存与数据库数据不一致。
- **核心策略**:
    - **先更新数据库，再删除缓存 (Cache Aside Pattern)**。
    - **为什么是删除缓存而不是更新缓存？**
        - **懒加载**: 只有在需要时才加载数据，避免无效写。
        - **并发问题**: 写后更新缓存，可能被旧的读请求覆盖。
    - **重试机制**: 删除缓存失败时，通过消息队列等方式异步重试，保证最终一致性。

## 缓存更新策略
- **Cache Aside (旁路缓存)**: 最常用。应用代码直接维护缓存。
    - **读**: 查缓存 -> (未命中) -> 查DB -> 写缓存。
    - **写**: 更新DB -> **删除**缓存。
- **Read/Write Through (读/写穿透)**: 缓存层负责与DB同步，对应用透明。
- **Write Behind (写回)**: 先写缓存，再由缓存异步批量写入数据库。适用于写操作频繁且对一致性要求不高的场景。

## 性能优化
- **数据结构**: 按需选择 (String, Hash, List, Set, ZSet)。用Hash结构聚合相关数据可减少内存占用。
- **内存优化**: 压缩、合理过期策略、数据淘汰机制(LRU/LFU)。
- **网络优化**: 客户端连接池、批量操作(Pipeline/MSET)、高效序列化协议(Protobuf)。

## 最佳实践
- **设计**: 合理设置过期时间、数据预热、监控告警、降级预案。
- **开发**: 统一Key命名规范、封装通用缓存操作、处理缓存异常。
- **运维**: 容量规划、性能监控(命中率、延迟)、故障演练、数据备份。
