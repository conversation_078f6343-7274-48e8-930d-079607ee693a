# 熔断器与限流算法 Cheatsheet

## 一、熔断器 (Circuit Breaker)

**核心目的**：防止单个服务的故障引发整个系统的雪崩效应（级联故障），实现**快速失败**。

### 核心三状态

*   `✅ 关闭 (Closed)`: 正常状态，请求直接调用下游服务，并持续统计成功率/失败率。
*   `❌ 开启 (Open)`: 熔断状态，请求**直接失败返回**，不再调用下游，减轻下游压力。
*   `❓ 半开 (Half-Open)`: 探测状态，允许少量"探测"请求通过，用于判断下游服务是否已恢复。

### 状态流转图

```mermaid
graph TD
    A[关闭 Closed] -- 失败率 > 阈值 --> B(开启 Open);
    B -- 超时时间到达 --> C{半开 Half-Open};
    C -- 探测请求成功 --> A;
    C -- 探测请求失败 --> B;
```

### 关键配置

*   **请求量阈值**：在统计周期内，触发熔断所需处理的最小请求数（防止少量请求失败就熔断）。
*   **失败率阈值**：触发熔断的失败比例。
*   **熔断开启时间**：从 `开启` -> `半开` 的等待时间。

---

## 二、限流算法 (Rate Limiting)

**核心目的**：在高并发场景下，通过限制请求速率来保护系统，防止因瞬时流量过高而导致的系统崩溃。

### 常用算法对比

| 算法 | 核心原理 | 优点 | 缺点 |
| :--- | :--- | :--- | :--- |
| **固定窗口** | 统计**固定时间周期**内的请求数 | 实现简单 | 有**临界突发**问题 (窗口切换时两倍流量) |
| **滑动窗口** | 将时间窗口细分为多个小格，**滑动统计** | 解决临界问题，流量控制更平滑 | 实现稍复杂，占用更多内存 |
| **令牌桶** | 以**恒定速率**往桶里放令牌，请求需获取令牌 | **允许突发流量**，只要桶内有令牌 | - |
| **漏桶** | 请求先进桶排队，以**恒定速率**流出被处理 | **强制平滑速率**，下游处理速率固定 | 无法应对突发流量 |

### 关键区别：令牌桶 vs 漏桶

*   `令牌桶 (Token Bucket)`：**允许突发**。只要桶里有令牌，请求可以被立即处理。适合秒杀、抢购等需要处理突发流量的场景。
*   `漏桶 (Leaky Bucket)`：**强制平滑**。请求只能以固定的速率被处理。适合保护下游系统，如消息队列消费、调用第三方固定速率API等。

---

## 三、面试核心要点

### 1. 区别：限流、熔断、降级

*   **限流 (Rate Limit)**：**入口防御**。不管后端服务是否健康，限制上游的请求速率。
*   **熔断 (Circuit Break)**：**下游保护**。当下游服务持续出错时，主动切断请求，防止其彻底崩溃，并快速返回失败。
*   **降级 (Degrade)**：**备用方案**。当服务熔断、限流或超时后，返回一个预设的、兜底的结果，保证核心功能可用性。

**关系**：流量过大 -> **限流** -> (限流后依然有问题) -> 后端服务故障 -> **熔断** -> **降级**返回兜底数据。

### 2. 分布式实现方案

*   **单机限流**：
    *   Guava RateLimiter (令牌桶实现)
    *   Go: `golang.org/x/time/rate` (令牌桶实现)
*   **分布式限流**：
    *   **核心技术**：`Redis + Lua 脚本` (保证原子性)
    *   **实现思路**：
        *   **滑动窗口**: 使用 Redis 的 `ZSET`。
        *   **令牌桶**: 使用 Redis 的 `HASH` 结构或直接用 Lua 脚本实现。
*   **服务治理框架 (如 PolarisMesh 北极星)**:
    *   **实现原理**：同样基于**令牌桶**算法，但将令牌计算和分发逻辑中心化到了**控制面(Control Plane)**。
    *   **优势**:
        *   **客户端无感知**: 客户端（SDK）只需向控制面节点上报并请求令牌，无需关心 Redis 等外部存储，简化了业务集成。
        *   **高性能**: 令牌计算在控制面内存中完成，性能极高。
        *   **精细化控制**: 支持针对服务、接口、甚至请求参数（如`Header`, `Query`）配置灵活的限流规则。
*   **熔断器框架**：
    *   Hystrix, Resilience4J (Java)
    *   Sentinel (Java, Go)
    *   go-breaker (Go)

### 3. 如何选择限流算法？

*   希望系统在整体稳定的前提下，能应对突发流量 -> **令牌桶**
*   希望严格保护下游系统，下游处理能力有限 -> **漏桶**
*   简单场景，能容忍临界问题 -> **固定窗口**

## 实际应用

### 熔断器应用场景
- **微服务调用**：防止级联故障
- **数据库访问**：保护数据库过载
- **外部API调用**：处理第三方服务不稳定
- **缓存穿透**：保护后端存储

### 限流应用场景
- **API网关**：保护后端服务
- **用户接口**：防止恶意刷量
- **资源保护**：数据库连接池限制
- **系统保护**：防止系统过载

## 常见面试问题

### 1. 熔断器相关
- **Q: 熔断器的作用是什么？**
- A: 防止故障扩散，保护系统稳定性，提供快速失败机制

- **Q: 熔断器的三种状态及转换条件？**
- A: 关闭、开启、半开三种状态，根据失败率和超时时间转换

- **Q: 如何设置熔断器参数？**
- A: 根据业务特点设置失败阈值、超时时间、请求量阈值

### 2. 限流算法相关
- **Q: 令牌桶和漏桶的区别？**
- A: 令牌桶允许突发流量，漏桶强制平滑输出

- **Q: 如何选择限流算法？**
- A: 根据业务需求：允许突发选令牌桶，要求平滑选漏桶

- **Q: 分布式环境下如何限流？**
- A: 使用Redis等共享存储，或者分片限流

### 3. 实际应用
- **Q: 微服务中如何实现熔断？**
- A: 使用Hystrix、Sentinel等框架，或自实现熔断逻辑

- **Q: 限流被触发后如何处理？**
- A: 返回错误码、降级处理、排队等待

### 4. 设计考虑
- **Q: 熔断器如何避免误判？**
- A: 设置合理的统计窗口、最小请求量、多维度判断

- **Q: 限流算法的性能考虑？**
- A: 选择合适算法、减少锁竞争、使用本地缓存

## 最佳实践

### 熔断器设计
- **多级熔断**：方法级、服务级、系统级
- **监控告警**：实时监控熔断状态
- **降级策略**：提供备用方案
- **参数调优**：根据实际情况调整参数

### 限流设计
- **分层限流**：网关、服务、资源多层保护
- **动态调整**：根据系统负载动态调整限流参数
- **用户区分**：VIP用户和普通用户不同限制
- **优雅降级**：超限时提供降级服务

### 监控指标
- **熔断器指标**：熔断次数、成功率、响应时间
- **限流指标**：请求量、拒绝率、通过率
- **系统指标**：CPU、内存、网络使用率
- **业务指标**：用户体验、业务成功率
