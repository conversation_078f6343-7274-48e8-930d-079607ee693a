# 分布式ID生成核心要点

## 一、核心要求

- **全局唯一**: ID在分布式系统中不重复。
- **趋势递增**: ID有序，利于数据库索引和排序。
- **高性能、高可用**: 服务能应对高并发请求，且无单点故障。

## 二、主流方案速览

| 方案 | 原理 | 优点 | 缺点 | 适用场景 |
|:---|:---|:---|:---|:---|
| **UUID** | 本地生成，基于时间、MAC地址等 | 客户端生成，无网络开销 | 无序，字符串长，索引效率差 | 非核心业务，对有序性无要求 |
| **数据库自增** | `AUTO_INCREMENT` + 步长 | 实现简单，ID严格递增 | 数据库压力大，存在单点故障风险 | 数据量小，并发度不高的内部系统 |
| **号段模式** | 服务端批量获取ID段，在内存中分配 | 性能高，ID有序 | 实现稍复杂，服务重启可能浪费ID | 中大规模系统，如订单、帖子ID |
| **Redis** | `INCR` 原子命令 | 性能极高，实现简单 | 依赖Redis，持久化配置不当会丢数据 | 对数据绝对安全要求不高的计数场景 |
| **Snowflake** | 时间戳+机器ID+序列号 | 性能高，趋势递增，服务端生成 | 依赖机器时钟，时钟回拨会出问题 | 高并发分布式系统，要求ID有序 |

## 三、Snowflake 算法详解

#### 结构

`| 1位符号(固定为0) | 41位时间戳(毫秒级,可用69年) | 10位机器ID(1024台) | 12位序列号(每毫秒4096个) |`

#### 核心问题及对策

- **时钟回拨 (Clock Skew)**
  - **问题**: 服务器时钟回退，可能导致生成重复ID。
  - **对策**:
    1. **直接拒绝**: 如果回拨时间短，直接拒绝服务，等待时钟追上。
    2. **记录并使用未来时间**: 记录上次时间戳，若当前时间小于上次，继续用上次时间戳+1生成序列号。
    3. **备用节点**: 切换到其他节点生成。

- **机器ID分配 (Worker ID)**
  - **问题**: 需保证集群中每个节点的ID唯一。
  - **对策**:
    1. **手动配置**: 配置文件或环境变量写死。
    2. **启动时注册**: 启动时去Zookeeper、Etcd等中心化服务获取一个唯一ID。
    3. **数据库号段**: 从数据库获取一个唯一ID。

## 四、面试快问快答

- **Q: 为什么不推荐用UUID做主键？**
  - **A:** UUID是无序的，会导致B+树索引在插入时频繁进行页分裂和合并，严重影响数据库插入性能。

- **Q: 高并发场景下，你会选择哪种ID方案？**
  - **A:** 首选Snowflake或号段模式。两者都能提供高性能和趋势递增的ID，满足绝大多数业务需求。

- **Q: 如何保证ID生成服务的高可用？**
  - **A:**
    1. **集群部署**: 将ID生成服务部署在多个节点上。
    2. **引入代理**: 使用Nginx或LVS等作为代理层，实现负载均衡和故障转移。
    3. **多机房容灾**: 在不同机房部署服务，避免单机房故障。

- **Q: 号段模式的ID浪费问题，有什么看法？**
  - **A:** 这是一种空间换时间的思想。浪费的ID量级可控（通常是服务重启时一个号段的剩余部分），相比于其带来的性能提升和数据库压力降低，这种牺牲是值得的。