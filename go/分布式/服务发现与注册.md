# 服务发现与注册 (面试精简版)

本文档旨在为面试提供一个关于服务发现与注册的快速参考，核心内容经过精简和重构，便于快速理解和回顾。

---

## 1. 核心概念与两种发现模式

服务发现是微服务架构中的基石，它让服务实例能够动态地注册自己的位置，并找到其他依赖的服务，无需硬编码IP地址和端口。

主要有两种实现模式：**客户端发现** 和 **服务端发现**。

```mermaid
graph TD
    subgraph "客户端发现模式 (Client-Side Discovery)"
        direction LR
        C1[客户端] -- "1. 查询服务地址" --> R1[注册中心]
        R1 -- "2. 返回地址列表" --> C1
        C1 -- "3. 自行负载均衡<br/>并直接调用" --> S1[服务实例 A]
        C1 -- "3. 自行负载均衡<br/>并直接调用" --> S2[服务实例 B]
    end

    subgraph "服务端发现模式 (Server-Side Discovery)"
        direction LR
        C2[客户端] -- "1. 请求固定地址" --> P1[负载均衡器/网关]
        P1 -- "2. 查询服务地址" --> R2[注册中心]
        R2 -- "3. 返回地址列表" --> P1
        P1 -- "4. 代理或转发请求" --> S3[服务实例 C]
    end
```

### 模式对比

| 模式 | 优点 | 缺点 | 代表技术 |
| :--- | :--- | :--- | :--- |
| **客户端发现** | 结构简单，客户端可实现更灵活的负载均衡策略。 | 客户端逻辑重，与注册中心强耦合，多语言实现成本高。 | Spring Cloud (Eureka + Ribbon) |
| **服务端发现** | 客户端无感知，逻辑简单，与注册中心解耦。 | 多了一次网络跳转，负载均衡器可能成为单点瓶颈。 | Nginx, F5, K8s Service |
| **服务网格** | **(前两种模式的演进)** 对应用透明/无侵入，通过Sidecar实现强大的流量治理、安全等功能。 | 架构复杂，运维成本高，有一定性能损耗。 | Istio, Linkerd |

---

## 2. 主流注册中心对比

选择注册中心时，通常需要在**一致性 (CP)** 和 **可用性 (AP)** 之间做权衡。

| 组件 | 一致性模型 | 核心特性 / 一句话总结 | 适用场景 |
| :--- | :--- | :--- | :--- |
| **Eureka** | **AP** (高可用) | Spring Cloud 全家桶核心，优先保证服务可用。 | 微服务注册发现，容忍数据短暂不一致，强调高可用性。 |
| **Consul** | **CP** (强一致) | 功能全面(KV/健康检查/多数据中心)，HashiCorp 出品。 | 需要服务发现、配置、多数据中心联通的复杂场景。 |
| **ZooKeeper** | **CP** (强一致) | 分布式协调领域的元老，通过临时节点和Watch机制实现。 | 需要可靠分布式锁、选举等协调功能的场景，兼做服务发现。 |
| **etcd** | **CP** (强一致) | K8s 的大脑，轻量、高性能的键值存储。 | Kubernetes 内部服务发现，或需要强一致元数据存储的云原生应用。 |
| **Nacos** | **AP / CP 可切换** | 阿里开源，"服务发现 + 配置管理" 二合一，生态整合好。 | Spring Cloud Alibaba 生态，或希望统一管理服务和配置的场景。 |

---

## 3. 核心机制：健康检查

健康检查是服务发现的"眼睛"，用于**动态、自动地**剔除已宕机或无响应的服务实例，保证调用的成功率。

- **检查方式**:
  - **被动:** 服务实例定期向注册中心发送**心跳 (Heartbeat)**。
  - **主动:** 注册中心反向探测服务实例的**健康检查端点** (如 HTTP `/health` 或 TCP 端口)。
- **关键策略**:
  - **检查间隔**: 平衡检查的及时性和系统开销。
  - **超时和重试**: 定义合理的超时时间和失败重试次数，防止因网络抖动造成误判。

---

## 4. 高频面试题精选

### Q1: 什么是服务发现？它解决了什么问题？
**A:** 在微服务架构中，服务实例的地址是动态变化的。服务发现机制**维护一个可用服务实例的动态列表**，使得服务之间可以找到对方并通信，**解决了服务定位的难题**，避免了硬编码地址。

### Q2: 客户端发现和服务端发现有什么区别？
**A:** 核心区别在于**谁来做负载均衡**和**谁与注册中心交互**。
- **客户端发现:** 客户端自己拉取地址列表，自己做负载均衡。
- **服务端发现:** 客户端请求一个中间代理 (LB/Gateway)，由代理来做服务发现和负载均衡。

### Q3: Eureka 为什么选择 AP 而不是 CP？
**A:** 这是 CAP 理论在实践中的权衡。
- Eureka 的设计哲学认为，在服务发现场景下，**可用性比数据一致性更重要**。
- 即使因为网络分区导致注册信息不是最新的（比如获取到了一个已下线的服务地址），客户端也应该有重试等容错机制来处理，这通常好过于因注册中心为了保证数据一致性而完全不可用，导致所有服务调用失败。为了可用性，它容忍了数据可能短暂不一致。

### Q4: 注册中心如果全挂了怎么办？(如何保证高可用)
**A:** 这是考察容错设计。
1.  **客户端缓存:** 这是**最重要**的防线。客户端会缓存一份从注册中心拉取到的服务列表在本地内存中。即使注册中心完全宕机，客户端也能依赖本地缓存继续调用已知服务，只是无法发现新注册的服务。
2.  **注册中心集群:** 注册中心本身通过集群部署来保证高可用，部分节点宕机不影响整体服务。
3.  **多地多活/异地容灾:** 部署在不同的机房或可用区。
4.  **降级预案:** 极端情况下，可降级到 DNS 或静态配置。

### Q5: Consul, ZooKeeper, Eureka 的异同点？
**A:**
- **相同点:** 都可用于服务发现。
- **不同点:**
  - **一致性:** Eureka 是 AP，Consul 和 ZooKeeper 是 CP。
  - **功能:** Eureka 专注于服务发现。Consul 是"全家桶"，提供服务发现、KV存储、健康检查、多数据中心方案。ZooKeeper 的定位是分布式协调器，服务发现只是其应用场景之一。
  - **健康检查:** Eureka 依赖客户端心跳。Consul 支持更多样的检查方式 (HTTP, TCP, Script)。
