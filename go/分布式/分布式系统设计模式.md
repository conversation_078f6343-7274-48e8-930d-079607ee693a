# 分布式系统设计模式 (面试速查版)

## 核心设计原则
- **可扩展性 (Scalability)**: 无状态设计、水平扩展优先、数据分片。
- **可用性 (Availability)**: 消除单点、故障隔离、快速恢复。
- **性能 (Performance)**: 减少网络IO、多用异步、善用缓存。

---

## 架构模式

| 模式 (Pattern) | 特点 (Characteristics) | 优点 (Pros) | 缺点 (Cons) | 应用 (Application) |
|---|---|---|---|---|
| **主从 (Master-Slave)** | 写:主, 读:从; 故障时选举新主 | 读写分离, 数据备份 | 主节点SPOF, 数据有延迟 | MySQL, Redis, Kafka |
| **对等 (Peer-to-Peer)** | 节点对等, 去中心化, 直接通信 | 无单点故障, 高扩展性 | 数据一致性与网络拓扑复杂 | BitTorrent, Blockchain, Cassandra |
| **分片 (Sharding)** | 数据按规则(哈希/范围)水平分区 | 高度水平扩展, 分散负载 | 跨分片查询/数据重平衡复杂 | MongoDB, Redis Cluster, HBase |
| **微服务** | 按业务拆分, 独立部署, API通信 | 技术栈灵活, 易于独立开发/扩展 | 架构复杂, 对运维/监控/一致性要求高 | (通用) |
| **事件驱动** | 服务间通过事件异步通信, 松耦合 | 高度解耦, 高扩展性 | 调试复杂, 难以保证事件顺序 | (通用) |

---

## 一致性模式 (CAP权衡)

| 一致性 | 定义 | 实现方式 | 优缺点 | 典型场景 |
|---|---|---|---|---|
| **强一致性 (CP)** | 任何读操作总能获取**最新**的写结果 | 同步复制、分布式锁、两阶段提交(2PC) | 数据一致性最强，但牺牲性能和可用性 | 金融、交易系统 |
| **最终一致性 (AP)** | 经过一段时间后，数据**最终**会达到一致 | 异步复制、Gossip协议、版本向量 | 高性能、高可用，但数据短期不一致 | 社交网络、用户状态 |

---

## 核心问题与解决方案

### 高可用 & 容错
- **故障检测**:
    - 心跳机制、超时检测、第三方监控
- **故障恢复**:
    - 主备切换、自动重启、数据修复/回滚
- **降级与限流**:
    - **降级**: 关闭非核心功能、返回缓存/默认值。
    - **限流**: 令牌桶/漏桶算法，保护系统不被冲垮。
- **脑裂问题解决**:
    - **仲裁机制**: 引入第三方判断 (如Zookeeper)。
    - **多数派投票 (Quorum)**: 大于一半节点同意。
    - **Fencing机制**: 阻止旧主节点继续操作共享资源。

### 性能 & 扩展
- **负载均衡策略**:
    - **静态**: 轮询、随机、加权。
    - **动态**: 最少连接、最快响应时间、基于CPU/内存负载。
- **热点数据处理**:
    - 多级缓存 (本地+分布式)、数据分片、读写分离。
- **一致性哈希**:
    - **优势**: 增减节点时，数据迁移量最小化，避免缓存大规模失效。
    - **优化**: 引入虚拟节点，使数据分布更均匀。

### 数据一致性
- **数据冲突解决方案**:
    - **乐观锁**: CAS (Compare-And-Swap) / 版本号。
    - **悲观锁**: 事务期间锁定资源。
    - **时间戳 / 向量时钟**: 确定事件顺序。

### 微服务挑战
- **服务治理**: 服务发现、注册、配置管理、熔断、降级。
- **分布式事务**: 2PC/3PC, TCC, Saga, 本地消息表。
- **链路追踪**: Trace ID串联请求，分析调用链 (如 OpenTelemetry)。
- **持续集成/部署 (CI/CD)**: 自动化测试、构建、部署。
