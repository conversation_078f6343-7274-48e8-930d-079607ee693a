# 分布式锁核心要点

## 1. 为什么需要分布式锁？
- **资源互斥**: 避免多个节点同时操作共享资源，如：秒杀扣库存。
- **任务防重**: 防止同一个定时任务或消息被重复执行。
- **数据一致性**: 维护分布式系统中的数据一致性，如：更新缓存时，先失效再更新。

## 2. 主流实现方案对比

| 特性 | Redis | ZooKeeper / etcd | 数据库 |
| :--- | :--- | :--- | :--- |
| **模型** | AP (性能优先) | CP (一致性优先) | CP (一致性优先) |
| **性能** | **最高** (内存, ns-ms级) | 较高 (Raft/Zab, ms级) | 最低 (依赖磁盘IO) |
| **可靠性** | 依赖哨兵/集群; Redlock可增强 | **非常高** (Raft/Zab) | 好 (依赖DB高可用) |
| **特性** | Key超时防死锁, 非公平锁 | 临时节点/Lease, Watch机制, 公平锁 | 实现简单, 依赖事务 |
| **推荐场景** | 高并发、性能敏感、短时锁 | 可靠性、一致性要求高的场景 | 业务简单, 已有DB, 并发低 |

## 3. 实现关键点

### Redis
- **加锁 (原子操作)**: `SET key unique_id NX PX timeout`
  - `NX`: 只在 key 不存在时设置成功。
  - `PX`: 设置过期时间，**防止死锁**。
  - `unique_id`: 客户端唯一标识，**防止误删**。
- **解锁 (原子操作)**: 使用 Lua 脚本，先`GET`验证`unique_id`再`DEL`。
  ```lua
  if redis.call("get",KEYS[1]) == ARGV[1] then
      return redis.call("del",KEYS[1])
  end
  ```
- **锁续期 (看门狗)**: 客户端通过后台线程定时延长锁的过期时间，防止业务未执行完锁失效。
- **高可用 (Redlock)**: 在过半（N/2+1）独立的 Master 上请求锁，成本高，安全性有争议 (时钟漂移问题)。

### ZooKeeper / etcd
- **原理**: 利用其CP特性和节点功能。
  - **ZooKeeper**: 临时顺序节点 + Watch 机制。
  - **etcd**: Lease(租约) + Key Prefix + Watch 机制。
- **加锁 (公平锁)**:
  1. 创建一个带唯一ID的 Key (ZK: 临时顺序节点, etcd: `pfx/uuid` + Lease)。
  2. 判断自己是否是序号最小的节点。
  3. 若是，获取锁成功。
  4. 若不是，监听(Watch)前一个节点的删除事件。
- **解锁**: 删除自己创建的 Key。客户端崩溃时，临时节点/Lease会自动失效，**天然防死锁**。
- **优点**: 天然解决惊群效应(只Watch前一个)，实现公平锁，可靠性高。

### 数据库
- **方式一：唯一索引**: `INSERT`一个唯一键，成功则加锁，`DELETE`解锁。
- **方式二：悲观锁**: `SELECT ... FOR UPDATE` (依赖行级锁和事务)。
- **缺点**: 性能开销大，依赖数据库，要处理好事务和索引，否则易死锁。

## 4. 核心设计问题 Q&A

- **Q: 如何保证互斥性 (Mutually Exclusive)？**
  - **A:** 利用存储系统自身的原子操作。如 Redis 的 `SETNX`，数据库的唯一索引。

- **Q: 如何防止死锁 (Deadlock)？**
  - **A:**
    1. **超时释放**: 给锁加上过期时间（Redis `PX`）。
    2. **会话/连接绑定**: 利用 ZK/etcd 的临时节点或 Lease，客户端崩溃后锁自动释放。

- **Q: 如何保证安全性 (Safety)，防止误删锁？**
  - **A:** 加锁时设置一个**唯一随机值** (如UUID)。解锁时，先获取锁的值，判断是否和自己设置的一致，再执行删除。

- **Q: 如何实现锁续期 (Lease Renewal)？**
  - **A:** 客户端在持有锁期间，启动一个后台线程/协程，在锁过期前为其自动续期。如果客户端崩溃，续期停止，锁最终会自动释放。

- **Q: Redis 锁在主从切换时，可能导致两个客户端都拿到锁，怎么办？**
  - **A:** 这是 Redis 异步复制的缺陷。
    1. **容忍**: 如果业务能容忍短时间的不一致。
    2. **Redlock**: 使用 Redlock 算法，向多个独立的 master 实例获取锁。但该算法争议较大，实现复杂。
    3. **换方案**: 改用 ZK/etcd 等CP型实现。

- **Q: 如何实现可重入锁 (Reentrant Lock)？**
  - **A:** 在锁的 Value 中记录**持有者信息和重入次数**。加锁时，如果发现持有者是自己，则增加重入次数，直接返回成功。解锁时则减少重入次数，减到0才真正释放锁。
