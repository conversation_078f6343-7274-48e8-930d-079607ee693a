# 分布式事务速查

## 核心挑战
- **一致性**: 如何在多个独立系统中保证所有操作要么全部成功，要么全部失败。
- **故障处理**: 需要处理单个服务故障、网络分区等异常情况。
- **性能**: 跨服务的协调和数据同步会带来额外的性能开销。

## 方案速览
| 方案 | 一致性 | 性能 | 实现复杂度 | 适用场景 |
| :--- | :--- | :--- | :--- | :--- |
| **2PC** | 强一致 | 低 | 中 | 短事务，不追求高性能，传统单体应用 |
| **3PC** | 强一致 | 较低 | 高 | 对2PC阻塞问题的优化，但很少使用 |
| **TCC** | 强一致 | 高 | 高 | **核心业务**，性能要求高，业务侵入强 |
| **Saga** | 最终一致 | 高 | 中 | **长事务**，微服务，业务流程复杂 |
| **消息队列** | 最终一致 | 很高 | 低 | 高并发，可接受延迟，**业务解耦** |

---

## 主要方案详解

### 1. 两阶段提交 (2PC)
- **一句话概括**: 协调者分两阶段（准备、提交）进行投票，"要么都做，要么都不做"。
- **原理**:
    - **准备阶段**: 协调者询问所有参与者是否可以提交，参与者锁定资源并回复。
    - **提交阶段**: 协调者根据投票结果，通知所有参与者提交或回滚。
- **优点**: ✅ 强一致性，原理简单。
- **缺点**: ❌ **同步阻塞** (所有参与者等待协调者)，**协调者单点故障**，极端情况下数据不一致。
- **与3PC对比**: 3PC在2PC基础上增加了`CanCommit`阶段和超时机制，减少了阻塞，但更复杂且仍有问题。

### 2. TCC (Try-Confirm-Cancel)
- **一句话概括**: 业务层面的"两阶段提交"，手动编码实现 `Try`, `Confirm`, `Cancel`。
- **原理**:
    - **Try**: 检查并**预留资源**。
    - **Confirm**: 确认执行，使用预留的资源。
    - **Cancel**: 取消执行，**释放预留的资源**（补偿）。
- **优点**: ✅ 性能高（不依赖DB锁），应用层控制，灵活性高。
- **缺点**: ❌ **业务侵入强**，开发成本高，需要考虑幂等、空回滚等问题。

### 3. Saga 模式
- **一句话概括**: 将长事务拆分为多个本地事务，每个事务都有对应的补偿操作，失败时反向补偿。
- **原理**:
    - 正向执行：`T1, T2, ..., Tn`
    - 逆向补偿：若 `Ti` 失败，则执行 `C(i-1), ..., C1`
- **实现方式**:
    - **编排式 (Orchestration)**: 由一个中央协调器统一调度。
    - **编舞式 (Choreography)**: 各服务通过监听事件触发下游服务。
- **优点**: ✅ 适合长事务，无长时间锁定，性能好，易于扩展。
- **缺点**: ❌ **非强一致性 (最终一致)**，可能看到中间状态，补偿逻辑复杂。

### 4. 消息队列（可靠事件模式）
- **一句话概括**: 通过消息中间件实现上下游业务的解耦和最终一致性。
- **原理**:
    1. 上游服务在**本地事务**中完成业务，同时记录一条消息到"本地消息表"。
    2. 通过后台任务将消息可靠地投递到消息队列。
    3. 下游服务消费消息，完成自己的业务操作（需保证幂等）。
- **优点**: ✅ 高性能，高可用，业务松耦合。
- **缺点**: ❌ **非强一致性 (最终一致)**，依赖消息中间件的可靠性。

---

## 关键设计与面试要点

- **如何选择方案?**
    - **钱、库存等核心业务，要求强一致性**: 优先考虑 TCC。
    - **高并发，可接受最终一致性**: 优先考虑 Saga 或 消息队列。
    - **业务需要解耦，链路长**: 消息队列是最佳选择。
    - **微服务架构**: Saga 和 消息队列是主流选择。

- **幂等性如何保证?**
    - **全局唯一ID**: 为每次请求生成唯一ID，处理前检查ID是否已被处理。
    - **去重表**: 利用数据库唯一索引防止重复插入。
    - **状态机**: 利用订单状态等进行前置判断，如"已支付"状态的订单不再扣款。
    - **乐观锁**: `update table set version = version + 1 where ... and version = old_version`。

- **如何处理异常?**
    - **超时控制**: 防止资源无限期等待。
    - **重试机制**: 关键步骤失败后应有重试（如网络抖动），注意重试间隔和次数。
    - **补偿/回滚**: TCC 和 Saga 的核心，保证数据能回退到一致状态。
    - **告警+人工介入**: 对于无法自动恢复的异常（如补偿失败），必须有监控告警，并由人工处理。