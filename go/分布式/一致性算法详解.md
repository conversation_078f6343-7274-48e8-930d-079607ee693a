# 分布式一致性算法速查手册

本手册旨在帮助在面试中快速回忆和阐述核心概念，内容精炼，突出重点。

---

## 核心概览

- **一致性问题**: 在分布式系统中，多个节点需要对某个值达成一致，即使存在节点故障或网络分区。
- **故障模型**:
    - **崩溃故障 (Crash Fault)**: 节点宕机，不发送错误信息。
        - **容错能力**: `2f+1` 个节点可容忍 `f` 个故障。
    - **拜占庭故障 (Byzantine Fault)**: 节点可能发送错误或恶意信息。
        - **容错能力**: `3f+1` 个节点可容忍 `f` 个故障。

---

## 主流算法对比

| 算法 | 容错模型 | 核心思想 | 优点 | 缺点 | 典型应用 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **Raft** | 崩溃故障 | **强领导者 (Strong Leader)**，所有操作经由 Leader | **易于理解和实现**，业界广泛应用 | 领导者是性能瓶颈，选举期间不可用 | `etcd`, `Consul`, `TiKV` |
| **Paxos** | 崩溃故障 | **无固定Leader**，两阶段提交 (Prepare/Accept) | **理论完备**，足够灵活 | **难于理解和实现**，易产生"活锁" | `ZooKeeper(ZAB)`, `Chubby` |
| **PBFT** | 拜占庭故障 | 三阶段协议，**视图变更 (View Change)** | **容忍恶意/欺骗节点**，高可靠性 | 消息复杂度高 `O(n²)`, 节点数受限 | `Hyperledger Fabric`, `Tendermint` |

---

## 算法精要

### 1. Raft
- **角色**: `Leader`, `Follower`, `Candidate`。
- **核心流程**:
    1. **领导者选举**: `Follower` 在心跳超时后，转为 `Candidate`，向其他节点请求投票。获得多数票者成为新 `Leader`。
    2. **日志复制**: 客户端请求只发给 `Leader`。`Leader` 将操作写入本地日志，然后并行发给所有 `Follower`。
    3. **提交**: 当 `Leader` 收到 **多数派** `Follower` 的成功响应后，将该日志条目标记为 **已提交 (committed)**，并执行操作，然后响应客户端。

### 2. Paxos (以Multi-Paxos为例)
- **核心思想**: 将一个复杂的决策分解为一系列简单的、可达成共识的提案。
- **两阶段提交**:
    1. **Prepare**: 提案者(Proposer) 发起一个带编号 `N` 的 `prepare` 请求，争取到多数接收者(Acceptor)的**承诺(promise)**：不再接受编号小于 `N` 的提案。
    2. **Accept**: 若争取成功，Proposer 发送带编号 `N` 和内容的 `accept` 请求。Acceptor 若未承诺过更高编号，则**接受**该提案。
- **Multi-Paxos**: 选举一个 **主Proposer (Leader)**，后续提案都由它发起，从而跳过 `Prepare` 阶段，大幅提升性能，使其接近 Raft。

### 3. PBFT (实用拜占庭容错)
- **核心思想**: 通过三阶段协议，在存在恶意节点的情况下，让诚实节点对请求顺序达成一致。
- **三阶段协议**:
    1. **Pre-prepare**: 主节点(Primary) 收到客户端请求，为其分配序号，然后广播 `pre-prepare` 消息给所有备份节点(Replica)。
    2. **Prepare**: 备份节点收到 `pre-prepare` 消息后，若同意，则向所有其他节点广播 `prepare` 消息。
    3. **Commit**: 当一个节点收到了 `2f+1` 个（包括自己）一致的 `prepare` 消息后，说明多数节点已准备就绪，于是广播 `commit` 消息。
    4. **Reply**: 当一个节点收到了 `2f+1` 个 `commit` 消息后，执行请求并响应客户端。

---

## 高频面试题

- **Q: Raft 和 Paxos 的主要区别是什么？**
    - **A:**
        - **核心差异**: **Raft 强调强领导者**，简化了算法，使其更易于理解和实现。Paxos 理论上更灵活（允许多个节点同时提议），但实现复杂得多。
        - **易理解性**: Raft > Paxos。Raft 的设计目标之一就是可理解性。
        - **工程应用**: Raft 因其简单性在工程界应用更广。Paxos 更多是理论基石，实际系统（如ZooKeeper的ZAB）使用的是其变种。

- **Q: 为什么 PBFT 需要 `3f+1` 个节点来容忍 `f` 个恶意节点？**
    - **A:** 这是为了保证 **安全性 (Safety)** 和 **活性 (Liveness)**。
        - 简单来说：系统需要从节点中获得 `2f+1` 个回复来确认一个操作是有效的。
        - 考虑最坏情况：`f` 个节点是恶意的，它们可能给出错误的回复；另外 `f` 个诚实的节点可能恰好宕机或网络不通。
        - 为了让系统依然能够凑够 `2f+1` 个节点来达成共识，总节点数 `n` 必须满足 `n - f (恶意) - f (宕机) >= 1`，但这不严谨。
        - **正确理解**: 为了确保任意两个"法定人群"(Quorum，大小为`2f+1`)的交集至少包含一个诚实节点，以防止系统分裂。数学推导要求 `n >= 3f+1`。这样即使有 `f` 个节点撒谎，`2f+1` 个签名中也至少有 `f+1` 个来自诚实节点，保证了正确性。

- **Q: 如何处理分布式系统中的"脑裂"问题？**
    - **A:** 核心是 **多数派机制**。
        - **Raft/Paxos**: 当网络分区发生时，只有包含 **超过半数 (`(n/2)+1`)** 节点的分区才能选举出 Leader 并对外提供服务。
        - 少数派分区因为无法获得多数票，不能选举出 Leader，也就无法处理写请求，从而避免了数据不一致。

- **Q: 常见的一致性算法性能优化策略有哪些？**
    - **A:**
        - **批处理 (Batching)**: 将多个客户端请求打包成一批，一次性发起共识，减少网络和CPU开销。
        - **流水线 (Pipelining)**: Leader 可以连续发送日志复制请求，而无需等待前一个请求的响应，大幅提高吞吐量。
        - **只读优化 (Read Optimization)**: 对于读请求，Leader 可以直接返回本地数据（需确认自身Leader地位），或通过租约(Lease)机制，Follower 也能在一定租期内安全地响应读请求。
