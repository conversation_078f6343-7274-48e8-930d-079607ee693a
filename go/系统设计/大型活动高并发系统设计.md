### 系统设计题："大型活动（如春节红包）高并发系统设计"

**请你设计一个类似腾讯视频春节"集卡/抢红包"活动的后端系统。**

这是一个典型的瞬时高并发、高时效性、高稳定性和数据一致性要求的场景。我的设计思路如下：

#### a. 整体架构与数据流

这个系统的核心设计思想是"削峰填谷"和"关键路径轻量化"。我们要将瞬时的流量洪峰，通过各种手段转化为平稳的、后端系统可承受的数据流。

1.  **客户端 (Client)**: App/H5页面。负责UI展示、静态资源加载、定时拉取活动状态，并在关键时刻（如晚8点）发起请求。
2.  **接入层 (Access Layer)**:
    *   **CDN**: 缓存所有静态资源（图片、JS、CSS），以及不常变化的API，如活动规则、奖品列表等。这是抵御流量的第一道防线。
    *   **API网关 (API Gateway)**: 作为流量入口，负责鉴权、路由、协议转换，以及最关键的**限流**。
3.  **核心业务层 (Core Business Layer)**: 采用微服务架构，隔离不同业务。
    *   **资格校验服务 (Qualification Service)**: 检查用户是否满足参与条件（如登录状态、会员等级、是否在黑名单中）。这是一个无状态服务，可以水平扩展。
    *   **抢红包/抽卡核心服务 (Grabbing Core Service)**: **这是关键路径中的核心**。它的逻辑必须极度轻量化。它只做一件事：快速校验请求合法性，然后将请求丢入消息队列，并立即向客户端返回"正在处理中"或"排队中"。
    *   **发奖/记账服务 (Awarding Service)**: 这是一个异步的消费者集群，它从消息队列中拉取任务，执行真正耗时的操作：扣减库存、计算奖品、写入用户账本、记录流水。
4.  **数据与消息队列层 (Data & Messaging Layer)**:
    *   **消息队列 (Message Queue - Kafka/Pulsar)**: 用它来承接瞬时流量，实现业务逻辑的异步解耦，将前端的"脉冲"流量转化为后端平滑的"直流"处理。
    *   **高速缓存 (Redis Cluster)**: 存放所有"热"数据。例如：奖品库存、用户参与次数、防刷黑/灰名单、幂等性判断的`request_id`集合等。
    *   **持久化存储 (MySQL Cluster / TiDB)**: 存放最终的"事实"数据，如中奖记录、用户资产流水、活动配置等。数据库**绝对不能**放在抢红包的直接请求路径上。

#### b. 核心挑战：如何应对瞬时亿级请求洪峰？

这是该系统的"C位"难题。假设有5000万用户在同一秒点击按钮，请求量将是千万级QPS，任何常规系统都无法直接处理。

**关键设计1：层层过滤，削峰填谷 (Multi-layer Filtering & Peak Shaving)**

这是一个漏斗模型，每一层都过滤掉大量请求，最终到达核心业务层的流量是可控的。

1.  **客户端削峰**: 在客户端，抢红包按钮点击后，不立即发请求，而是增加一个几百毫秒到1秒的随机延迟。这能将集中的请求在时间上打散一点。
2.  **接入层(网关)限流**: 这是最关键的削峰手段。使用令牌桶或漏桶算法，配置一个全局的、远小于实际请求峰值的速率。比如，我们的后端处理能力是10万QPS，那么网关就严格限制只放行10万QPS，超出的请求直接拒绝（返回特定错误码，如HTTP 429），客户端收到后可以显示"活动火爆，请稍后再试"。
3.  **消息队列削峰**: 这是兜底的缓冲层。通过网关的请求，会立即被写入Kafka。Kafka的吞吐能力极高（可达百万/秒），完全可以承接住这一波流量。而后端的发奖服务则可以按照自己的节奏，平稳地从Kafka消费数据进行处理。

**关键设计2：全内存处理，原子化操作 (In-Memory Processing & Atomic Operations)**

库存的扣减是整个过程的另一个瓶颈，必须快且准。

*   **技术选型**: Redis是最佳选择。
*   **库存扣减**: 使用Redis的 `DECR` 命令来扣减库存。它是原子操作，天生线程安全。
*   **避免库存超卖**: 为了防止在并发下出现"检查库存>0"和"扣减库存"的非原子性问题，必须使用 **Redis Lua脚本**。将"读取库存、判断库存、扣减库存"这三步封装在一个Lua脚本里，利用Redis执行Lua的原子性，确保操作的正确性。
*   **复杂奖池设计**: 如果奖品有多种（例如iPhone、现金红包、优惠券），可以用一个Redis `List`作为奖品队列。`LPOP` 命令可以原子性地弹出一个奖品。如果返回 `nil`，说明该奖品池已空。

#### c. 数据一致性

在分布式、高并发环境下，如何保证用户的奖品不丢、不错、不重复？

1.  **请求唯一ID (Unique Request ID)**: 每个抢红包的请求，从客户端生成时就要带上一个全局唯一的`request_id`。
2.  **可靠消息投递**: 核心服务在将请求写入Kafka时，必须确保消息成功投递。可以采用带回调的同步发送，或在失败后有重试机制。
3.  **消费者幂等性 (Idempotent Consumer)**: 这是**至关重要**的一点。由于网络等原因，Kafka中的同一个消息可能会被重复消费。发奖服务必须具备幂等性。
    *   **实现**: 在发奖服务处理一个`request_id`之前，先去Redis里用 `SETNX` 或 `SADD` 检查该`request_id`是否存在。如果存在，说明是重复消息，直接丢弃；如果不存在，则处理该消息，并将`request_id`存入Redis并设置一个合适的过期时间（如24小时）。
4.  **操作顺序与风险控制（应对Redis与MySQL不一致）**: `发奖/记账服务`同时操作数据库和缓存，存在数据不一致的风险。系统采用"最终一致性"方案，核心策略是控制操作顺序，优先保证核心数据不出错。
    *   **步骤1：先更新数据库**: 在一个MySQL事务中完成所有"事实数据"的写入（如中奖记录、流水日志）。
    *   **步骤2：后更新缓存**: 只有在数据库事务成功提交后，才去执行对Redis的操作（如扣减库存）。
    *   **为什么这么做？**: 数据库是系统的"事实之源"(Source of Truth)。最坏的情况是数据库写入成功，但缓存更新失败。这时，用户资产是安全的，只是缓存中的库存等数据暂时不准。如果Kafka消息因处理失败而重发，幂等性机制会防止数据库重复写入，并再次尝试更新缓存，使系统有机会自我修复。
5.  **最终一致性与对账**: 用户的资产（如红包余额）和流水日志最终会落在MySQL中。发奖服务在更新MySQL时，需要启用事务。此外，必须有一个离线的对账系统，在活动结束后（或分时段）核对Redis中的库存消耗、Kafka中的消息总数、MySQL中的发奖记录总数，三方数据必须吻合，确保数据最终一致性。如果发现不一致（例如，由"先写库后写缓存"失败导致的问题），对账系统会发出预警并以数据库为准进行修复。

#### d. 高可用与容灾

活动不能出任何线上事故，必须做到万无一失。

*   **异地多活**: 核心服务（网关、核心逻辑、Redis、Kafka）必须部署在两个或以上的地理上隔离的机房，实现异地多活。
*   **降级、熔断与限流**:
    *   **降级**: 如果系统压力过大，可以动态关闭一些非核心功能，如活动页面的排行榜展示、详细中奖名单滚动等，优先保证抢红包核心链路的稳定。
    *   **熔断**: 任何服务间的RPC调用，都必须有熔断器（如gobreaker）。当某个下游服务（如用户信息服务）出现故障时，快速失败，甚至返回一个默认值，避免请求堆积导致雪崩。
    *   **限流**: 除了网关的全局限流，每个微服务自身也要有单机限流，防止被流量打垮。
*   **预案与压测**: 在活动上线前，必须进行全链路压力测试，摸清系统瓶颈和准确容量。对所有可能出现的故障（如Redis宕机、DB慢查询、网络分区）都要有详细的应急预案（Runbook）。

#### e. 反作弊设计

有利益的地方就有"羊毛党"，反作弊是活动成功的关键保障。

*   **风控前置**: 在用户请求到达核心业务逻辑前，就应该通过风控系统进行拦截。
*   **实时反作弊**:
    *   **规则引擎**: 基于用户ID、设备ID、IP地址，在时间窗口内统计其请求频率。例如，1秒内请求超过3次，直接判定为异常，并拉入临时黑名单。
    *   **黑名单库**: 维护一个恶意用户/设备/IP的黑名单，在网关层或资格校验服务中直接拒绝其请求。
*   **离线分析**:
    *   **行为建模**: 正常用户的行为是相对随机的，而机器刷量的行为模式（如请求间隔、时间点）往往高度一致。通过离线分析日志，可以挖掘出这些作弊团伙。
    *   **设备指纹**: 识别同一设备上的多个账号，限制其参与次数。
*   **业务门槛**: 提高参与门槛，如要求是注册超过7天的用户、或要求绑定手机号等，可以有效过滤掉大量为刷奖而生的"僵尸号"。

#### f. 客户端体验与结果异步通知

一个常见的问题是：核心服务立即返回"排队中"，客户端如何以及何时得知最终的抢红包结果？

前端不会一直等待。即时的"排队中"响应是为了快速释放连接，而真正的结果是通过异步方式通知的。最常用和最稳健的方式是 **客户端轮询**。

1.  **同步返回凭证**: `抢红包/抽卡核心服务` 在将请求送入消息队列后，会立即同步返回给客户端一个带有唯一`request_id`的"处理中"回包。
2.  **客户端轮询**: 客户端收到该回包后，启动定时器，每隔1-3秒（可附加随机延迟以错峰）调用一个专门的**结果查询接口**，请求中携带此`request_id`。
3.  **高效的结果查询服务**: 这个查询服务逻辑极简，它仅用`request_id`从 **Redis** 中查询结果。`发奖/记账服务`在处理完任务后，会将最终结果（如"恭喜获得1元红包"或"谢谢参与"）写入Redis，并设置一个较短的过期时间（例如5分钟）。
4.  **轮询的压力处理**: 虽然轮询会产生大量请求，但这种"读请求"的压力和初始的"写请求"洪峰是完全不同的：
    *   **压力模型不同**: 轮询请求被时间摊平，负载相对平缓，而非瞬时脉冲。
    *   **处理链路极快**: 请求直达Redis，这是内存操作，QPS可以轻松达到百万级别，与操作数据库和Kafka的核心链路完全分离。
    *   **可水平扩展**: 结果查询服务是无状态的，可以无限扩展实例来应对流量。
5.  **获取结果与超时**: 客户端轮询直到从查询接口拿到最终状态（成功/失败），然后停止轮询并渲染UI。轮询本身也应有超时限制（如45秒），超时后提示用户"系统繁忙，请稍后在卡包/钱包中查看结果"，保证即使通知链路拥堵，用户的资产记录也是安全的。 