### 系统设计题："腾讯视频'一起看'"功能系统设计

#### a. 整体架构

这是一个以实时通信为核心的系统，我会将它拆分为几个独立的、高内聚的服务来构建。它融合了有状态的网关、无状态的业务逻辑以及高性能的实时状态存储。

1.  **客户端 (Client)**：
    *   负责视频播放、UI交互（聊天、发送表情回应）。
    *   内置一个强大的 **WebSocket客户端**，处理与后端的所有实时信令交互。
    *   包含一个 **同步控制器 (Sync Controller)** 模块，负责解析服务端的同步指令，并精确控制本地播放器的行为（播放、暂停、跳转），同时处理本地网络抖动，追赶或等待主机进度。

2.  **接入层：长连接网关 (Persistent Connection Gateway)**
    *   **技术选型**：使用Go（如 `gorilla/websocket`）或Erlang/Elixir构建的专用WebSocket网关集群。
    *   **核心职责**：
        *   **连接管理**：维持与海量客户端的WebSocket长连接，处理心跳检测、断线重连。这是系统的有状态部分。
        *   **身份认证**：在连接建立时，通过Token验证用户身份。
        *   **消息路由**：维护一个 `connection_id -> user_id, room_id` 的映射（可存在Redis中），根据 `room_id` 将消息精确路由到后端的业务服务。

3.  **核心业务层："一起看"服务 (Watch-Together Service)**
    *   **技术选型**：无状态的Go微服务集群，部署在K8s上，可水平扩展。
    *   **核心职责**：
        *   **房间管理**：处理创建、加入、离开、解散房间的HTTP API请求。
        *   **业务编排**：作为"指挥官"，协调其他服务。例如，当用户加入房间时，它会调用状态同步服务，将该用户订阅到房间的频道。
        *   **主持人（Host）管理**：处理主持人权限的转移逻辑。

4.  **核心引擎：状态同步服务 (State Sync Service)**
    *   **技术选型**：基于Redis的Pub/Sub或一个专门的低延迟消息队列（如NATS）构建。这是整个系统的"心脏"。
    *   **核心职责**：
        *   **状态广播**：接收来自主持人（Host）的播放控制信令（如播放、暂停、进度跳转），并以极低的延迟广播给房间内的所有其他成员。
        *   **状态记录**：在Redis中实时记录每个房间的"权威状态"，例如 `{ "status": "playing", "progress": 125.4, "server_timestamp_ms": 1678886400123 }`。`server_timestamp_ms` 至关重要，它允许客户端计算延迟并进行精确追赶。

5.  **互动系统：实时IM服务 (Real-time IM Service)**
    *   这是一个成熟、独立的系统，负责处理房间内的聊天消息、表情回应等。
    *   "一起看"服务只是调用IM服务的API，将聊天消息推送到指定的房间频道。

6.  **基础服务**
    *   **视频服务/CDN**："一起看"不传输视频流本身。所有客户端仍然通过现有的视频服务和CDN获取视频内容。我们同步的只是播放行为。
    *   **持久化数据库 (MySQL/TiDB)**：存储房间的基础信息、邀请码等非易变数据。

#### ab. 通信协议分工：HTTP API 与 WebSocket 信令

为了职责清晰和性能最优化，我们将通信方式明确分为两类：

*   **HTTP API (RESTful)**: 用于处理所有 **"事务性"**、**"非实时广播"** 的操作。这类操作的特点是客户端发起请求，服务端处理后返回一个明确的结果，通常不涉及通知房间内的其他用户。
    *   **场景**: 创建房间、通过邀请链接/码加入房间、解散房间、主动离开房间、获取房间信息和成员列表。

*   **WebSocket 信令**: 用于处理所有 **"实时"**、**"状态同步"**、**"广播性"** 的操作。这类操作需要以极低的延迟触达房间内的所有（或部分）成员。
    *   **场景**: 主持人播放/暂停/进度跳转的同步指令、房间内聊天消息、表情互动、用户加入/退出房间的实时通知（用于UI更新）、主持人权限变更的通知。

这样的划分使得无状态的HTTP服务可以轻松地水平扩展来应对高并发的房间创建/加入请求，而有状态的WebSocket网关则专注于维护长连接和高效广播，架构更加清晰健壮。

#### b. 核心难点与技术选型

1.  **播放状态的精确、低延迟同步**
    *   **挑战**：这是用户体验的生命线。网络延迟、客户端性能差异都可能导致音画不同步。
    *   **解决方案**：
        *   **主持人驱动模型 (Host-Driven Model)**：房间内有且仅有一个主持人（通常是创建者），他的播放器是所有人的"标准时间"。所有控制事件（播放/暂停/快进）都由主持人发起。
        *   **带服务器时间戳的信令**：主持人的任何操作，发送到服务端时，状态同步服务会打上当前的服务器时间戳，然后广播出去。
        *   **客户端智能校准**：其他客户端收到同步指令后，比如`{ "command": "SEEK", "progress": 300, "server_timestamp_ms": ... }`，它会用 `当前本地时间 - server_timestamp_ms` 计算出信令传输的延迟。然后，它会计算出目标进度：`目标进度 = 300 + 延迟(秒)`。客户端的同步控制器会平滑地将播放器调整到这个目标进度，而不是生硬地跳转。对于微小的差异（< 1s），可以通过微调播放速率 (playbackRate) 来追赶，而不是暂停或跳转，体验更好。

2.  **主持人的"单点故障"与可信迁移 (Trusted Host Migration)**
    *   **挑战**: 如果主持人意外离线，如何在其缺位期间保证房间的控制权平-稳、安全地交接，防止恶意用户（"黑粉"）抢夺控制权扰乱观影。
    *   **解决方案**: 放弃纯粹的"用户认领"模式，采用一个更有序、更可控的 **"指定-邀请-确认"** 委托式迁移方案。
        *   **1. 角色预设：共同主持人 (Co-Host)**
            *   **机制**: 房间创建者（主主持人）可以预先指定一个或多个"共同主持人"。共同主持人拥有与主主持人几乎同等的权限（播放、暂停、跳转）。
            *   **优点**: 这是最理想的备份方案。当主主持人掉线，在线的共同主持人可以立即无缝接管，保证观影不中断。
        *   **2. 主持人重连恢复 (Host Reclaim)**
            *   **机制**: 如果主持人（无论是主或共同主持人）短暂掉线后重新连接到房间，系统应自动恢复其主持人身份和权限。
        *   **3. 委托式自动迁移 (Delegated Auto-Migration)**
            *   **场景**: 主持人掉线，且当前 **没有** 任何共同主持人在场。
            *   **流程**:
                *   **a. 全局暂停与锁定**: 系统立即广播 `HOST_DISCONNECTED` 和 `PAUSE` 指令。此时，除了主持人重连外，房间控制权被暂时锁定。
                *   **b. 智能提名**: "一起看"服务根据预设的可靠性策略，在房间成员中 **提名** 一位候选人。提名策略可以综合考虑：`房主的好友关系 > 加入房间时长 > 近期活跃度`。
                *   **c. 私密邀请与确认**: 系统向被提名者发送一条私密消息："主持人已离线，是否愿意临时接管主持？（您随时可以移交权限）[同意] [拒绝]"。
                *   **d. 权限交接**:
                    *   如果用户 **同意**，系统立即向全员广播 `NEW_HOST` 指令，确立新主持。新主持上报状态后，恢复播放。
                    *   如果用户 **拒绝** 或在15秒内无响应，系统会按照提名策略顺延至下一位候选人，重复c步骤。
                *   **e. 最终回退 (Last Resort)**: 如果所有被提名的候选人（例如3位）都拒绝了邀请，系统将向原主持人信任的圈子（如好友、粉丝群）发送通知，或作为最后手段，退回至"邀请认领"模式，但会在UI上明确提示风险，并赋予成员"投票踢出"的权利。
    *   **优点**:
        *   **安全性**: 彻底杜绝了"黑粉"通过抢占方式成为主持人的可能，将控制权始终维持在可信的用户手中。
        *   **尊重用户意愿**: "邀请-确认"机制避免了强行指派给一个不情愿的用户。
        *   **体验流畅**: 流程自动化，最大程度减少了房间的"失控"时间。

3.  **大规模连接管理与状态一致性**
    *   **挑战**：如何管理百万、千万级的WebSocket连接，并保证每个房间内用户看到的状态是一致的。
    *   **解决方案**：
        *   **专业网关层**：将连接管理（有状态）和业务逻辑（无状态）分离。Go实现的网关可以轻松管理大量连接，而无状态的业务服务可以无限水平扩展。
        *   **Redis作为核心状态存储**：使用Redis Cluster来存储所有房间的实时状态和用户列表。
            *   `HASH`: `room:info:<room_id>` -> `{ "host": "user_a", "video_id": "v123", ... }`
            *   `SET`: `room:members:<room_id>` -> `{ "user_a", "user_b", "user_c" }`
            *   `STRING`: `room:state:<room_id>` -> `'{ "status": "playing", ... }'`
            *   `Pub/Sub`: 用于广播房间内的状态变化。当网关A上的用户A（主持人）操作时，消息发布到`room:events:<room_id>`频道，所有订阅了该频道的网关（B、C、D）都能收到消息，并推送给各自连接的用户。

#### c. 高可用与扩展性

*   **分层无状态化**：除了网关层，所有后端服务（一起看服务、IM服务）都设计为无状态，可以随时增删节点，通过K8s的HPA进行弹性伸缩。
*   **依赖解耦**：服务间通过RPC（gRPC）或消息队列通信，避免紧耦合。
*   **数据库高可用**：MySQL使用主从复制+读写分离。核心状态存储Redis使用哨兵(Sentinel)或集群(Cluster)模式保证高可用。
*   **容灾与降级**：
    *   **IM服务故障**：聊天功能不可用，但核心的视频同步功能必须不受影响。
    *   **状态同步服务故障**：这是最严重的情况。可以降级为"仅提醒"模式，即客户端收到"主持人已暂停"的系统消息，但需要用户手动操作，保证核心观影体验不中断。当服务恢复后，再发送全量状态进行一次强制同步。

#### d. 安全考虑

虽然不像榜单系统那样有强烈的作弊动机，但仍需考虑：
*   **身份验证**：所有WebSocket连接和API请求都必须经过严格的身份验证。
*   **信令风控**：在状态同步服务层增加简单的规则校验，例如一个用户在1秒内发送超过10次`SEEK`指令，视为异常行为并暂时忽略该用户的指令，防止恶意用户通过脚本攻击房间。
*   **邀请机制**：通过有时效性、一次性的邀请码或链接来控制房间的加入，防止被爬虫扫描和无关人员进入。
