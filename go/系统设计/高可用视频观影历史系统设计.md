### 系统设计题："腾讯视频高可用观影历史系统"

这是一个典型的高写负载、读写模式特殊的系统。其核心挑战在于如何用合适的架构和存储模型，平衡海量实时写入、数据一致性、存储成本和快速读取的需求。

#### a. 整体架构

我将系统分为数据上报、接入与缓冲、处理与存储、查询服务这几个核心层次。

1.  **客户端 (Client)**
    *   **上报时机**：
        *   播放开始时：上报一次，标记视频为"正在观看"。
        *   播放过程中：**每30秒**定期上报一次当前播放进度。
        *   播放暂停/退出时：立即上报一次，确保最新的进度被记录。
        *   播放完成时：上报完成状态。
    *   **上报内容**：`{user_id, video_id, series_id, position, duration, event_type, client_timestamp, device_id}`

2.  **接入与缓冲层 (Ingestion & Buffering)**
    *   **API网关**: 承载所有客户端请求，负责鉴权、负载均衡。
    *   **消息队列 (Message Queue)**: **Apache Kafka** 是这里的最佳选择。
        *   **作用**：这是整个系统的关键。它将前端的高并发写入请求与后端的处理逻辑完全解耦。即使后端处理短暂延迟或故障，数据也能暂存在Kafka中，保证不丢失。它通过削峰填谷，将瞬时的写入洪流平滑地交由后端消费。
        *   **Topic设计**: 可以设计一个主Topic `playback_history_events`，使用 `user_id`作为partition key，确保同一个用户的上报事件被同一个消费者实例顺序处理。

3.  **处理与存储层 (Processing & Storage)**
    *   **实时计算/消费服务**:
        *   部署一个由Go编写的消费者集群（或使用Flink/Kafka Streams），订阅Kafka中的历史记录Topic。
        *   **核心逻辑**：对数据进行清洗、校验，并执行**写前合并**。例如，在一个短时间窗口（如1分钟）内，一个用户对同一个视频的多次进度上报，只取时间戳最新的那条写入主存储，极大地降低对存储层的写压力。
    *   **存储选型 (Hybrid Storage)**:
        *   **主存储 (在线历史)**: **Apache HBase** 或 **Cassandra**。
            *   **理由**：观影历史数据量极其庞大（百亿甚至千亿级别），且写入频繁。关系型数据库（如MySQL分库分表）难以水平扩展且成本高昂。HBase这类宽列存储引擎为这种写密集型、按行键（Row Key）快速查询的场景而生。
            *   **数据模型见下一节 b 部分**。
        *   **缓存层 (断点续播与热数据)**: **Redis Cluster**。
            *   **作用**：提供极低延迟的"断点续播"信息和缓存用户最近的少量历史记录。
            *   **数据结构**:
                *   断点续播: `HASH`, Key: `resume:{user_id}`, Field: `video_id`, Value: `position:timestamp`。
                *   历史首页缓存: `ZSET`, Key: `history:latest:{user_id}`, Member: `video_id`, Score: `last_watched_timestamp`。只缓存最近的200条记录。
        *   **数据湖/归档存储**: **Hive/Iceberg on COS/HDFS**。
            *   **作用**：HBase中只保留例如最近2年的在线历史。更早的数据通过离线任务（Spark）定期从HBase迁移到对象存储中，用于离线分析、模型训练和司法取证等。

4.  **查询服务层 (Query Service)**
    *   一个无状态的Go语言编写的API服务集群。
    *   `/api/v1/history` (获取历史列表):
        1.  优先尝试从Redis缓存（ZSET）中获取。
        2.  缓存未命中，则查询HBase，获取用户最新的N条记录。
        3.  将查询结果写回Redis缓存，并返回给用户。
    *   `/api/v1/resume` (获取断点续播): 直接从Redis的HASH中读取指定`video_id`的进度，提供毫秒级响应。

#### b. 存储模型深度设计 (HBase)

HBase的Schema设计是性能的关键。

*   **表名**: `user_watch_history`
*   **RowKey设计**: `md5(user_id).substring(0, 4) + user_id`
    *   直接用`user_id`做RowKey会导致热点问题（如果ID是顺序递增的）。通过在`user_id`前加上其哈希值的前几位作为"盐"，可以有效将数据散列到HBase集群的不同Region Server上。
*   **Column Family**: `h` (history)
*   **Column Qualifier (列名)**: `video_id`
*   **Value**: 使用Protobuf序列化的二进制数据，包含`{position, duration, last_watched_timestamp, device_id}`。
*   **查询最新N条记录的挑战与优化**:
    *   上述设计在查询一个`user_id`的所有历史时很高效，但要"获取最新的N条"则需要扫描该用户行下的所有列，然后在服务端排序，效率低下。
    *   **优化方案**: 引入第二张表或改变设计。一个更优的设计是：
        *   **RowKey**: 依然是 `md5(user_id).substring(0, 4) + user_id`。
        *   **Column Qualifier**: `Long.MAX_VALUE - last_watched_timestamp`。这是一个经典技巧，通过用长整型的最大值减去时间戳，使得最新的记录在字典序上排在最前面。
        *   **Value**: `video_id`。
        *   **这样，获取最新的N条记录就变成了一个对该行进行`scan`操作并`limit N`即可，速度极快。** 播放进度等详细信息可以存为另一列，或在需要时再去第一张表查询。

#### c. 核心挑战与解决方案

1.  **写放大与无效写入**
    *   **问题**：用户观看一集电视剧（45分钟），每30秒上报一次，会产生90次请求，大部分是冗余的进度更新。
    *   **解决方案**：**处理层前置合并**。如上文所述，利用Flink或自定义的消费者服务，在写入HBase前，基于`user_id:video_id`对短时间窗口内的数据进行聚合，只保留最新的状态，可以将写入QPS降低1-2个数量级。

2.  **多端并发与数据一致性**
    *   **问题**：用户在PC上看到15分钟，暂停；几乎同时在手机上从头开始看。如果PC的上报延迟到达，可能会覆盖手机端的新进度。
    *   **解决方案**：**基于客户端时间的乐观锁**。在写入存储时，必须采用"Last-Write-Wins"策略，但依据的是**客户端上报的时间戳 (`client_timestamp`)**，而非消息处理时间。
        *   HBase的`CheckAndPut`原子操作是实现此逻辑的利器：`仅当数据库中记录的时间戳 < 本次上报的时间戳时，才执行更新操作`。这保证了旧数据不会覆盖新数据。

3.  **缓存一致性**
    *   **问题**：更新HBase后，如何保证Redis缓存的数据也是最新的？
    *   **解决方案**：**Cache-Aside Pattern + Invalidation**。
        *   写操作：消费者服务在成功写入HBase后，**直接删除(invalidate)** Redis中对应的缓存键（`history:latest:{user_id}`）。
        *   读操作：查询服务访问时若发现缓存不存在（Miss），则从HBase加载数据，然后重新填充（Populate）缓存。
        *   删除缓存而非更新缓存，可以避免更复杂的数据同步问题。

#### d. 高可用与高并发

*   **无单点**：整个链路，从API网关、Kafka集群、消费者集群、Redis集群到HBase集群，所有组件都以集群模式部署，没有单点故障。
*   **弹性伸缩**：API服务和消费者服务都运行在K8s上，配置HPA，可以根据CPU或Kafka消息积压情况自动扩缩容。
*   **容灾降级**：
    *   当HBase故障时，写入可以暂存在Kafka中（可配置数小时甚至数天的保留期）。
    *   当HBase查询服务故障，可以降级为只提供Redis中的缓存数据，保证核心的断点续播和热门历史可用。
    *   设置异地灾备，通过HBase的Replication机制和Kafka的MirrorMaker实现数据的跨数据中心同步。

#### e. 与关联系统的交互

观影历史是公司的数据金矿，是许多上层业务的驱动力。

*   **推荐系统**: 完整的观影历史（特别是从数据湖中获取的全量、长期历史）是训练用户兴趣模型最重要、最关键的特征来源。
*   **用户画像系统**: 根据用户观看的内容、时长、频率、完成度等，为用户打上各种标签，如"美剧爱好者"、"体育迷"、"深夜党"等。
*   **会员与营销**: 可以根据用户的观影历史，定向推荐付费内容或相关的会员活动。例如，发现用户看了很多漫威电影，可以向其推送《洛基》第二季上线的消息。

这个设计方案兼顾了成本、性能和可扩展性，能够有效支撑一个大规模视频平台的观影历史功能及其衍生的业务需求。 