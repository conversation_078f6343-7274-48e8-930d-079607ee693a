### 会员与支付系统设计

### 1. 系统概述

本文档旨在设计一个高可用、高并发、数据强一致的会员与支付系统。系统核心功能覆盖用户购买、手动/自动续费、会员升降级等完整生命周期，同时确保与下游业务（如用户、内容、播放服务）的高效、可靠联动，为核心商业化运营提供稳定支撑。

### 2. 整体架构

#### a. 系统架构图

```mermaid
graph TD
    subgraph "客户端 (Client)"
        WebApp[Web/H5]
        MobileApp[Mobile App]
    end

    subgraph "基础设施 (Infrastructure)"
        DB[(MySQL Cluster)]
        Cache[(Redis Cluster)]
        MQ(Kafka / RocketMQ)
        Scheduler(分布式调度中心<br/>xxl-job)
    end
    
    subgraph "核心服务域 (Core Services)"
        OS[订单服务<br/>Order Service]
        PS[支付服务<br/>Payment Service]
        MS[会员服务<br/>Membership Service]
        RS[续费服务<br/>Renewal Service]
        Promo[优惠服务<br/>Promotion Service]
        NS[通知服务<br/>Notification Service]
    end
    
    subgraph "周边系统 (Downstream Systems)"
        APIGW[API 网关]
        UserCenter[用户中心]
        ContentService[内容服务]
    end

    %% Client Interactions
    WebApp -- HTTP/API --> APIGW
    MobileApp -- HTTP/API --> APIGW
    APIGW -- RPC --> OS
    APIGW -- RPC --> MS

    %% Core Service Interactions
    OS -- RPC --> Promo
    OS -- RPC --> PS
    PS -- "异步回调" --> PS
    PS -- "支付成功事件" --> MQ
    
    MQ -- "订阅支付事件" --> OS
    MQ -- "订阅支付事件" --> MS
    
    MS -- "注册/取消续费任务" --> Scheduler
    MS -- "会员状态变更事件" --> MQ
    Scheduler -- "触发扣款任务" --> RS
    RS -- RPC --> OS
    
    %% Downstream Sync
    MQ -- "订阅会员状态" --> UserCenter
    MQ -- "订阅会员状态" --> ContentService
    MQ -- "订阅会员状态" --> APIGW
    
    %% Data Stores
    OS & PS & MS & RS & Promo & NS -- CRUD --> DB
    OS & PS & MS & RS & Promo & NS -- Cache --> Cache
```

#### b. 核心服务域

我会将整个系统拆分为多个高内聚、低耦合的微服务，通过消息队列和RPC进行通信，确保系统的可扩展性和可维护性。

1.  **订单服务 (Order Service)**：作为交易的入口和状态机。负责创建订单、管理订单生命周期（待支付、已支付、已取消、已退款）、计算优惠、记录交易快照。
2.  **支付服务 (Payment Service)**：聚合所有第三方支付渠道（微信支付、支付宝、Apple IAP等）。负责与网关交互生成支付凭证、接收异步回调通知、主动查询支付状态。它对外屏蔽了多渠道支付的复杂性。
3.  **会员服务 (Membership Service)**：管理用户的会员身份和权益。核心职责是根据支付成功的消息，为用户精确地开启或延长会员有效期，并管理用户的签约状态（是否开启自动续费）。
4.  **续费服务 (Renewal Service)**：专门处理自动续费签约和执行。它会管理用户的签约协议，并在会员到期前，通过支付服务发起静默扣款。
5.  **优惠服务 (Promotion Service)**：独立管理优惠券、折扣活动、会员折扣等，为订单服务提供价格计算支持。
6.  **通知服务 (Notification Service)**：负责在关键节点（支付成功、续费提醒、扣款失败等）通过Push、短信、站内信等方式触达用户。

#### c. 基础设施

*   **数据库**：核心交易和会员信息使用 `MySQL` 集群（采⽤InnoDB引擎），并按 `user_id` 进行分库分表，保证事务的ACID特性和水平扩展能力。
*   **缓存**：使用 `Redis` Cluster，用于缓存用户的会员状态、热门商品信息，以及实现分布式锁，提升性能和处理并发。
*   **消息队列**：选用 `Apache Kafka` 或 `RocketMQ`。用于服务间的异步解耦，实现最终一致性。例如，支付成功后，由支付服务发出消息，订单和会员服务订阅该消息来更新各自的状态。
*   **分布式调度中心**：对于自动续费场景，我会引入类似 `xxl-job` 的分布式调度中心，或基于时间轮算法自研一个轻量级调度器，来管理千万级的续费任务。


### 3. 核心流程与API设计

#### a. 用户首次开通/手动续费流程

1.  **创建订单**：用户在客户端选择商品（如"1个月VIP"），请求后端。订单服务创建一条状态为`待支付`的订单，并调用优惠服务核算最终价格。
2.  **获取支付凭证**：订单服务请求支付服务，支付服务根据用户选择的渠道（如微信支付）向微信支付网关请求，获取预支付交易会话标识`prepay_id`，并将其返回给客户端。
3.  **用户支付**：客户端调起支付SDK，用户输入密码完成支付。
4.  **异步回调**：微信支付服务器将支付结果通过HTTP回调接口通知到我们的支付服务。
5.  **关键：回调处理与核验**：
    *   **验签**：必须用预先配置的密钥验证回调请求的签名，防止伪造。
    *   **幂等处理**：以`微信支付订单号`作为唯一键。处理前先查询该订单号是否已处理，若已处理，直接返回成功，防止因网络重试导致重复记账。
    *   **主动查询**：收到回调后，不能完全信任其内容。应立即调用支付网关的"查询订单"接口，反向确认该笔订单的最终状态，以此为准。
6.  **通知下游**：状态确认无误后，更新支付订单状态为`成功`，然后通过Kafka发送一条`支付成功`的事件。该事件内容需要足够丰富，包含`user_id`, `order_id`, `amount`, `product_id`等。
7.  **更新业务状态**：会员服务和订单服务都订阅此Topic。会员服务消费消息后，为用户延长会员有效期；订单服务则将订单状态更新为`已完成`。

#### b. 自动续费流程

1.  **用户签约**：用户在首次开通时勾选"自动续费"，支付成功后，支付网关（如微信）会返回一个`签约协议号 (contract_id)`。支付服务需要将此`contract_id`与我们的`user_id`绑定并持久化。
2.  **注册调度任务**：会员服务在收到签约成功的消息后，计算出下一个扣费日期（如会员到期前1天），然后调用分布式调度中心，注册一个以`user_id`为唯一标识的、在指定日期执行的扣费任务。
3.  **任务触发**：调度中心在预定时间精准触发任务，调用续费服务。
4.  **执行扣款**：
    *   续费服务先进行一系列前置检查：用户是否已主动取消续费？会员状态是否仍然有效？
    *   检查通过后，调用订单服务创建一张`自动续费`类型的订单。
    *   然后调用支付服务，使用之前保存的`contract_id`，向支付网关发起"协议支付/免密支付"请求。
5.  **处理扣款结果**：后续流程与手动支付类似。支付服务接收异步回调，核验状态，然后发布`支付成功`或`支付失败`的事件。
6.  **失败与重试**：如果扣款失败（如用户账户余额不足），支付服务会发出`支付失败`事件。续费服务收到后，可以配置一个重试策略，例如，在接下来的3天内，每天重试一次。多次失败后，则标记该用户的自动续费协议为`已暂停`，并通过通知服务提醒用户。

#### c. 会员升降级流程

当用户已经是月度会员，想升级到年度会员时，流程会更复杂。

1.  **前端询价**：前端向订单服务发起询价请求，告知用户当前会员状态和目标升级产品。
2.  **差价计算**：订单服务需要调用会员服务，获取用户当前会员的剩余价值（按天折算），然后用目标产品的价格减去剩余价值，计算出需要补的差价。
3.  **创建升级订单**：创建一个类型为`UPGRADE`的订单，`payable_amount`为计算出的差价。
4.  **支付与权益变更**：后续支付流程与新购一致。支付成功后，会员服务收到消息，将用户的会员类型更新为`年度会员`，并根据新购时长`延长`有效期（注意：不是覆盖，而是在原有效期基础上延长，或者直接设置新有效期为一年后，具体策略由产品定义）。降级通常不允许直接操作，一般会引导用户关闭自动续费，等当前会员过期后，再购买新的低等级会员。

#### d. 核心API定义 (示例)

*   **订单服务 (`Order Service`)**
    *   `POST /v1/orders`: 创建订单
        *   Request: `{ "user_id": "xxx", "product_id": "monthly_vip", "coupon_id": "xxx" }`
        *   Response: `{ "order_id": "xxx", "payable_amount": 9.9, "payment_id": "xxx" }`
    *   `GET /v1/orders/{order_id}`: 查询订单详情
*   **支付服务 (`Payment Service`)**
    *   `POST /v1/payments/credential`: 获取支付凭证
        *   Request: `{ "payment_id": "xxx", "channel": "wechat_app" }`
        *   Response: `{ "prepay_id": "...", "nonce_str": "...", ... }`
    *   `POST /v1/callbacks/{channel}`: 接收支付渠道异步回调
*   **会员服务 (`Membership Service`)**
    *   `GET /v1/memberships/{user_id}`: 查询用户会员信息
    *   `POST /v1/memberships/actions/renew`: (内部接口) 触发会员续期
        *   Request: `{ "user_id": "xxx", "order_id": "xxx" }`

### 4. 数据库设计 (示例)

**订单表 (`orders`)**

```sql
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) NOT NULL COMMENT '订单唯一ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `product_id` varchar(50) NOT NULL COMMENT '商品ID (如 monthly_vip)',
  `product_description` varchar(255) DEFAULT NULL COMMENT '商品描述',
  `original_amount` decimal(10,2) NOT NULL COMMENT '原价',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `payable_amount` decimal(10,2) NOT NULL COMMENT '应付金额',
  `status` tinyint(4) NOT NULL COMMENT '订单状态: 1-待支付, 2-已支付, 3-已取消, 4-已关闭',
  `order_type` tinyint(4) NOT NULL COMMENT '订单类型: 1-新购, 2-续费, 3-升级, 4-降级',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_user_id_status` (`user_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

**支付单表 (`payments`)**

```sql
CREATE TABLE `payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` varchar(64) NOT NULL COMMENT '支付单唯一ID',
  `order_id` varchar(64) NOT NULL COMMENT '关联的订单ID',
  `user_id` bigint(20) unsigned NOT NULL,
  `transaction_id` varchar(128) DEFAULT NULL COMMENT '第三方支付流水号',
  `payment_channel` tinyint(4) NOT NULL COMMENT '支付渠道: 1-微信, 2-支付宝, 3-Apple IAP',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `status` tinyint(4) NOT NULL COMMENT '支付状态: 1-处理中, 2-成功, 3-失败',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付成功时间',
  `channel_callback_data` text COMMENT '渠道回调原始数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_id` (`payment_id`),
  UNIQUE KEY `uk_transaction_id_channel` (`transaction_id`, `payment_channel`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付单表';
```

**会员信息表 (`memberships`)**

```sql
CREATE TABLE `memberships` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `membership_type` varchar(50) NOT NULL COMMENT '会员类型 (如 vip, svip)',
  `start_time` timestamp NOT NULL COMMENT '会员开始时间',
  `end_time` timestamp NOT NULL COMMENT '会员结束时间',
  `auto_renew_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '自动续费状态: 0-关闭, 1-开启, 2-暂停',
  `contract_id` varchar(128) DEFAULT NULL COMMENT '第三方签约协议号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员信息表';
```

### 5. 关键技术设计深潜

#### a. 数据一致性保障
这是支付系统的灵魂。我会采用**"事务消息"或"本地消息表（Transactional Outbox）"模式**来保证主流程（如支付->记账->开通会员）的最终一致性。在支付服务侧，`更新支付单状态`和`写入消息到发件箱表`这两个操作会在一个本地事务中完成。然后由一个独立的任务轮询发件箱表，确保消息100%投递到Kafka。这避免了在主事务中进行网络调用，保证了核心库的性能和稳定性。

#### b. 高并发下的幂等性设计
*   **入口层**：在API网关层，对创建订单这类写操作，可以引导客户端生成一个唯一的`request_id`，服务端通过`Redis`的`SETNX`命令检查`request_id`，实现秒级的防重放。
*   **核心服务**：所有接收外部回调、消费MQ消息的接口都必须是幂等的。数据库层面，可以在关键业务字段上建立唯一索引（如支付网关的流水号`transaction_id`），这是保证幂等性的最后一道防线。

#### c. 自动续费调度系统的设计
*   **挑战**：管理上亿级别的待续费用户，如果每天用`SELECT ... WHERE renewal_date = CURDATE()`的方式去扫描数据库，会造成巨大的DB压力。
*   **方案**：采用**多级时间轮算法**。可以想象一个有366个格子的环形队列，每个格子代表一年中的一天。当一个用户的续费任务（例如，`user_id: 123`）在明年3月5日需要执行时，就将这个任务ID放入第64个格子（31+29+5）的链表中。调度程序每天只需要处理当前格子里的任务列表即可，查询复杂度是O(1)，极大地降低了数据库压力。对于任务量巨大的格子，还可以设计二级时间轮（按小时），进一步平滑负载。

#### d. 会员状态与权益的下游同步机制
支付和会员系统作为核心，其状态变更必须高效、可靠地同步给所有依赖这些信息的下游微服务。为此，采用"事件驱动的异步同步"与"网关层实时查询"相结合的方案。

**事件驱动的异步同步 (保证最终一致性)**
1.  **定义标准化的事件**：会员服务作为权威数据源，在任何导致用户会员状态或权益变化的动作完成后（如新购、续费、升级、过期、取消续费），都需要对外发布一个`MembershipStatusChanged`事件到Kafka。
2.  **丰富的事件内容**：该事件的Payload必须包含足够的信息，以便下游服务独立做出判断，而无需回查会员服务。
    ```json
    {
      "event_id": "unique-event-id-for-idempotency",
      "user_id": 12345,
      "timestamp": "2023-10-27T12:00:00Z",
      "change_type": "RENEWAL", // NEW_PURCHASE, UPGRADE, EXPIRATION, CANCELLATION
      "before_status": {
        "membership_type": "vip",
        "end_time": "2023-10-27T11:59:59Z"
      },
      "after_status": {
        "membership_type": "vip",
        "end_time": "2023-11-27T11:59:59Z",
        "auto_renew_status": 1
      }
    }
    ```
3.  **下游服务订阅消费**：
    *   **用户中心服务**：订阅此事件，更新数据库中用于展示的`is_vip`字段和会员等级。
    *   **内容服务**：订阅此事件，更新其内部的权限缓存。
    *   **API网关**：订阅此事件，更新其内存或Redis中的用户权限视图。

**网关层实时查询 (满足实时鉴权)**
对于播放视频这类需要严格实时鉴权的操作，等待异步事件处理完成是不可接受的。通过在API网关构建本地权限缓存，可以毫秒级完成鉴权，流程如下：
1.  用户请求到达API网关。
2.  网关从权限缓存中查询该用户权限。
3.  **命中缓存**：直接在请求头中注入鉴权成功信息，转发给后端服务。
4.  **未命中缓存**：(极小概率) 对会员服务发起同步RPC调用回源，获取最新状态并写入缓存，再继续流程。

#### e. 安全与风控设计
*   **支付防刷**：
    *   **验证码**：在支付前，对于风险较高的操作（如新设备、异地登录），强制要求短信或图形验证码。
    *   **频率限制**：对同一用户ID、同一IP地址的下单频率进行限制。
*   **数据安全**：
    *   **传输加密**：全链路使用 HTTPS/TLS。
    *   **敏感数据加密**：数据库中存储的用户敏感信息（如签约协议号`contract_id`）必须加密存储。
*   **防范伪造回调**：除了验签，还应将支付回调接口的服务器IP加入支付网关的白名单，只接受来自特定IP的请求。
*   **风控引擎**：引入独立的风控引擎服务，在关键节点对用户行为进行实时风险评估。

### 6. 运维保障体系

#### a. 监控与告警
*   **业务核心指标**：
    *   **支付成功率**：(支付成功订单数 / 总支付订单数) * 100%。按渠道、按商品、按时段进行多维度监控。成功率的骤降是最高优的告警。
    *   **订单创建量**：监控订单量的实时波动，发现流量异常。
    *   **回调延迟**：从支付成功到收到回调的平均耗时。
    *   **续费成功率**：自动续费的成功率是商业模式健康度的关键。
*   **系统健康度指标**：各服务的CPU、内存、网络IO、GC情况；数据库连接池使用率、慢查询数量；Kafka消息积压量。
*   **告警设计**：
    *   **分级告警**：P0级告警（如支付成功率断崖式下跌）需要通过电话、短信立即通知到核心负责人。
    *   **On-Call机制**：建立清晰的On-Call排班表和应急预案（SOP）。

#### b. 对账系统
任何支付系统都必须有一个独立的、异步的对账系统。它每天会拉取第三方支付网关的日终账单，与我们系统内的订单、支付记录进行逐条比对。目标是发现并处理掉单（用户付了钱，我们系统没记录）、长款（我们收了钱，但没给用户开服务）、短款（用户没付钱，我们却开了服务）等异常情况，确保资金流和信息流的绝对一致。 