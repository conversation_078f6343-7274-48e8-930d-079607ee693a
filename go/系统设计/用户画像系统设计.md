### 系统设计题："腾讯视频用户画像系统"

#### a. 整体架构

这是一个典型的 Lambda 架构或 Kappa+ 架构的变种，旨在结合离线计算的全面性和实时计算的时效性。

**1. 数据源 (Data Sources):**
*   **实时行为数据**: 用户在客户端的所有行为（播放、暂停、快进、点赞、评论、分享、搜索、VIP购买），通过统一的埋点SDK，带上`trace_id`和`user_id`，实时上报到高可用的 Kafka 消息队列中。
*   **业务数据 (DB Data)**: 用户的注册信息、会员状态、订单历史等存储在 MySQL 或其他业务数据库中。通过 Canal 这类工具订阅其 binlog，将变更数据也实时推送到 Kafka。
*   **内容媒资数据**: 视频的元数据（如标题、分类、标签、演员、导演），同样通过订阅 binlog 的方式进入 Kafka。

**2. 数据计算层 (Computation Layer):**
*   **实时计算 (Speed Layer)**: 我会选择 **Apache Flink**。
    *   **职责**: 负责计算时效性要求高的标签，例如"实时兴趣"（最近10分钟在看科幻片）、"活跃状态"（正在App内活跃）等。
    *   **处理流程**: Flink 消费上述 Kafka 中的多个 Topic，进行多流 Join，关联用户行为和内容信息，然后使用短时间窗口（如1分钟、10分钟）进行聚合，快速产出实时标签。
*   **离线计算 (Batch Layer)**: 我会选择 **Spark + Hive** 的技术栈。
    *   **职责**: 负责计算长期的、稳定的、复杂的标签。例如"内容偏好"（历史观看最多的品类）、"流失风险等级"、"潜在付费用户"等。
    *   **处理流程**: 每天或每小时，Spark 作业会读取数据湖（如 HDFS, S3）中 T-1 的全量日志数据，进行深度计算和机器学习模型推理（如用户聚类、协同过滤）。
*   **数据合并与服务层 (Serving Layer)**:
    *   计算结果会统一写入一个高性能的画像存储系统中。
    *   一个独立的 Go 语言编写的 **画像合并服务 (Profile Merge Service)** 负责在查询时，将离线标签和实时标签进行融合，生成最终的、完整的用户画像视图。

**3. 数据存储与服务 (Storage & Serving):**
*   **画像存储 (Profile Store)**: 这是核心。我会选择 **HBase** 或 **Aerospike** 这类高性能、可水平扩展的 NoSQL 数据库。
*   **API 服务**: 一个无状态的 Go 语言编写的 **画像查询API服务集群**，负责从画像存储中读取数据，并提供给上游业务（推荐系统、广告系统、Push系统等）。
*   **数据湖/数仓**: 所有原始日志和中间计算结果会落盘到 HDFS/S3 数据湖，并通过 Hive/ClickHouse 构建数据仓库，用于离线分析和模型训练。

#### b. 核心计算逻辑

**1. 实时计算 (Flink):**
*   **会话窗口 (Session Window)**: 可以用来识别用户的单次连续访问，对于分析用户的短期意图很有帮助。
*   **状态与定时器**: 使用 Flink 的 `KeyedState` (如 `ValueState`, `MapState`) 来存储每个用户的中间状态（如最近观看的3个视频ID）。使用 `Timer` 设定状态的存活时间 (TTL)，实现"短期兴趣"的自动过期。
*   **规则引擎集成**: 将简单的标签生成逻辑配置化，例如"连续7天登录" -> "周活跃用户"，Flink 作业加载这些规则进行计算。

**2. 离线计算 (Spark):**
*   **TF-IDF/Word2Vec**: 对用户观看过的视频标题、简介进行文本分析，提取关键词，计算用户的兴趣向量。
*   **协同过滤 (Collaborative Filtering)**: 基于"物以类聚，人以群分"的思想，通过 ALS (Alternating Least Squares) 等算法计算用户的隐含兴趣。
*   **图计算 (GraphX/Neo4j)**: 构建用户-视频-标签的异构图，通过社区发现、PageRank等算法挖掘用户潜在关联和影响力。
*   **用户分群 (Clustering)**: 使用 K-Means 等聚类算法，将亿级用户自动划分为不同的群体（如"价格敏感型"、"美剧爱好者"、"体育迷"），便于进行群体运营。

#### c. 存储选型

**画像主存储: HBase**
*   **为什么是 HBase?**
    *   **海量存储**: 轻松支持千亿行 * 百万列的数据规模，满足亿级用户、万级标签的存储需求。
    *   **高吞吐写**: 支持从 Flink 和 Spark 同时进行高并发写入。
    *   **稀疏列存储**: 用户画像的标签通常是稀疏的，HBase 可以极大地节省存储空间。
    *   **毫秒级随机读**: 通过 RowKey 进行点查，性能非常高，满足在线服务需求。
*   **Schema 设计:**
    *   **RowKey**: `reverse(user_id)`。对 user_id 进行反转或加盐哈希，避免写入热点。
    *   **Column Family (列簇)**:
        *   `b`: Basic info，存放性别、年龄等基本、不常变的标签。
        *   `bh`: Behavior，存放统计类标签，如"近30日观看时长"、"消费等级"等。
        *   `i`: Interest，存放兴趣类标签，如"偏好_科幻"、"偏好_演员_xxx"等。
    *   **Qualifier (列名)**: `tag_name`，例如 `genre_sci-fi`。
    *   **Value**: `tag_value`，可以是分数值、枚举值或布尔值，统一用 Protobuf 序列化。

**缓存层: Redis Cluster**
*   在 HBase 之前可以架设一层 Redis 缓存，存放最热门用户（如明星、KOL）或最近被访问过的用户的画像，进一步降低延迟，保护后端 HBase。

#### d. 高可用与高性能

*   **全链路高可用**:
    *   **采集层**: Kafka 集群多副本、跨机房部署。
    *   **计算层**: Flink 和 Spark on YARN/K8s，开启 Checkpoint/Failure Recovery 机制，作业失败可自动恢复。
    *   **存储层**: HBase 依赖 HDFS 的多副本保证数据不丢失，依赖 ZooKeeper 进行 Master 选举。Redis 使用哨兵或集群模式。
    *   **服务层**: API 服务是无状态的，可以水平扩展，部署在 K8s 中，通过 HPA 自动伸缩。
*   **API 性能保障**:
    *   **多级缓存**: `本地缓存 (FreeCache/BigCache)` -> `分布式缓存 (Redis)` -> `画像主库 (HBase)`。
    *   **请求合并**: 对于高并发场景，可以使用请求合并技术，将短时间内对同一个 user_id 的多次请求合并为一次对后端的查询。
    *   **服务降级与熔断**: 当后端存储或某个数据源出现问题时，API服务可以降级返回（例如只返回离线标签、返回缓存的旧数据，甚至不返回），并对下游依赖进行熔断，防止雪崩。

#### e. 标签体系与管理

一个好的画像系统离不开科学的标签体系和配套的管理平台。

*   **标签体系**:
    *   **层次化与结构化**: 将标签分为 **属性、行为、兴趣、风险、模型预测** 等几大类，每一类下再进行细分，形成一棵标签树。
    *   **标签命名规范**: `分类_子类_名称_更新频率`，例如 `interest_movie_genre_d` (兴趣-电影-类型-日更)。
*   **标签管理平台**:
    *   **一站式平台**: 为算法、运营、产品同学提供一个可视化的Web平台，用于**标签的申请、定义、审批、查询和下线**。
    *   **元数据管理**: 管理每个标签的元数据，包括：标签ID、名称、业务含义、负责人、更新频率、存储位置、下游消费者等。
    *   **标签生产自动化**: 对于一些基于规则的标签，平台可以提供"配置化"的生产能力。运营同学通过填写表单（例如选择事件、过滤条件、时间窗口），平台后台自动生成并提交一个 Flink/Spark SQL 作业，实现标签的自助上线，极大提升效率。 
### f. 优化与改进方向

在现有坚实的设计基础上，我们可以从以下几个方向进行探索，使系统更具前瞻性、更高效、更智能。

#### 1. 架构演进：走向实时化与一体化

*   **向 Kappa+ 架构演进**: 当前的 Lambda 架构虽然经典，但维护两套（实时/离线）计算逻辑增加了复杂度和不一致的风险。可以考虑：
    *   **统一计算引擎**: 以 Flink 为核心，统一实时和离线计算。离线任务本质上可以看作是对存储在数据湖（如 Kafka/Pulsar 的无限流或 Iceberg/Hudi 表）中的历史数据进行的一次性 Flink 批处理作业。这简化了技术栈，保证了逻辑的一致性（一份代码，两种模式）。
    *   **流式数据湖 (Streaming Lakehouse)**: 引入 **Apache Hudi / Iceberg / Delta Lake** 来构建数据湖。它们为 HDFS/S3 上的数据带来了 ACID 事务、Schema 演变和时间旅行（Time-travel）能力。Flink/Spark 可以直接以流式或批量的方式读写这些表，完全打通湖和仓，使得 "T+1" 的概念逐渐模糊，可以实现 "分钟级" 的离线计算。

#### 2. 存储层升级：专业化与向量化

*   **引入专业特征存储 (Feature Store)**: 当前的 "画像存储" 和 "标签管理平台" 的思想，可以升级为业界领先的 **Feature Store** 理念（如开源的 Feast 或 Tecton）。
    *   **核心价值**: 它不仅仅是存储键值对，更是连接了数据处理（Flink/Spark）和模型应用（推荐/广告）的桥梁。
    *   **功能**:
        *   **统一服务**: 为模型**训练**（离线获取大量样本）和**在线推理**（低延迟获取单用户特征）提供一致的特征视图，避免线上线下特征不一致的典型问题。
        *   **血缘关系**: 自动管理特征的血缘（从原始数据到计算逻辑再到最终特征值），便于追踪、调试和评估特征价值。
        *   **特征注册与发现**: 提供中心化的特征目录，便于团队协作和特征复用。

*   **引入向量数据库 (Vector Database)**:
    *   **场景**: 对于通过深度学习模型（如 Word2Vec, BERT）产生的用户和内容的 **Embedding/向量化** 兴趣标签，传统的 KV 存储只能根据 ID 点查，无法发挥其最大价值。
    *   **方案**: 引入 **Milvus, Pinecone** 或 **Weaviate** 等向量数据库。它们专门用于高效存储和检索海量向量数据。
    *   **应用**: 可以实现更高级的功能，如"寻找与高价值用户品味相似的潜在用户"（Lookalike）、"根据用户实时观看的内容，推荐向量空间上最接近的视频"等，极大提升推荐和广告的精准度。

#### 3. 计算与智能：实时化与可观测性

*   **在线机器学习 (Online Machine Learning)**:
    *   将部分机器学习模型（如 LR, FTRL）的**训练和推理过程直接嵌入到 Flink 流处理作业中**。模型可以实时地根据用户行为流进行更新，并立刻应用到新的数据上，实现毫秒级的 "模型自适应"。这对于反欺诈、实时出价（RTB）等场景至关重要。

*   **数据质量与可观测性 (Data Quality & Observability)**:
    *   **数据契约 (Data Contract)**: 在 Kafka Topic 的生产者和消费者之间强制实施 **Schema Registry (如 Confluent Schema Registry)**。任何不符合预定义 Schema 的数据都无法写入，从源头保证数据质量。
    *   **标签健康度监控**: 建立一个独立的监控系统，持续监控标签的**新鲜度、覆盖率、空值率、数值分布**等，一旦出现异常（如某个标签超过2小时未更新），立刻告警。
    *   **A/B 测试框架**: 不仅对应用功能做 A/B 测试，也应该对**画像标签本身做 A/B 测试**。例如，上线一个新算法生成的 "用户购买意愿" 标签，可以先让它在小部分流量上生效，通过对比核心业务指标（如转化率），来科学地评估新标签带来的实际业务价值。

#### 4. 服务与消费：更灵活、更具韧性

*   **采用 GraphQL API**:
    *   **背景**: 画像系统的下游消费者众多（推荐、广告、搜索、运营...），每个业务方需要的标签组合都不同。RESTful API 要么需要为每个场景定制接口，要么返回大而全的宽表，造成浪费。
    *   **优势**: GraphQL 允许客户端**按需声明所需的数据字段**。消费方可以精确地只请求它们需要的标签，减少网络开销，提高前端响应速度。服务端也无需再为不断变化的需求频繁改动接口。

#### 5. 治理与效能：自动化与价值驱动

*   **强化标签治理与生命周期管理**:
    *   在标签管理平台中，除了元数据，必须加入**成本 (Cost) 和价值 (Value) 的评估**。
    *   **成本**: 一个标签的生产，占用了多少计算和存储资源？可以量化为 CPU 小时和存储 GB。
    *   **价值**: 这个标签被多少下游业务使用？在这些业务的 A/B 测试中，是否证明能带来正向收益？
    *   **自动化下线**: 基于上述成本和价值分析，可以建立**自动化流程**，定期识别并下线那些 "高成本、低价值" 或 "无人使用" 的僵尸标签，实现资源的有效循环。 