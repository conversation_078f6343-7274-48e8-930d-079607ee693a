**系统设计题："腾讯视频内容版权保护与数字版权管理(DRM)系统"**

### a. 整体架构

这是一个横跨内容生产、分发、播放全链路的安全系统，我把它分为"离线处理"和"在线服务"两大部分。

1.  **离线加密流程**：内容上传后，在转码环节对视频进行加密。
    *   **触发**：视频内容成功上传并通过审核，进入转码流水线。
    *   **核心组件**：转码系统、加密服务、密钥管理系统（KMS）。
    *   **流程**：
        1.  转码系统将视频切片成符合HLS或DASH标准的TS/MP4文件。
        2.  转码系统调用**加密服务**，请求对这些文件进行加密。
        3.  **加密服务**向**KMS**为该内容请求一个内容加密密钥（Content Encryption Key, CEK）和一个密钥ID（Key ID, KID）。
        4.  **KMS**生成CEK和KID，将`{KID -> CEK}`的映射关系加密后持久化存储，并将KID返回给加密服务。
        5.  **加密服务**使用标准的通用加密方案（CENC, Common Encryption），用CEK对视频切片进行加密。
        6.  转码系统更新媒体描述文件（HLS的`.m3u8`或DASH的`.mpd`），在其中写入加密信息，包括KID和许可证服务器（License Server）的URL。
        7.  加密后的视频文件和更新后的媒体描述文件被推送到CDN。

2.  **在线解密与播放流程**：用户点击播放时，客户端获取许可证，解密并播放视频。
    *   **触发**：用户在客户端（App、Web）点击播放受DRM保护的视频。
    *   **核心组件**：客户端播放器、业务后端、DRM许可证服务器。
    *   **流程**：
        1.  客户端从CDN拉取`.m3u8`或`.mpd`文件。
        2.  播放器解析文件，发现是加密内容，提取出KID和许可证服务器URL。
        3.  **播放器首先调用业务后端**，进行用户身份认证和鉴权（例如：检查是否为VIP会员、是否已购买该影片）。
        4.  **业务后端**鉴权通过后，生成一个包含用户ID、设备信息、权限、KID等信息的**短期时效性Token**（如JWT），返回给客户端。
        5.  客户端向**许可证服务器**发起获取许可证的请求，请求中会携带DRM系统特有的挑战（Challenge）信息和上一步获取的Token。
        6.  **许可证服务器**收到请求后，首先**校验Token的合法性**（签名、时效性、权限等）。
        7.  校验通过后，使用请求中的KID向**KMS**查询对应的CEK。
        8.  **KMS**返回CEK给许可证服务器。
        9.  许可证服务器根据不同的DRM平台（Widevine/FairPlay/PlayReady），将CEK包装成一个加密的许可证响应（License Response），并发送回客户端。这个响应本身是被DRM平台的非对称加密体系保护的。
        10. 客户端播放器在**可信执行环境（TEE）**或系统级的安全模块中，用私钥解开许可证，拿到CEK。
        11. 播放器使用CEK**逐帧实时解密**视频数据流，送入解码器进行播放。整个解密过程在安全内存中进行，应用层无法访问到解密后的数据。

### a.1. 核心流程图

```mermaid
graph TD
    subgraph 离线处理: 内容加密
        A["内容上传/转码"] --> C("转码系统");
        C -- "1. 对视频进行切片" --> C;
        C -- "2. 调用加密服务" --> D("加密服务");
        D -- "3. 向KMS请求内容密钥(CEK)和密钥ID(KID)" --> E("密钥管理系统 (KMS)");
        E -- "4. 返回KID" --> D;
        D -- "5. 使用CEK加密视频切片" --> F("加密后的视频");
        C -- "6. 更新媒体描述文件(m3u8/mpd)<br>写入KID和许可证服务器URL" --> G("媒体描述文件");
        F --> H((CDN));
        G --> H((CDN));
    end

    subgraph 在线服务: 解密播放
        P("用户点击播放") --> Player("客户端播放器");
        Player -- "1. 从CDN拉取媒体描述文件" --> H;
        Player -- "2. 解析文件, 发现加密<br>提取KID和许可证服务器URL" --> Player;
        Player -- "3. 向业务后端请求授权" --> Backend("业务后端");
        Backend -- "4. 校验用户身份和权限(如VIP)<br>生成含权限的短期Token" --> Backend;
        Backend -- "5. 返回Token给客户端" --> Player;
        Player -- "6. 携带Token和设备信息<br>请求许可证" --> LicenseServer("DRM许可证服务器");
        LicenseServer -- "7. 校验Token" --> LicenseServer;
        LicenseServer -- "8. 使用KID向KMS查询CEK" --> E;
        E -- "9. 返回CEK" --> LicenseServer;
        LicenseServer -- "10. 将CEK包装成加密的许可证" --> LicenseServer;
        LicenseServer -- "11. 返回许可证给客户端" --> Player;
        Player -- "12. 在可信执行环境(TEE)中<br>解密许可证, 拿到CEK" --> Player;
        Player -- "13. 使用CEK逐帧解密视频并播放" --> V("观看视频");
    end
```

### a.2. 流程文字描述

#### 1. 离线内容加密流程 (图上半部分)
这个流程在用户上传视频后、视频可被观看前完成。
1.  **内容处理**：视频上传后，进入转码流水线，被切割成符合网络播放标准（如HLS）的视频片段。
2.  **请求加密**：转码系统为这部视频向**加密服务**发起加密请求。
3.  **获取密钥**：加密服务向**密钥管理系统(KMS)** 请求一个用于加密视频的**内容密钥(CEK)** 和其对应的**密钥ID(KID)**。KMS是整个安全体系的核心，负责生成和安全存储所有密钥。
4.  **执行加密**：加密服务使用获取到的CEK，对每一个视频片段进行加密。
5.  **更新清单**：加密完成后，转码系统会更新视频的播放描述文件（比如 `.m3u8` 文件），在里面注明该视频是加密的，并记录下之前获取的**KID**和**许可证服务器的地址**。
6.  **内容分发**：最后，将加密后的视频片段和更新后的描述文件一起推送到**CDN**，等待用户播放。

#### 2. 在线解密播放流程 (图下半部分)
这个流程在用户点击播放按钮时实时发生。
1.  **启动播放**：用户在App或网站上点击播放，客户端播放器首先从CDN获取视频的描述文件。
2.  **识别加密**：播放器解析描述文件，发现内容是加密的，并从中提取出KID和许可证服务器地址。
3.  **业务鉴权**：播放器暂停播放，转而请求**业务后端**。这是为了确认用户的身份和权限，例如"该用户是否是VIP会员？"或"他是否已购买这部电影？"。
4.  **获取令牌(Token)**：业务后端验证通过后，会生成一个有时效性、包含用户权限信息的**临时授权Token**，并返回给客户端。
5.  **请求许可证**：客户端拿着这个Token，向**DRM许可证服务器**发起请求，申请用于解密的"许可证"（其中包含内容密钥CEK）。
6.  **下发许可证**：许可证服务器首先**校验Token的合法性**，确认无误后，用KID向**KMS**查询到对应的CEK，然后将CEK用特定于设备的方式（如Widevine, FairPlay）加密包装成许可证，再返回给客户端。
7.  **安全解密**：客户端的播放器在系统的**可信执行环境(TEE)**（一个安全的硬件隔离区）内，解开许可证，拿到关键的CEK。
8.  **实时播放**：播放器使用CEK，**逐帧、实时地**解密从CDN下载的视频数据流，然后送去解码和播放。整个解密过程都在安全环境中完成，应用层无法触碰到解密后的视频数据，从而保障了版权安全。

### b. 关键流程设计

1.  **内容加密流程**：
    *   **标准化**：必须采用**CENC（Common Encryption）**标准。这使得我们只需要加密和存储一份视频文件，就可以同时被支持CENC的多个DRM系统（Widevine, PlayReady）使用，极大节省了存储和CDN成本。FairPlay虽然有自己的加密方式（AES-CBC），但也可以通过信令转换兼容。
    *   **原子性与幂等性**：加密是转码流水线的一环，需要保证其操作的原子性。如果失败需要能安全回滚。同时，对同一个视频的加密请求应该是幂等的，重复请求不会产生新的密钥或副本。
    *   **密钥粒度**：通常情况下，一部电影或一集电视剧的所有码率和清晰度共用一个CEK，这样可以简化管理和客户端的许可证请求逻辑。

2.  **许可证获取流程**：
    *   **多DRM协议适配**：许可证服务器是整个系统的在线门户，它必须能够处理来自不同平台和浏览器的请求。内部需要有一个**协议适配层**，能够解析Google Widevine、Apple FairPlay、Microsoft PlayReady各自独特的请求/响应格式。
    *   **Token鉴权机制**：这是设计的核心。它将复杂的业务逻辑（用户身份、会员等级、付费状态、地域限制等）与DRM许可证的分发逻辑**解耦**。许可证服务器只负责"认票（Token）不认人"，大大简化了其架构，使其能专注、高效、安全地分发密钥。Token必须是短时效的，并包含Nonce防止重放攻击。

### c. 密钥管理与存储 (KMS)

KMS是安全的核心，它的设计需要遵循"纵深防御"原则。

*   **密钥体系**：
    *   **根密钥 (Root Key)**: 存储在**硬件安全模块 (HSM)** 中，物理隔离，用于最高层级的加密。
    *   **密钥加密密钥 (KEK)**: 由根密钥保护，用于加密CEK。
    *   **内容加密密钥 (CEK)**: 用于直接加密视频内容。
*   **存储方案**：KMS的核心是一个高可用的数据库（如MySQL/PostgreSQL集群），存储KID与被KEK加密后的CEK的对应关系 (`map[KID] -> Encrypted(CEK)`)。绝不允许明文CEK落盘。数据库需要做到多可用区容灾。
*   **服务安全**：KMS服务本身必须部署在隔离的内网环境中，与公网完全隔离。所有对KMS的调用都必须通过双向TLS (mTLS)认证，并且有严格的IAM权限控制。

### d. 高可用与安全

1.  **高可用**：
    *   **许可证服务器**：这是直接面向用户的关键在线服务。它必须设计成**无状态**的，可以水平无限扩展。通过部署在K8s上，配置HPA，根据CPU和请求量自动伸缩。同时，必须做多地域/多机房部署，通过全局流量管理器（GTM）实现故障转移。
    *   **KMS**：作为核心依赖，其可用性至关重要。除了服务本身的多实例部署，其底层数据库必须是主从热备或多活架构。可以在许可证服务器中增加一层本地缓存（如freecache），缓存热点KID对应的CEK几秒钟，以应对KMS的瞬间抖动。
    *   **优雅降级**：当DRM系统出现故障时，可以根据业务策略进行降级，比如对于非独家或非付费内容，可以临时切换到不加密或清晰度较低的版本播放，保证基础的播放体验。

2.  **安全增强**：
    *   **设备绑定与并发播放控制**：在业务层下发Token时，可以记录当前用户的活跃设备列表。当许可证服务器请求校验Token时，业务后端可以检查设备ID是否合法，以及当前并发播放数是否超限，从而实现"一个VIP账号最多在2台设备上同时观看"这类需求。
    *   **防录屏机制**：
        *   **软件层面**：客户端App需要具备检测录屏软件运行的能力，一旦发现就停止播放或弹出警告。
        *   **硬件层面**：在许可证策略中开启**HDCP (High-bandwidth Digital Content Protection)** 支持。对于高清内容（如1080P/4K），要求播放设备和显示器之间的连接支持HDCP 2.2或以上版本，否则降级到标清播放或直接禁止播放。
    *   **溯源水印**：对于价值极高的内容（如院线首发电影），这是最后的防线。在播放时，由CDN边缘节点或客户端，将经过加密的、包含当前用户ID、设备ID、请求时间等信息的水印**实时、不可见地**嵌入到视频像素或频域中。一旦发生盗版，可以从盗版视频中提取出水印信息，精确定位到盗版源头。

### e. 客户端集成与兼容性

*   **多DRM策略**：这是必须的选择，因为没有一个DRM方案能覆盖所有平台。
    *   **Google Widevine**: Chrome, Android, Firefox。
    *   **Apple FairPlay Streaming (FPS)**: Safari, iOS, tvOS。
    *   **Microsoft PlayReady**: Edge, Windows, Xbox。
*   **播放器选型**：自研播放器成本极高，通常会选择成熟的商业播放器SDK（如THEOplayer, Bitmovin）或优秀的开源方案（Web端的Shaka Player，Android的ExoPlayer），它们已经封装好了复杂的多DRM握手逻辑。应用开发者的主要工作是集成业务逻辑，即获取Token并传递给播放器内核。
*   **兼容性处理**：对于无法支持DRM的古老设备或环境，可以采取**差异化服务**策略：禁止播放，或仅提供较低清晰度（如360P）的非加密版本，并配上更显眼的可见水印（如用户UID）。这是一种在用户体验和内容安全之间的权衡。
