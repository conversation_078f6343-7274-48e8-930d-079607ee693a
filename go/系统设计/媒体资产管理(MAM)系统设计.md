### 系统设计题："腾讯视频媒体资产管理（MAM）系统"

#### a. 整体架构 (Overall Architecture)

这个系统的核心是"资产"（Asset）和"工作流"（Workflow）。我将其设计为微服务架构，主要包含以下几个部分：

*   **接入层 (Ingestion Gateway)**:
    *   提供给内部运营、外部CP（Content Provider）上传内容的统一入口。
    *   支持多种上传方式：控制台上传、API上传、对象存储触发。
    *   负责初步的鉴权、参数校验，并生成全局唯一的资产ID（`Asset ID`）。
    *   上传完成后，向工作流引擎触发一个"新资产入库"工作流。

*   **核心服务层 (Core Services)**:
    *   **资产服务 (Asset Service)**: 维护资产的核心元数据，包括技术元数据（格式、码率、时长）、业务元数据（标题、简介、演员）、管理元数据（版权、状态、生命周期）。这是MAM的核心。
    *   **工作流引擎 (Workflow Engine)**: 系统的"大脑"。负责编排各种原子化的媒体处理任务。例如，一个典型的入库流程可能是：`开始 -> 视频转码 -> 视频质检 -> AI打标签 -> 人工审核 -> 结束`。我会选择开源的 `Cadence` 或 `Temporal` 作为技术选型，因为它们提供了强大的状态管理、重试、定时任务和可视化能力。
    *   **媒体处理服务 (Media Processing Services)**: 一系列无状态的、可水平扩展的Worker服务，订阅工作流引擎下发的任务。包括：
        *   转码服务：生成不同码率、格式的视频文件。
        *   质检服务（QC）：检测黑边、花屏、音画不同步等问题。
        *   AI分析服务：智能封面、内容标签、场景识别、明星识别。
        *   审核服务：与内容安全系统对接，进行机审和人审。

*   **存储层 (Storage Layer)**:
    *   **对象存储 (Object Storage)**: 视频源片、转码后的各码率文件、封面图等非结构化数据，全部存储在腾讯云COS或自建的Ceph集群中。根据访问频次使用不同的存储类别（标准、低频、归档）来优化成本。
    *   **关系型数据库 (MySQL/PostgreSQL)**: 存储结构化、需要事务保证的核心元数据，如资产基本信息、用户信息、版权合同信息。采用分库分表来支持水平扩展。
    *   **文档/搜索引擎 (Elasticsearch/MongoDB)**: 存储半结构化、需要灵活查询的元数据，如演职员表、标签、搜索关键词等。Elasticsearch尤其适合后台运营人员进行多维度、全文检索的需求。

*   **API与后台 (API & Console)**:
    *   **API网关**: 暴露RESTful API，供内部系统（如CMS后台、推荐系统）调用。
    *   **CMS后台**: 供运营和编辑人员管理、检索、编排和发布资产的Web界面。


#### b. 核心挑战与设计考量

1.  **海量文件管理与生命周期**:
    *   **全局唯一ID**: 每个独立的媒体内容（无论是一部电影还是一个短视频）都必须有一个全局唯一的`Asset ID`。所有衍生出的文件（不同码率的视频、不同尺寸的封面）都与这个ID关联。
    *   **标准化命名**: 制定严格的文件存储路径和命名规范，例如 `/asset_id/source/video.mp4`, `/asset_id/transcoded/1080p.mp4`, `/asset_id/images/cover_16x9.jpg`。这样便于程序定位和管理。
    *   **存储成本优化**: 结合业务场景，制定数据的生命周期策略。例如，上传的源片在转码成功并验证后，若无特殊需求可在30天后转为归档存储。冷门的、老的剧集也可以整体降级到低频或归档存储。

2.  **复杂的元数据模型**:
    *   **混合存储是关键**: 不能用单一数据库解决所有问题。
        *   `MySQL`: 存放核心的、关系明确的实体，如`asset`表（asset_id, title, status, create_time），`copyright`表等。利用其事务性保证数据一致性。
        *   `Elasticsearch`: 存放描述性的、多对多的、需要被检索的数据。比如一个资产有多个标签，多个演员，这些都适合存为JSON文档。ES的倒排索引能提供毫秒级的复杂组合查询能力，这是MySQL难以做到的。
    *   **数据同步**: MySQL的数据通过`Canal`订阅`binlog`的方式，准实时地同步到Elasticsearch，保证两边数据的一致性。更新操作只在MySQL端进行。

3.  **高可靠的工作流编排**:
    *   **为何不用消息队列?**: 简单的"任务A完成发个消息，任务B监听消息"的模式，在流程复杂时会变得难以维护。无法直观地看到一个资产当前处于哪个阶段，失败了如何重试，某个环节卡住了如何排查。
    *   **专业工作流引擎的优势**:
        *   **状态持久化**: `Temporal/Cadence`会把工作流的每一步状态都持久化，即使Worker进程崩溃或重启，工作流也能从上次中断的地方继续执行。
        *   **内置重试与超时**: 可以为每个活动（Activity）配置精细的重试策略（如指数退避）和超时时间。
        *   **可观测性**: 提供可视化的界面，可以清楚地看到每个工作流实例的执行历史、耗时和输入输出，极大地方便了问题排查。
        *   **可扩展性**: Worker Pool可以根据任务队列的长度动态扩缩容。

#### c. 关键模块深度设计

**工作流模板化设计**

为了应对不同类型内容（电影、电视剧、综艺、UGC短视频）有不同处理流程的需求，工作流必须是可配置和模板化的。

1.  **定义原子能力 (Activity)**: 将所有媒体处理功能封装成独立的、可复用的`Activity`。例如：`transcode(source_url, profile)`, `run_qc(video_url)`, `get_ai_tags(video_url)`。
2.  **创建工作流模板 (Workflow Definition)**: 在数据库中存储工作流模板。一个模板就是一个`Activity`的DAG（有向无环图）。
    *   例如，"电影入库模板"可能是：`[Validate -> Transcode(HD, SD) -> QC -> HumanReview -> Publish]`
    *   "UGC短视频入库模板"可能是：`[Validate -> Transcode(SD) -> SecurityScan -> AutoPublish]`
3.  **实例化工作流**: 当一个新资产入库时，根据其类型（如`content_type=movie`）选择对应的模板，实例化一个工作流实例来执行。这样运营人员甚至可以在后台通过拖拉拽的方式，组合出新的处理流程，而不需要开发人员修改代码。

#### d. 高可用与扩展性

*   **服务无状态化**: 所有核心服务和媒体处理Worker都设计成无状态的，可以任意水平扩展，部署在K8s集群中，利用HPA进行自动伸缩。
*   **数据库扩展**:
    *   MySQL：初期进行垂直拆分（按服务拆分），后期对单张大表（如`asset_metadata`表）进行水平分片（Sharding），分片键可以选择`asset_id`。
    *   Elasticsearch：本身就是分布式架构，通过增加节点即可线性扩展。
*   **异步化和解耦**: 整个系统是事件驱动的。上传文件、发起处理流程等都是异步操作。API调用者只需拿到一个`task_id`或`workflow_id`，之后通过回调或轮询来获取最终结果。这避免了长链接和同步等待，提高了系统的吞吐量和韧性。
*   **多区域部署**: 为保证容灾，存储和服务都应考虑多AZ（可用区）或多Region（地域）部署。对象存储COS本身就提供跨区域复制能力。数据库和服务可以实现同城双活或异地多活。

#### e. 系统安全

*   **上传安全**: 对上传入口做严格的身份验证和授权。使用预签名URL（pre-signed URL）的方式进行上传，客户端直接将文件传到对象存储，不经过业务服务器，同时URL有时间限制和权限控制，安全且高效。
*   **防盗链**: 最终对外提供播放服务的URL，必须经过CDN和边缘鉴权，防止未授权的下载和播放。MAM系统在将内容发布到CDN时，会协同配置相应的鉴权策略。
*   **操作审计**: 所有对资产的修改、删除、发布操作，都必须有详细的日志记录，包括操作人、时间、IP、变更内容，以备审计和追溯。

这个系统设计涵盖了分布式存储、微服务、工作流引擎、混合数据存储等多个技术领域，对候选人的技术广度和深度都是一个很好的考察。 