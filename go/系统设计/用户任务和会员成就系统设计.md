# 系统设计题："腾讯视频用户任务和会员成就系统"设计

## a. 整体架构

这是一个结合了实时流处理和在线事务处理的混合架构。

1.  **数据采集层 (Data Collection)**:
    *   用户的核心行为，如播放、点赞、评论、分享、关注、登录等，都会通过客户端或服务端埋点，携带用户 `user_id` 和事件 `event_type` 等信息，统一上报到公司的日志网关。
    *   日志网关将这些行为事件实时投递到 **Kafka** 集群中。每个业务行为可以有自己的 Topic，或者一个统一的 Topic，通过 `event_type` 区分。

2.  **实时计算层 (Real-time Computing)**:
    *   **核心引擎**: 我会选择 **Apache Flink**。它非常适合这种需要对用户行为流进行复杂事件处理（CEP）和状态计算的场景。
    *   **处理流程**:
        1.  Flink 作业消费 Kafka 中的用户行为 Topic。
        2.  **规则加载与分发**: Flink 作业启动时，从配置中心（如 Apollo）或数据库中拉取所有在线的"任务/成就"规则，并广播到所有 TaskManager 实例中。
        3.  **事件处理**: 当一个用户行为事件到达时，Flink 根据 `user_id` 进行 `keyBy`，确保同一个用户的所有事件由同一个 Sub-task 处理。
        4.  **进度计算**: 在 Flink 的 `KeyedState` 中维护每个用户在每个任务上的进度。例如，对于"本周观看5部电影"的任务，State 中会存储一个计数器和一个电影 `content_id` 的 Set（用于去重）。
        5.  **任务完成检测**: 当一个任务的进度条在 State 中被更新为"完成"时，Flink 会产生一个"任务完成"事件，并将其发送到下游的另一个 Kafka Topic (`task-completion-topic`)。

3.  **数据存储与服务层 (Storage & Service)**:
    *   **任务配置库 (MySQL/PostgreSQL)**: 存储任务和成就的静态定义信息，如任务名称、描述、起止时间、规则定义（DSL形式）、奖励内容等。运营通过后台管理系统配置。
    *   **用户进度缓存 (Redis Cluster/Tair)**: 这是为了让 API 服务能快速查询用户任务的实时进度。Flink 在计算过程中，可以定期（如每隔几秒）将 `KeyedState` 中的进度快照异步地写入 Redis。这样，用户在任务中心页面刷新时，看到的是秒级延迟的进度。
    *   **核心状态库 (MySQL/TiDB)**: 存储用户的最终任务状态，如"已完成"、"已领取奖励"。当"任务完成"事件被消费时，会更新这个库。这是系统的最终一致性保证，使用关系型数据库是为了保证事务性（例如，更新状态和发放奖励需要在一个事务里）。
    *   **奖励发放服务 (Go Service)**: 订阅 `task-completion-topic`。收到消息后，调用对应的奖励发放接口（如优惠券、积分、会员天数等），并在成功后更新核心状态库中的任务状态为"已完成，待领取"。
    *   **任务API服务 (Go Service)**: 无状态的 Go 服务集群。负责向客户端提供任务列表、任务进度、领取奖励等接口。它会优先查询 Redis 获取实时进度，查询 MySQL/TiDB 获取最终状态。

4.  **客户端展现层 (Client Presentation)**:
    *   用户在 App 的任务中心与任务 API 服务交互，查看任务列表和进度，并手动点击领取奖励。

## b. 核心模块设计

1.  **任务/成就规则引擎**:
    *   **规则定义**: 规则不能硬编码。我会设计一套 **DSL (Domain-Specific Language)**，用 JSON 格式来描述一个任务。
        ```json
        {
          "taskId": "weekly_movie_5",
          "taskType": "ACCUMULATE", // 累积型
          "timeRange": { "start": "2023-10-01", "end": "2023-12-31" },
          "triggerEvent": "video_play",
          "conditions": [
            { "key": "play_duration_sec", "op": ">=", "value": 600 },
            { "key": "video_category", "op": "==", "value": "movie" }
          ],
          "goal": { "type": "COUNT", "target": 5, "deduplicateBy": "content_id" },
          "rewardId": "reward_vip_3_days"
        }
        ```
    *   **规则执行**: Flink 作业解析这个 DSL，并将其转化为内部的计算逻辑。例如，`triggerEvent` 对应要消费的 Kafka Topic，`conditions` 对应 `filter` 操作，`goal` 对应 `aggregate` 逻辑。这种方式让运营可以灵活地配置新任务而无需代码开发。

2.  **用户进度追踪**:
    *   **Flink State**: 这是核心。对于每个 `(user_id, task_id)` 对，Flink 会在 `KeyedState` 中维护其进度。
        *   对于计数类任务，使用 `ValueState<Long>`。
        *   对于需要去重的计数类，使用 `MapState<String, Boolean>` 或更节省空间的 `ValueState<HyperLogLog>`（如果允许一定误差）。
        *   对于复杂的组合任务（如"完成A、B、C三个子任务"），使用 `MapState<String, Boolean>` 跟踪子任务的完成情况。
    *   **State Backend**: 使用 **RocksDBStateBackend**。它可以将状态数据存储在本地磁盘上，支持增量快照到 HDFS/S3，实现了超大状态（T级别）的存储，避免了内存瓶颈。
    *   **进度查询优化**: 直接查询 Flink 的 State 对外提供服务是困难的。因此，采用 Flink State -> Redis -> API 的模式。Flink 定期将状态更新到 Redis，API 查询 Redis。这是一种读写分离的 CQRS 模式，保证了查询服务的高 QPS 和低延迟，同时不影响实时计算的稳定性。

## c. 存储选型

*   **Redis Cluster**:
    *   **用途**: 缓存用户实时任务进度。
    *   **数据结构**: 使用 `HASH`。Key: `task_progress:{user_id}`，Field: `task_id`，Value: `{"current": 4, "last_update": "timestamp"}` (JSON string)。
    *   **理由**: 极高的读写性能，满足任务中心页面的高频刷新需求。

*   **MySQL / TiDB**:
    *   **用途**: 1. 存储任务定义元数据。 2. 存储用户的最终任务状态（已完成/已领取）。
    *   **理由**: 任务定义读多写少，适合 MySQL。用户最终状态的变更需要事务保证，且数据量可能巨大（用户数 * 任务数），如果单机 MySQL 成为瓶颈，可以平滑迁移到分布式数据库 TiDB。

*   **Kafka**:
    *   **用途**: 核心的消息总线，用于用户行为事件的收集和计算结果（任务完成事件）的传递。
    *   **理由**: 高吞吐、可扩展、解耦上下游。是流处理架构的基石。

*   **ClickHouse / HBase**:
    *   **用途**: （可选）归档历史数据。Flink 可以将所有状态变更日志（如每次进度+1）或每日的全量进度快照沉淀到 OLAP 数据库中。
    *   **理由**: 用于后续的数据分析、用户行为路径挖掘、任务效果评估等。

## d. 高可用与高并发

*   **写路径 (行为事件)**:
    *   入口处的日志网关和 Kafka 集群本身就是高可用的分布式系统。
    *   Flink 作业开启 Checkpoint，保证 **Exactly-Once** 语义。即使计算节点宕机，也能从上一个成功的 Checkpoint 恢复状态，数据不丢不重。

*   **读路径 (任务查询)**:
    *   任务 API 服务是无状态的，可以水平扩展。
    *   **多级缓存**: `客户端本地缓存` -> `CDN` -> `API Gateway 缓存` -> `服务本地缓存 (BigCache)` -> `Redis Cluster` -> `MySQL`。通过层层设防，将绝大部分读请求挡在数据库之前。
    *   **数据一致性**: 客户端展示的数据允许秒级延迟，优先读缓存。只有在"领取奖励"这种关键操作时，才必须穿透到后端服务，读取 MySQL/TiDB 的最终状态并加锁，保证奖励不被重复领取。

*   **服务容灾**:
    *   **限流熔断**: 在 API Gateway 层对用户请求进行限流，防止恶意请求。对 Redis 和 MySQL 的调用，必须有熔断器包裹，当依赖的存储出现故障时，可以快速失败或返回兜底数据（如提示"系统繁忙，请稍后再试"），防止级联雪崩。
    *   **降级处理**: 如果 Flink 计算链路延迟严重或故障，最差情况下，任务进度不更新。但用户依然可以访问任务中心（看到旧的进度），核心的视频播放等功能不受影响。

## e. 难点与挑战

1.  **规则的灵活性与动态更新**:
    *   **挑战**: 如何在不停止 Flink 任务的情况下，动态增删或修改任务规则？
    *   **方案**:
        1.  **规则动态加载**: 利用 Flink 的 `Broadcast State` 模式。一个独立的 `Rule-Provider` 源（可以是一个特殊的 Kafka Topic 或定期轮询数据库）将最新的规则集广播给所有计算实例。
        2.  **新旧规则处理**: 当规则更新时（例如修改了任务的目标），需要策略来处理已有进度。可以废弃旧进度重新计算，也可以保留并按新规则继续。这需要在规则中增加版本号和迁移策略字段。

2.  **数据一致性与准确性**:
    *   **挑战**: 如何保证奖励不被重复发放？如何处理上游数据源的延迟或乱序？
    *   **方案**:
        1.  **幂等性保证**: 奖励发放服务在消费"任务完成"事件时，必须做幂等处理。可以在 MySQL/TiDB 的用户任务完成表中增加一个 `request_id` 或使用 `(user_id, task_id)` 作为联合唯一键，利用数据库约束防止重复插入。
        2.  **事件时间处理**: Flink 必须使用 `Event Time`（事件发生时间）而非 `Processing Time`（事件处理时间）来处理数据，并设置合理的 `Watermark` 策略。这样即使事件因为网络等原因乱序到达，也能被正确地分配到其所属的时间窗口中进行计算，保证了时效性任务（如"本周内"、"今日内"）计算的准确性。

3.  **历史数据重算 (Back-filling)**:
    *   **挑战**: 当上线一个全新的、有追溯性的成就时（例如"累计观看1000部电影"），如何为老用户计算他们的历史进度？
    *   **方案**: 这需要一个离线批处理流程。
        1.  编写一个 **Spark** 或 **Flink Batch** 作业。
        2.  该作业读取存储在数据湖（如 HDFS, S3）中的全部历史用户行为日志。
        3.  按照新成就的逻辑进行批量计算，得出每个用户的初始进度。
        4.  将计算结果批量写入 Redis 和 MySQL/TiDB，完成进度的初始化。此后，增量部分再交由实时的 Flink 流处理任务接管。

## f. 架构选型思考：为什么使用 Flink 而不是在业务后端直接处理？

这是一个非常核心的架构决策点。在业务后端服务中直接消费 Kafka 消息并更新数据库中的计数，确实是一种更简单的架构。对于业务初期或任务逻辑简单（如：累计登录X天）的场景，这种方法是可行的。

但对于腾讯视频这种体量和复杂度的系统，直接在后端处理会面临以下几个难以解决的瓶颈，而这正是 Flink 这类流处理框架的价值所在：

1.  **数据库成为性能瓶颈**:
    *   **后端直写**: 用户的每一个行为（播放、点赞、评论）都需要触发一次对数据库（无论是 Redis 还是 MySQL）的读写操作来更新进度。在高峰期，这会产生每秒数十万甚至上百万次的数据库请求，任何集中式的数据库都难以承受。
    *   **Flink 方案**: Flink 将海量的状态计算（进度累加、去重）在自己的分布式任务节点内部完成（使用基于本地磁盘的 RocksDB State），极大地减少了对外部数据库的依赖。它只在必要时（如任务完成、定期快照）与外部系统交互，将N次高频的写请求聚合为1次或0次低频写，从根本上解决了数据库瓶颈。

2.  **状态一致性与并发控制的复杂性**:
    *   **后端直写**: 后端 API 服务通常是无状态且水平扩展的。同一个用户的多个并发事件（例如，快速点赞后又取消）可能被分发到不同的服务实例上。要正确处理计数，就必须依赖数据库的事务或分布式锁，这会显著增加延迟、降低吞吐量，并引入死锁风险。
    *   **Flink 方案**: Flink 通过 `keyBy(user_id)` 操作，可以保证同一个用户的所有事件都由同一个物理线程按顺序处理。这从机制上避免了并发冲突，使得状态更新（如计数、去重）可以在无锁的环境下高效进行，极大地简化了逻辑。

3.  **复杂时间窗口与事件顺序处理的难度**:
    *   **后端直写**: 许多任务是基于时间的，例如"本周内观看5部电影"、"连续7天登录"。在业务后端自己实现这些复杂的窗口逻辑、处理事件乱序和延迟数据（Watermark 概念），是非常困难且容易出错的。
    *   **Flink 方案**: Flink 的核心能力之一就是强大的事件时间（Event Time）处理和窗口（Windowing）机制。无论是滚动窗口、滑动窗口还是会话窗口，都可以用几行声明式的代码来定义，框架会为你处理好所有与时间相关的复杂问题，保证计算的准确性。

4.  **计算与服务的解耦**:
    *   **后端直写**: 将密集的计算逻辑和对外服务的 API 逻辑耦合在同一个服务中，会导致服务变得"重"。计算资源的扩缩容会与服务资源的扩缩容混在一起，难以独立优化。
    *   **Flink 方案**: Flink 将数据处理管道与 API 服务清晰地分离开。计算层可以根据数据流入的速率独立扩缩容，而 API 服务层可以根据用户请求的 QPS 独立扩缩容。这是一种更清晰、更具弹性的微服务架构。

**总结**:
虽然引入 Flink 增加了系统的技术栈复杂度，但我认为它通过**本地化状态计算**、**内置的并发模型**和**强大的时间处理能力**，解决了后端直写方案在**高性能**、**高一致性**和**逻辑复杂性**上的核心痛点，是构建大规模、实时、复杂事件驱动型系统的理想选择。 