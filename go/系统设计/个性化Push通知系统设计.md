### 系统设计题："腾讯视频亿级用户个性化Push通知系统"

#### a. 整体架构

一个健壮的Push系统需要清晰地分层，以实现高内聚、低耦合。我将其划分为数据层、策略层、任务层和执行层。

```mermaid
graph TD
    subgraph 数据与事件源
        A1[用户行为流<br>(Kafka)]
        A2[内容事件流<br>(Kafka)]
        A3[用户画像平台<br>(HBase/Redis)]
        A4[运营平台]
    end

    subgraph 策略与任务中心 (Go)
        B1[任务管理<br>(批量/实时/自动化)]
        B2[用户圈选与Targeting]
        B3[智能调度与流控]
        B4[A/B测试模块]
    end

    subgraph 分发与执行层
        C1[推送网关 (Go)<br>水平扩展/无状态]
        C2[设备与用户档案库<br>(Redis Cluster)]
    end

    subgraph 厂商通道
        D1[APNs(Apple)]
        D2[FCM(Google)]
        D3[华为/小米/OPPO/VIVO]
    end

    subgraph 效果追踪与反馈
        E1[SDK回执上报<br>(Kafka)]
        E2[实时统计<br>(Flink)]
        E3[分析与展示<br>(ClickHouse/Dashboard)]
    end

    A1 --> B2
    A2 --> B1
    A3 --> B2
    A4 --> B1

    B1 --> B3
    B2 --> B3

    B3 -- 分发任务 --> C1

    C1 -- 查询设备信息 --> C2
    C1 -- 推送 --> D1
    C1 -- 推送 --> D2
    C1 -- 推送 --> D3

    subgraph Client App
      F1[用户手机]
    end

    D1 --> F1
    D2 --> F1
    D3 --> F1

    F1 -- 上报回执 --> E1
    E1 --> E2
    E2 --> E3
    E3 -- 反哺 --> A3
```

*   **数据与事件源 (Data & Event Sources):**
    *   **用户行为流:** 通过高可用日志网关实时采集用户行为（如追剧、点赞、搜索新热词），汇入Kafka。
    *   **内容事件流:** 媒资库的更新（如剧集更新、新电影上线）、直播事件（如NBA比赛即将开始）通过Canal订阅binlog或业务方直接生产消息到Kafka。
    *   **用户画像平台:** 离线（Spark/Hive）和实时（Flink）计算用户的长期和短期兴趣标签、活跃度、消费能力等，存入HBase/ClickHouse，并将需要高速查询的标签同步到Redis或Elasticsearch。
    *   **运营平台:** 允许运营人员手动创建推送任务，通过圈选人群、配置文案和落地页，生成推送指令。

*   **策略与任务中心 (Strategy & Task Center):**
    *   **核心职责:** 这是系统的大脑，负责"决定给谁（Targeting）、在何时（Scheduling）、发什么（Content）"。
    *   **任务类型:**
        1.  **批量任务 (Batch):** 如每日新闻、节假日活动，用户量巨大，需要分批、错峰推送。
        2.  **实时任务 (Real-time):** 由事件触发，如"您追的剧更新了"、"您关注的主播开播了"，要求延迟在秒级。
        3.  **自动化任务 (Automation/Journey):** 预设的用户生命周期路径，如"新用户注册3天后推送一部高分电影"，实现精细化运营。
    *   **技术实现:** 使用Go构建。通过MySQL/TiDB存储任务元数据。利用分布式调度器（如xxl-job）或结合Redis ZSET实现的延迟队列来处理定时和周期性任务。

*   **分发与执行层 (Dispatch & Execution):**
    *   **推送网关 (Push Gateway):**
        *   **职责:** 核心的推送执行单元。它维护与各大厂商Push服务器（APNs, FCM, 华为, 小米, OPPO, VIVO等）的长连接或HTTP2连接。
        *   **设计:** 采用无状态、可水平扩展的Go服务。它从任务中心拉取或接收已经处理好的推送子任务（每个子任务包含一批用户的device_token和payload）。
        *   **通道抽象:** 内部会有一个适配器层（Adapter Pattern），将上游统一的推送请求，转换为各厂商私有的协议格式和鉴权逻辑。
    *   **设备与用户档案库 (Device & User Profile Store):**
        *   **职责:** 维护`user_id` -> `device_token` -> `厂商通道`以及设备的各种状态（如App版本、通知开关状态、免打扰时段）的映射关系。
        *   **技术选型:** 这是典型的KV查询场景，对性能要求极高。使用Redis Cluster或Tair等高性能KV存储是最佳选择。Key可以是`user_id`或`device_token`。

*   **效果追踪与反馈闭环 (Tracking & Feedback Loop):**
    *   **数据上报:** SDK在客户端接收、展示、点击推送后，会上报回执日志到Kafka。
    *   **数据处理:** Flink或Spark Streaming消费回执日志，实时计算触达率、打开率、转化率等核心指标。
    *   **数据落地:** 统计结果存入ClickHouse或Elasticsearch用于分析和可视化，同时关键结果（如用户对某类推送的点击偏好）会反哺用户画像平台，形成闭环。

#### b. 核心挑战与设计要点

1.  **海量用户与高并发推送:**
    *   **挑战:** 面对亿级用户，一次大型活动推送可能需要在一小时内触达数亿设备，峰值QPS可达百万。
    *   **对策:**
        *   **任务分片:** 在任务中心，将一个大的推送任务（如给1亿用户推送）拆分为数千个子任务，每个子任务包含几万个用户。这些子任务被分发到消息队列（如Kafka/RocketMQ）中。
        *   **水平扩展:** 推送网关是无状态的，可以部署大量实例，每个实例都是一个消费者，从队列中拉取子任务并执行。通过K8s的HPA可以轻松实现弹性伸缩。
        *   **连接复用:** 推送网关与厂商服务器之间使用带有连接池的HTTP2长连接，大幅减少握手开销。

2.  **精准与个性化:**
    *   **挑战:** 推送错误的内容是用户卸载App的主要原因之一。
    *   **对策:**
        *   **多维标签体系:** 用户画像需包含：人口属性（年龄、地域）、行为标签（偏好明星、类目）、状态标签（是否会员、活跃度）、上下文标签（当前Wi-Fi环境、所在城市天气）。
        *   **实时画像:** 利用Flink实时计算用户短时兴趣。例如，用户刚刚搜索了"庆余年2"，几分钟后就可以给他推送相关的剪辑或资讯，这种"即时满足"的体验效果最好。
        *   **A/B测试平台:** 所有重要的推送策略（文案、图片、推送时机）都应该通过A/B测试来验证，以数据驱动决策。

3.  **高时效性:**
    *   **挑战:** 赛事直播、突发新闻等场景，要求延迟必须在秒级。
    *   **对策:**
        *   **链路优化:** 为实时任务建立独立的、更高优先级的Kafka Topic和推送网关集群。
        *   **内存计算:** 任务中心和用户画像查询的关键路径上，大量使用本地缓存（FreeCache/BigCache）和分布式缓存（Redis）来减少对后端数据库的依赖。
        *   **预加载:** 对于可预见的事件（如9点的比赛），可以提前将目标用户圈选出来，预加载到Redis中，事件发生时直接触发，无需临时查询。

4.  **防打扰与用户体验:**
    *   **挑战:** 过度推送会严重影响用户体验。
    *   **对策:**
        *   **全局频率控制:** 在Redis中为每个用户维护一个计数器（如`push_count:user_id`），记录其当天已接收的推送次数，在任务下发前检查是否超限。
        *   **智能免打扰:** 除了用户手动设置，系统还可以通过学习用户的App使用习惯，预测其睡眠时间，自动规避。
        *   **消息折叠/聚合:** 对于同一剧集的多次更新，可以在客户端或厂商通道层面实现智能折叠，只显示"《XXX》有3条更新"，而不是刷屏。

#### c. 存储选型总结

*   **任务元数据:** `MySQL` 或 `TiDB`。事务性和一致性要求高，但并发量相对可控。
*   **用户-设备映射:** `Redis Cluster`。纯KV，QPS极高，需要高可用。
*   **用户画像/标签:**
    *   **离线数仓:** `HBase` 或 `ClickHouse`，存储全量用户行为和标签数据。
    *   **在线查询:** `Elasticsearch`（用于复杂组合圈人）、`Redis`（用于高速单点查询）。
*   **统计分析:** `ClickHouse`。极其适合海量日志的实时聚合分析场景。
*   **消息队列:** `Kafka`。业界标杆，高吞吐、可回溯，完美契合数据管道的需求。

#### d. 高可用与容灾

*   **多活部署:** 核心服务（任务中心、推送网关）必须在多个可用区/数据中心部署，通过负载均衡对外服务。
*   **通道降级与智能路由:** 实时监控各厂商通道的延迟和成功率。当某个通道（如某手机厂商）出现故障时，自动降低其权重或暂时熔断，对于同时拥有多个设备token的用户，优先选择当前最优通道。
*   **任务幂等性:** 推送网关在执行子任务时，必须先检查该任务ID是否已被处理（如使用Redis `SETNX`），防止因重试导致重复推送。
*   **数据兜底:** 如果在线用户画像系统出现故障，应能降级使用T-1的离线画像数据，保证核心推送业务不受影响。 