系统设计题：“腾讯视频通用活动配置和投放系统”设计
好的，这是一个非常好的问题，考察的是系统抽象、动态化和高可用设计能力，这对于支持腾讯视频这样体量巨大、运营活动繁多的业务至关重要。

在当前 `go/Case` 目录中，我们已经探讨了许多核心的媒体业务系统，如推荐、直播、弹幕等。现在，我们来设计一个同样关键但更具平台性的系统，这个问题目前目录里还没有：

**系统设计题：“腾讯视频通用活动配置和投放系统”设计**

这是一个支撑市场和运营团队，在不依赖开发资源的情况下，快速创建、上线和管理各种用户互动活动（如签到、集卡、投票、观看任务等）的平台级系统。

我的设计思路如下：

### a. 整体架构

这个系统的核心是“动态化”和“解耦”。运营通过“配置”而非“编码”来创造活动。

![通用活动配置和投放系统架构图](https://mermaid.ink/svg/eyJjb2RlIjoiZ3JhcGggVERcbiAgICBzdWJncmFwaCDnlJ_miJHms5XlhpAgKFVzZXIgRW52KVxuICAgICAgICBVc2VyQ2xpZW50W-iLseWPj-WlsF0gLS0-IHwg55uR5ZCs5LqL5o6lIHwgQVBJR2F0ZXdheVvlhbPlhpjvvIzlh4_nq4vlo6JdXG4gICAgICAgIEFQSVJhdGV3YXkgLS0-IHwg5L2c5a2Y55uR5ZCs5LqL77yM5aaC5p6c5Z-65YCZ5aW9IHwgS2Fma2Fb5aSq5o6o5ZSQ5LiL572uXSBcbiAgICBlbmRcblxuICAgIHN1YmdyYXBoIOW8gOWPkOWdoOWdoOS7i-WIq-ihqOacrOebuOWFs-i3r-mAgeW5s-WIq-WFs1xuICAgICAgICBBZG1pbkZFW-aWh-S7tuWFsF0gLS0-IHwgMS7phY3lrZDms5XliJfigIwgQWN0aXZpdHlNYW5hZ2VtZW50W-WcsOWdg-i3r-mAgeWIq-ihqF1cbiAgICAgICAgQWN0aXZpdHlNYW5hZ2VtZW50IC0tPiB8IDMuc2F2ZSDms5XliJfigIwgTXlTUUxb5rOV5Yid5pWw5o2u5pS2XVxuICAgICAgICBBY3Rpdml0eU1hbmFnZW1lbnQgLS0-IHwgMi7lkK_lkK_ms5XliJfigIwgQ29uZmlnQ2VudGVyW-WIq-ihqOW9k-i3r-i3sF1cbiAgICAgICAgQ29uZmlnQ2VudGVyIC0tPiB8IDQudG8g5ZSQ5LiL5LiN5Y-vIHwgRXhlY3V0aW9uU2VydmljZVvms5XliJfljZXkvZnms5VdXG4gICAgZW5kXG5cbiAgICBzdWJncmFwaCDms5XliJfljZXkvZnvvIzkvY_or6Hnsbvln5pcbiAgICAgICAgS2Fma2EgLS0-IHwgNS7ms5XliJfljZXkvZnms5UgISB8IEV4ZWN1dGlvblNlcnZpY2VcbiAgICAgICAgRXhlY3V0aW9uU2VydmljZSAtLT4gfCDlr7nlhL_kvZXnpL7vvIzkuI3lj68gISB8IFJ1bGVFbmdpbmVb6Kej5Ym15byA55m-XVxuICAgICAgICBFeGVjdXRpb25TZXJ2aWNlIC0uLT4gfCDmsqHmnI3kuovlhpvvvIzliqDkvZXnpL4gISB8IFVzZXJQcm9ncmVzc1NlcnZpY2Vb55So5oi35rOV5Yid54mp5oCB5pWwXSBcbiAgICAgICAgRXhlY3V0aW9uU2VydmljZSAtLT4gfCDlkIznpLrlsLHlhYDlrrAgISB8IFJld2FyZFNlcnZpY2Vb5aWx5YWQ5Y-R5pWw6K-l5py6XVxuICAgICAgICBSZXdhcmRTZXJ2aWNlIC0tPiB8IOiQveWPt-Wls-ihqO-8jOW5s-mhueeQhuaxguaVsCAhIHwgUmV3YXJkSW50ZXJmYWNlW-aVsOihqOaAn-mAml1cbiAgICAgICAgVXNlclByb2dyZXNzU2VydmljZSAtLT4gfOaOpeWPo-WPoyA8YnI-5L2c6L-H5pWw5o2uIHwgUmVkaXNb55So5oi354mp5oCB5pWw5o2uXVxuICAgICAgICBSdWxlRW5naW5lIC0tPiB8IOWvueWHv-eJqeWBmuS6i-S4jeWPr-aWh-S7tiEgfCBFeGVjdXRpb25TZXJ2aWNlXG4gICAgICAgIEV4ZWN1dGlvblNlcnZpY2UgLS0-IHwgOS7msqHmnI3mlbDmja7msYLlkIggISB8IENsaWNrSG91c2Vb5pWw5o2u5浠5Y-kXVxuICAgIGVuZFxuIiwibWVybWFpZCI6eyJ0aGVtZSI6ImRlZmF1bHQifSwidXBkYXRlRWRpdG9yIjpmYWxzZX0)

1.  **活动管理后台 (Admin Backend)**: 一个面向运营人员的可视化Web界面，用于创建、配置和管理活动。在这里可以定义活动的生命周期、规则、参与资格、奖励等。
2.  **配置中心 (Config Center)**: 活动的"大脑"。存储所有活动的元数据和规则定义。技术上，可以使用MySQL存储持久化的活动定义，并通过Nacos、Apollo等配置中心将活动规则实时推送给下游服务。
3.  **事件总线 (Event Bus - Kafka)**: 系统的"神经网络"。客户端产生的所有与活动相关的用户行为（如播放、登录、分享、点赞），都会被格式化为统一的事件消息，发送到Kafka集群。这实现了核心业务与活动系统的解耦。
4.  **活动执行服务 (Execution Service)**: 核心处理单元。它消费Kafka中的用户行为事件，从配置中心获取相关活动规则，并调用规则引擎进行判断。这是一个无状态、可水平扩展的服务。
5.  **规则引擎 (Rule Engine)**: 执行服务的"CPU"。负责解析并执行具体的活动规则，例如"用户A是否完成了'观看3部电影'的任务"。
6.  **用户进度服务 (User Progress Service)**: 负责记录和查询用户在某个活动中的实时进度。这是一个需要高性能读写的服务，是整个系统的状态存储核心。
7.  **奖励发放服务 (Reward Service)**: 一个独立、高可靠的服务，负责执行最终的奖励发放动作，如发券、加积分等。通过独立队列与执行服务解耦，确保主流程性能和奖励发放的可靠性。

### b. 核心设计：动态化与通用性的实现

为了做到"通用"，我们必须将一个活动抽象成可配置的组件。

1.  **活动三要素**：
    *   **参与条件 (Condition)**: 谁能参加？这可以是一个复杂的组合逻辑，如 `(用户等级 > 5 AND 注册时间 < 30天) OR (是VIP会员)`。这些条件可以被抽象成对用户画像标签的查询。
    *   **任务逻辑 (Task)**: 需要做什么？这是最核心的部分。我们应提供一个"原子条件库"，运营可以像搭积木一样组合它们。
        *   **原子条件示例**: `观看视频(content_id=xxx)`、`观看任意电影(category=movie)时长 > 10分钟`、`发布评论`、`每日签到`。
        *   **组合逻辑**: 可以通过 `AND/OR`，以及计数（`完成3次`）来组合这些原子条件，形成复杂的任务。
    *   **奖励 (Reward)**: 能得到什么？例如 `优惠券(coupon_id=xxx)`、`积分(points=10)`、`抽奖机会(lottery_chance=1)`。

2.  **规则引擎的设计**:
    *   **选型**: 对于如此动态的需求，不适合硬编码。我们可以选择：
        *   **轻量级脚本引擎**: 如Go的`expr`库或`otto`(JS)。它们允许运营人员编写简单的逻辑表达式，如 `user.level > 3 && event.name == 'play'`。这提供了极大的灵活性。
        *   **JSON-based Rule**: 自定义一套JSON结构来描述规则。例如：`{"op": "AND", "conditions": [{"op": "watch", "count": 3}, {"op": "share", "count": 1}]}`。这种方式更结构化，不易出错，但表达能力有限。
    *   **实现**: 执行服务接收到Kafka事件后，加载对应活动的规则脚本或JSON，将用户当前进度、用户画像、事件内容作为上下文（Context）传入规则引擎，执行后得出`true/false`或`progress_updated`的结果。

### c. 存储选型

不同类型的数据，需要不同的存储方案。

*   **活动元数据 (MySQL)**: 活动的定义、规则描述、生命周期、奖励库存等，这些是低频写、高频读（读到缓存）的数据，且需要事务保证，适合用MySQL。
*   **用户活动进度 (Redis Cluster)**: 这是典型的高并发读写场景。
    *   **数据结构**: `HASH` 是最佳选择。
    *   **Key**: `campaign:progress:{userID}:{campaignID}`
    *   **Field**: `task_1_progress`, `task_2_progress`, `is_completed`
    *   **操作**: 使用Lua脚本来封装"读取进度 -> 判断 -> 更新进度"的逻辑，确保操作的原子性，避免并发问题。例如，一个Lua脚本接收`userID`, `campaignID`, `taskID`和`increment_by`，原子地增加任务进度计数器。
*   **活动规则缓存 (服务本地缓存 + 配置中心)**:
    *   活动规则由MySQL持久化，但通过Nacos等配置中心下发到每个执行服务实例的本地内存中。
    *   这避免了每次处理事件都去请求数据库，极大提升了性能。运营修改规则后，可以通过配置中心实现秒级生效。
*   **活动数据分析 (ClickHouse)**:
    *   所有用户行为事件、规则匹配结果、奖励发放记录，都应落地一份到ClickHouse这样的OLAP数据库中。
    *   这用于后续的活动效果分析，例如计算活动转化漏斗、ROI、用户参与度等。

### d. 高可用与高并发

*   **入口削峰**: Kafka作为事件总线，是天然的削峰填谷利器。即使瞬间有大量用户完成任务，也只是增加了Kafka中的消息，后端服务可以按照自己的节奏平稳消费，不会被冲垮。
*   **服务无状态化**: 执行服务、规则引擎都是无状态的，所有状态都存在于Redis和MySQL中。这使得服务可以部署在K8s上，根据CPU或Kafka消息堆积情况进行自动水平伸缩（HPA）。
*   **热点隔离**:
    *   **热点活动**: 如果某个活动成为现象级爆款（如春节集卡），其处理流量可能会影响到其他几十个普通活动。可以设计"分片消费"机制，将热点活动的消息路由到专属的Kafka Topic和专属的消费者组（即专属的执行服务集群），实现物理资源隔离。
    *   **热点用户**: 一般来说，按`userID`对进度进行存储，天然就是散列的，不容易出现单用户热点。
*   **可靠的奖励发放**:
    *   **事务性消息**: 奖励发放是核心环节，绝不能出错。执行服务在判断用户完成任务后，并不直接调用奖励接口，而是发送一条"待发放奖励"的事务性消息到另一个专用的Kafka Topic。
    *   **幂等消费**: 奖励发放服务消费这条消息，调用对应的接口。奖励接口必须支持幂等性（例如通过唯一的`request_id`或`task_completion_id`），防止因为消息重试导致重复发放奖励。

### e. 风险控制与扩展性

*   **预算和库存控制**: 每个活动的奖励都必须配置预算上限和库存。奖励发放服务在发奖前，必须原子地扣减库存，并检查是否超出预算，防止系统被刷垮。
*   **活动一键开关**: 在配置中心为每个活动设置一个总开关。一旦发现活动有漏洞或造成不良影响，运营可以"一键下线"，执行服务会立刻停止处理该活动的任何事件。
*   **灰度发布能力**: 系统必须支持按用户百分比、用户白名单、用户标签（如"北京地区用户"）等维度进行灰度发布。这集成在"参与条件"的设计中，是降低上线风险的生命线。
*   **监控告警**: 对活动系统的每个环节都要有详尽的监控。关键指标包括：事件处理延迟、活动参与人数/完成人数（分钟级趋势）、奖励发放速率、各服务接口的错误率和耗时。当指标出现异常（如奖励发放速率突增），应立即触发告警。

通过以上设计，我们可以构建一个灵活、可靠、高性能的通用活动平台，极大地提升运营效率和用户体验。 