### 系统设计题："请设计一个支持百万人同时在线的腾讯视频直播系统"

#### a. 整体架构

一个完整的直播系统可以分为 **推流端**、**直播中心** 和 **拉流端** 三大部分。我将采用一个分层、解耦的架构来保证系统的可扩展性和稳定性。

```mermaid
graph TD
    subgraph "推流端 (Publisher)"
        A[主播客户端 App/PC]
    end

    subgraph "直播中心 (Live Center)"
        B[业务/鉴权服务]
        C[接入集群 Ingest<br/>(RTMP/SRT)]
        D[转码集群 Transcoding<br/>(多码率/录制/截图)]
        E[对象存储 COS/S3<br/>(CDN源站)]
        F[信令与互动服务]
    end
    
    subgraph "信令与互动服务"
        G[房间管理 Room Mgmt]
        H[消息网关 WebSocket GW]
        I[消息队列 Kafka]
        J[互动逻辑服务]
    end

    subgraph "数据层 (Data Tier)"
        K[Redis Cluster<br/>(热数据)]
        L[MySQL<br/>(温数据)]
        M[ClickHouse<br/>(冷/分析数据)]
    end

    subgraph "分发网络 (CDN)"
        N[CDN]
    end

    subgraph "拉流端 (Player)"
        O[观众客户端]
    end

    A -- "1. 请求推流地址" --> B
    A -- "2. 推送RTMP/SRT流" --> C
    
    C -- "3. 注册流信息" --> G
    C -- "4. 拉取原始码流" --> D
    
    D -- "5. 存入音视频切片(HLS/DASH)<br/>和录制文件" --> E
    D -- "5.1 异步写入分析数据" --> M
    
    E -- "6. CDN回源" --> N
    
    O -- "7. 请求播放地址和房间信息" --> G
    O -- "8. 通过CDN拉流播放" --> N
    O -- "9. 建立WebSocket长连接" --> H
    
    H -- "10. 收发弹幕/礼物消息" --> I
    I -- "11. 消费消息" --> J
    J -- "12. 业务逻辑处理(DB操作)" --> L
    J -- "13. 查询/更新房间状态(Cache)" --> G
    J -- "14. 扇出消息给相关网关" --> H

    G -- "读写缓存" --> K
    G -- "读写核心数据" --> L
```

1.  **推流端 (Publisher):**
    *   主播通过手机App、PC客户端或专业推流设备，采集本地的音视频数据。
    *   数据经过初步处理（如美颜、降噪）后，通过 **RTMP** (Real-Time Messaging Protocol) 或更优的 **SRT** (Secure Reliable Transport) 协议，将音视频流推送到直播中心的接入节点。推流前会先请求业务服务器，进行鉴权并获取推流地址。

2.  **直播中心 (Live Center):** 这是系统的核心，包含多个解耦的服务集群。
    *   **接入集群 (Ingest Cluster):** 负责接收主播推上来的码流。这是一个高可用的集群，通过LVS/Nginx进行负载均衡。它负责验证推流的合法性，并将流信息注册到中央的"房间管理服务"。
    *   **转码集群 (Transcoding Cluster):** 原始码流的码率和分辨率通常很高，无法适配所有观众的网络。转码集群会从接入集群拉取原始码流，并将其转码成多种不同分辨率和码率的版本（例如：1080p, 720p, 480p），即 **自适应码率 (Adaptive Bitrate)**。同时，它还可以执行如添加水印、实时录制、生成截图等任务。
    *   **分发与存储 (Distribution & Storage):**
        *   转码后的分片（通常是HLS的.ts文件或DASH的.m4s文件）和索引文件（.m3u8或.mpd）会被推送到 **CDN** 的源站，通常是对象存储（如腾讯云COS）。
        *   如果需要直播录制以供后续回看(VOD)，转码集群会将录制好的文件也存入对象存储。
    *   **信令与互动服务 (Signaling & Interaction Service):** 这是支撑海量用户实时互动的关键。
        *   **房间管理 (Room Management):** 负责维护直播间的状态（如主播信息、在线人数、直播流状态等）。这部分数据需要高一致性，可以用MySQL存储，并用Redis做缓存。
        *   **消息网关 (Message Gateway):** 维持与客户端的 **WebSocket** 长连接，负责处理弹幕、点赞、送礼等实时消息的收发。这是一个需要水平无限扩展的集群。

3.  **拉流端 (Player):**
    *   观众进入App的直播间时，客户端会先请求业务服务器，获取直播间的元数据和播放地址列表（包含不同清晰度的流地址）。
    *   播放器根据网络状况选择一个合适的流地址，通过 **CDN** 进行拉流播放。播放协议通常是 **HLS**、**DASH** 或低延迟的 **HTTP-FLV**。
    *   同时，客户端会与 **消息网关** 建立WebSocket连接，用于接收和发送实时互动消息。

#### b. 核心技术点解析

1.  **协议选型：**
    *   **推流协议:** 为什么用 **RTMP/SRT**？RTMP是事实上的工业标准，兼容性好。但它基于TCP，在网络抖动时容易出现推流不稳。SRT基于UDP，拥有更好的抗丢包和重传机制，延迟更低，是未来趋势。
    *   **拉流协议:** 为什么是 **HLS/DASH/HTTP-FLV**？
        *   **HLS/DASH:** 基于HTTP，穿透性好，能充分利用CDN的缓存能力，是大规模分发的首选，但延迟较大（通常在10-30秒）。
        *   **HTTP-FLV:** 将直播流封装在HTTP的FLV格式中进行传输，延迟可以做到2-5秒，兼顾了延迟和分发效率。
        *   **超低延迟方案:** 对于需要主播与观众强互动的场景（如PK、连麦），可以采用 **WebRTC**，延迟能控制在1秒以内，但这要求对架构进行更复杂的设计。

2.  **海量用户互动系统 (百万级消息风暴):**
    这是直播系统中最具挑战的一环。一个百万在线的直播间，一次礼物特效可能需要瞬间将一条消息推送给百万人。
    *   **技术选型:** **WebSocket** + **消息队列(Kafka)**。
    *   **架构:**
        1.  客户端通过WebSocket连接到 **消息网关集群**。网关是无状态的，负责维持连接和消息透传。
        2.  当主播或用户发送一条消息（如弹幕）时，消息网关将消息（带上`room_id`）发送到 **Kafka** 的特定Topic中。Kafka天然支持高吞吐和分区。
        3.  下游有一个 **消息消费/逻辑服务集群**，它们订阅Kafka。每个服务处理一部分`room_id`的消息。
        4.  逻辑服务收到消息后，从Redis或专门的路由服务中，查询到这个`room_id`的所有观众都连接在哪些消息网关实例上。
        5.  逻辑服务将消息 **扇出 (Fan-out)** 到所有相关的消息网关实例。
        6.  消息网关再将消息广播给其维护的所有在该房间内的客户端连接。
    *   **优化:** 对于点赞这类高频但价值较低的消息，可以在客户端和网关做 **消息合并**。例如，每秒上报一次点赞总数，而不是每次点赞都发消息，可以极大减轻后端压力。

#### c. 存储选型

*   **热数据 (直播状态):** Redis Cluster。用于存储房间在线用户列表、实时礼物榜、主播状态等需要高速读写的数据。
*   **温数据 (用户信息、礼物交易):** MySQL (分库分表)。用于存储用户资料、账户余额、礼物配置、交易流水等需要事务保证的数据。
*   **冷数据 (历史弹幕、录播文件):**
    *   直播录像和截图存储在 **对象存储 (COS/S3)**，成本低，易于与CDN集成。
    *   历史弹幕、用户行为日志等，可以存储在 **ClickHouse** 或 **HBase** 这类OLAP数据库中，用于后续的数据分析和挖掘。

#### d. 高可用与高并发

1.  **全链路高可用:**
    *   **推流端容灾:** 提供备用推流地址。当主播主推流线路中断时，SDK能自动切换到备用线路。
    *   **服务集群化:** 所有服务（接入、转码、信令）都必须是无状态且集群化部署在K8s上，配合HPA实现自动扩缩容。
    *   **数据库高可用:** MySQL采用主从+哨兵模式，Redis采用哨兵或Cluster模式。
    *   **CDN容灾:** 采用 **多CDN厂商** 策略。业务层可以根据地域、ISP和实时质量监控数据，为用户智能调度最佳的CDN节点。当某个CDN厂商出现故障时，可以秒级切换。

2.  **应对突发流量 (热点事件):**
    *   **容量预估与压测:** 对于已知的大型活动（如春晚直播），必须提前进行容量规划和全链路压测。
    *   **动态扩容:** 依赖云原生的弹性伸缩能力，在流量高峰时自动增加服务实例。
    *   **限流与降级:**
        *   在API网关层设置精细化的限流策略，防止恶意请求打垮系统。
        *   当系统负载过高时，可进行服务降级。例如，暂时关闭非核心功能（如用户等级勋章显示），或将弹幕设置为只显示高价值礼物弹幕，以保证核心直播流的稳定。

#### e. 架构深度优化

1.  **全球加速与智能调度**
    *   **多Region部署:** 核心服务(接入、转码、信令等)在靠近用户和主播的多个地理区域（如硅谷、法兰克福、新加坡、上海）进行部署，减少物理距离带来的延迟。
    *   **动态加速网络:** 使用类似AWS Global Accelerator或腾讯云GAAP的专用网络，优化主播到接入服务器、以及直播中心跨Region通信的公网质量。
    *   **DNS智能调度:** 使用如Route53或DNSPod的地理位置路由(Geo-DNS)策略。当用户请求时，DNS会解析到离他最近或网络质量最好的Region的IP地址，实现"就近接入"。

2.  **极致的成本优化**
    *   **计算成本:** 转码是非常消耗CPU的计算密集型任务，但又是可容忍延迟和失败的。因此，转码集群非常适合使用云厂商的 **Spot实例（竞价实例）**，对比按需实例可节省高达80%的成本。需要设计好优雅的失败重试机制。
    *   **带宽成本:**
        *   **P2P内容分发:** 对于超热门的直播间，可以引入 **WebRTC P2P** 技术。让观看同一个直播的观众之间，相互分享已经下载好的音视频切片。这样可以形成一个巨大的分享网络，大幅降低CDN的带宽成本，因为大部分流量都在用户之间消化了。这需要复杂的客户端SDK和P2P调度服务。
        *   **智能码率调整:** 通过QoE数据分析，如果发现某个区域的用户网络普遍较差，可以动态调整该区域默认提供的码率列表，避免浪费高码率的转码和存储资源。

3.  **QoE (体验质量) 监控与数据驱动决策**
    *   **客户端精细化埋点:** 播放器SDK必须上报详细的QoE指标，如：**首帧加载时长、卡顿率、卡顿时长、码率切换次数、DNS解析耗时、CDN连接耗时** 等。
    *   **实时数据处理与告警:** 这些海量日志被实时地送入ClickHouse或类似的数据仓库。通过Flink或ClickHouse的物化视图，对关键QoE指标进行实时聚合分析。一旦某个区域、某个ISP或某个主播的流出现卡顿率飙升，可以立即触发告警，驱动运维介入或系统自动切换。
    *   **决策闭环:** 分析出的QoE数据可以反哺给调度系统。例如，调度系统发现用户通过A厂商的CDN观看体验更好，就会在后续的调度中增加A厂商的权重，形成一个正向的优化闭环。

4.  **面向未来的协议演进**
    *   **CMAF (Common Media Application Format):** HLS和DASH虽然都是基于HTTP，但媒体封装格式不同(.ts vs .fmp4)，导致需要转码和存储两份数据。CMAF是苹果和微软等公司推出的统一封装格式，可以实现"**转码一次，打包两种**"，显著降低存储和CDN缓存成本。
    *   **QUIC (HTTP/3):** 拉流协议HTTP-FLV/HLS/DASH都基于TCP，存在队头阻塞问题。QUIC基于UDP，解决了队头阻塞，并且拥有更快的连接建立、更好的拥塞控制算法。将直播分发协议迁移到QUIC上，可以为用户在弱网环境下带来更流畅的观看体验，是未来的主要演进方向。 