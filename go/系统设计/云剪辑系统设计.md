### 系统设计题："腾讯视频云剪辑"系统设计

**问题背景：**
随着UGC（用户生产内容）和PGC（专业生产内容）的爆发，越来越多的创作者希望能在云端对视频素材进行快速、便捷的剪辑和处理，并一键发布到腾讯视频平台。请你设计一个高可用、高性能、可扩展的"云剪辑"系统。

**需要考虑的核心功能点：**
1.  支持多轨道（视频、音频、字幕）的非线性编辑。
2.  支持实时预览，所见即所得。
3.  支持多人在线协作编辑。
4.  提供高效的视频渲染和导出能力。
5.  集成AI能力，如智能识别精彩片段、自动生成字幕等。

---

#### a. 整体架构

我会采用分层、微服务化的架构，将系统解耦，便于独立扩展和维护。

**架构图：**
`用户端 (Browser) <=> API网关 (Gateway) <=> 各业务微服务 <=> 核心处理层 <=> 基础设施`

1.  **接入与前端层 (Access & Frontend Layer)**:
    *   **前端应用 (Web App)**: 一个复杂的单页应用（SPA），采用React或Vue等现代框架构建。它负责提供可视化的时间线、素材库和预览窗口。
    *   **API 网关 (API Gateway)**: 所有请求的统一入口，负责认证、鉴权、路由、限流和日志记录。对外暴露RESTful API和WebSocket接口。

2.  **业务逻辑层 (Business Logic Layer)**: 一组Go语言编写的无状态微服务集群。
    *   **项目管理服务**: 核心服务之一。负责创建、保存、加载剪辑项目。管理"时间线"数据结构，并处理用户的编辑操作指令。
    *   **媒资管理服务**: 管理用户上传的私有素材，以及从腾讯视频主站引入的公有素材。处理素材的元数据、权限和存储映射。
    *   **协作服务**: 基于WebSocket，处理多人协作的信令。广播编辑操作，解决编辑冲突，保证所有协作者视图的一致性。
    *   **模板与特效服务**: 管理各类剪辑模板、转场、滤镜、贴纸等资源的元数据。
    *   **用户与计费服务**: 管理用户信息、套餐权限以及付费渲染等计费逻辑。

3.  **核心处理层 (Core Processing Layer)**: 系统的"重型工厂"，处理计算密集型任务。
    *   **异步渲染管线 (Async Rendering Pipeline)**: 负责最终视频的合成导出。这是一个由消息队列驱动的复杂工作流。
    *   **智能处理服务**: 集成各类AI原子能力，如语音识别(ASR)用于生成字幕，视频内容分析(VCA)用于识别精彩片段。这些通常是Python编写的算法服务。
    *   **媒体处理服务**: 封装FFmpeg等底层工具，执行具体的转码、切片、合成等原子操作。

4.  **基础设施层 (Infrastructure Layer)**:
    *   **对象存储 (Tencent COS)**: 存储所有媒体文件，包括原始素材、代理文件和最终成品。
    *   **数据库 (MySQL/TDSQL + MongoDB)**: 使用MySQL存储结构化的项目、用户信息；使用MongoDB存储半结构化的时间线和草稿数据。
    *   **消息队列 (Kafka)**: 解耦各个服务，是异步渲染管线的生命线。
    *   **缓存 (Redis Cluster)**: 缓存热点项目数据、用户信息、媒资元数据，加速访问。
    *   **容器编排 (TKE/K8s)**: 整个系统部署在Kubernetes上，实现服务的弹性伸缩和高可用部署。

#### b. 核心挑战与设计方案

##### 1. 时间线数据结构与实时预览

这是云剪辑体验的基石。

*   **时间线设计**:
    我会设计一个JSON数据结构来表示整个编辑项目，它就是渲染的"蓝图"。
    ```json
    {
      "project_id": "proj_123",
      "version": 42,
      "settings": { "resolution": "1080p", "fps": 30 },
      "tracks": [
        {
          "track_id": "video_track_01", "type": "video",
          "clips": [
            { "clip_id": "clip_v_001", "asset_id": "asset_abc", "in": 10.5, "out": 15.0, "start": 0.0, "filters": [...] }
          ]
        },
        {
          "track_id": "audio_track_01", "type": "audio", ... },
        {
          "track_id": "subtitle_track_01", "type": "subtitle", ... }
      ]
    }
    ```
*   **实时预览的实现**:
    直接在云端渲染每一帧画面传给前端是不现实的，延迟太高。因此必须采用**代理+前端合成**的策略。
    1.  **代理文件 (Proxy File)**: 任何高清素材在入库时，都会被"媒体处理服务"异步生成一个低码率、低分辨率（如480p）的代理版本。这个代理版本在时间上与原片精确对齐。
    2.  **前端合成预览**: 用户在时间线上拖拽操作时，前端下载的是轻量的代理文件。利用现代浏览器的`<video>`和`<canvas>` API，可以在前端实现大部分的实时预览合成，包括视频轨的拼接、音频轨的混音、字幕和贴纸的叠加。这能提供毫秒级的反馈。
    3.  **复杂特效预览**: 对于无法在前端实现的复杂特效（如AI滤镜），前端可以向"媒体处理服务"发起一个"单帧预览"请求，后端快速渲染出应用特效后的那一帧图像返回给前端展示。

##### 2. 高效异步渲染管线

这是保证产出效率的关键。

1.  **触发**: 用户点击"导出"，项目管理服务将最终版的时间线JSON作为一个消息投递到Kafka的`render_tasks`主题中。
2.  **任务分发与DAG生成**:
    一个"渲染调度器"服务消费此消息。它解析时间线，并将其转换成一个**渲染任务有向无环图（DAG）**。例如，"渲染片段1"、"渲染片段2"是并行节点，"合成片段1和2"是它们的子节点。
3.  **分布式工作节点 (Render Workers)**:
    *   我们有一个庞大的、基于K8s的渲染Worker集群。每个Worker都是一个包含FFmpeg和AI模型的容器。
    *   Worker从任务队列（如Redis List或专用队列）中拉取一个原子化的渲染任务（如：将`asset_abc`从10.5秒截取到15.0秒并应用滤镜X）。
    *   Worker从COS下载原始高分辨率素材，执行任务，然后将产出的中间片段上传回COS。
4.  **合成与收尾**:
    当DAG中的所有叶子节点任务完成后，"渲染调度器"会触发最终的"合成"任务。该任务使用FFmpeg的`concat`等功能将所有中间片段合并成最终的视频。
5.  **状态与通知**: 整个渲染过程的状态（排队中、渲染中、成功、失败）会实时更新到项目数据库，并通过WebSocket通知前端。

##### 3. 多人实时协作

对标Google Docs的体验，需要解决协同冲突。

*   **技术选型**: CRDT (Conflict-free Replicated Data Types)。相比于OT（Operational Transformation）需要中心化服务器协调，CRDT更适合分布式系统，每个客户端可以独立应用操作，最终能保证达到一致的状态。
*   **实现**:
    1.  前端的每次编辑操作（如拖动剪辑、修改文字）都会生成一个CRDT操作对象。
    2.  该操作对象立即在本地应用，实现即时反馈。
    3.  同时，通过WebSocket将该操作对象发送到"协作服务"。
    4.  "协作服务"像一个广播路由器，将这个操作转发给同一项目组的其他所有客户端。
    5.  其他客户端收到操作后，在本地的CRDT数据结构上应用该操作。CRDT的数学特性保证了即使操作的接收顺序不同，最终所有客户端的时间线状态都会收敛到一致。
    6.  "项目管理服务"会定期或在用户不活跃时，将最终收敛的时间线版本持久化到数据库中。

#### c. 存储选型

*   **COS对象存储**: 毫无疑问是所有媒体文件的最佳选择。利用其海量、高可用、低成本的特性。并结合CDN进行素材和成品的加速分发。
*   **MongoDB**: 用于存储时间线JSON文档。其灵活的Schema非常适合迭代频繁的复杂文档结构。查询内嵌文档也相对方便。
*   **MySQL/TDSQL**: 存储强一致性要求的结构化数据，如用户信息、项目元数据、权限、订单等。
*   **Redis Cluster**: 用于高并发的缓存层。缓存热点项目数据、用户会话、协作信令、渲染任务队列等。

#### d. 高可用与性能优化

*   **无状态服务**: 所有业务逻辑层的Go服务都设计为无状态的，可以随意水平扩缩容。
*   **计算资源弹性**: 渲染Worker集群配置在K8s的弹性节点组上，可以根据渲染任务队列的长度自动扩缩容，实现成本与效率的平衡。在业务高峰期（如节假日前）自动扩容，在凌晨低谷期自动缩容。
*   **数据库高可用**: MySQL采用主从热备+读写分离。MongoDB和Redis采用集群模式，数据分片，多副本保证可用性。
*   **全球加速**: 对上传和下载链路，使用COS的全球加速功能，优化海外创作者的访问体验。成品分发则依赖腾讯视频成熟的CDN网络。
*   **失败重试与隔离**: 异步管线中的任何一步失败，都应有重试机制。对于长时间无响应的Worker，调度器应将其标记为失败并重新派发任务。通过资源队列等方式对不同优先级的用户（如VIP用户）进行渲染资源隔离。 