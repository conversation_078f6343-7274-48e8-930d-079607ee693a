### 系统设计题："腾讯视频搜索框Suggest系统"设计

#### a. 整体架构与核心挑战

**1. 核心挑战:**

*   **低延迟 (Low Latency):** 用户每输入一个字符，都可能触发一次请求。整个P99响应时间必须控制在50ms以内，否则用户会感到明显的卡顿。
*   **高并发 (High QPS):** 头部视频平台的搜索QPS非常高，Suggest系统作为其前置，请求量会是最终搜索请求的数倍甚至一个数量级以上。
*   **数据多样性与实时性:** 建议词来源复杂，包括媒资库的标题、演员名（PGC/UGC内容）、运营配置的热词、实时涌现的流行语等。需要快速将新内容、新热点反映到建议中。
*   **个性化与准确性:** 需要结合用户的历史搜索、观看行为，提供个性化的建议，并确保建议结果的相关性和点击率。
*   **内容安全 (Content Safety):** 必须防止违规、低俗、涉政或带有攻击性的词语出现在搜索建议中。这既是法规要求，也是保护用户体验的必要措施。

**2. 整体架构:**

这是一个典型的读多写少、对读取性能要求极致的场景。我会设计一个多数据源、多级缓存、聚合排序的架构。

*   **数据源层 (Data Sources):**
    *   **离线索引 (Offline Index):** 存储全量的、基础的建议词。如：视频标题、演员/导演名、PGC/UGC内容等。
    *   **实时索引 (Real-time Index):** 存储分钟级的热搜词、飙升词。
    *   **个性化数据 (Personalized Data):** 存储用户的个人搜索历史。
*   **数据处理层 (Data Processing):**
    *   **离线处理 (Offline):** 使用Spark或Flink，定期（如每小时）从内容库(MySQL)、日志(HDFS)中抽取数据，构建全量索引，推送到Elasticsearch集群。
    *   **实时处理 (Real-time):** 使用Flink消费实时的用户搜索行为日志(Kafka)，通过滑动窗口计算最近5-10分钟的热门搜索词，写入高速缓存/索引。
*   **服务层 (Serving Layer):**
    *   **Suggest API服务:** Go编写的无状态服务，负责接收前端请求。
    *   **聚合与排序服务 (Aggregator):** 并行查询上述三个数据源，对返回的结果进行去重、排序、截断，生成最终的候选列表。
*   **缓存层 (Caching):**
    *   **CDN/网关缓存:** 对非个性化的热门查询前缀做短时间缓存（5-10秒）。
    *   **服务本地缓存:** 在Suggest API服务内部，使用LRU策略缓存高频查询前缀的结果。

#### b. 数据源与索引构建

**1. 索引技术选型:**

*   **离线全量索引:** **Elasticsearch** 是最佳选择。
    *   **原因:** 它提供强大的分词能力（特别是通过IK、Pinyin插件支持中文和拼音）、相关性排序模型(BM25)，以及成熟的水平扩展和高可用方案。
    *   **索引内容:** `suggest_text` (建议词), `entity_id` (实体ID), `entity_type` (video/star/topic), `base_score` (基础热度分), `pinyin_field` (拼音/首字母)。
*   **实时/热点索引:** **内存Trie树 (Prefix Tree) + Redis**。
    *   **原因:** Trie树是实现高效前缀匹配的经典数据结构，查询复杂度为O(K)，K为前缀长度，速度极快。将整个Trie树结构序列化后存储在Redis中，可以实现分布式共享和快速加载。
    *   **构建:** Flink实时计算出Top 5000的热词，构建成一个Trie树。每分钟生成一个新版本的Trie，并平滑切换。
*   **个性化数据:** **Redis Hash 或 ZSET**。
    *   **原因:** 用户历史数据量不大，但读取要求快。
    *   **结构:** `Key: user_history:{user_id}`, `Member: search_query`, `Score: timestamp`。用ZSET可以按时间排序，轻松获取最近的搜索历史。

#### c. 服务层核心逻辑

1.  当用户输入前缀 `q`，Suggest API服务触发聚合逻辑。
2.  **并行扇出查询 (Fan-out):**
    *   `goroutine 1:` 查询个性化Redis，获取该用户的历史记录中以 `q` 开头的词。
    *   `goroutine 2:` 查询实时Trie/Redis，获取热点词中以 `q` 开头的词。
    *   `goroutine 3:` 查询Elasticsearch，使用`match_phrase_prefix`查询获取全量匹配的词。
3.  **聚合与排序 (Aggregation & Ranking):**
    *   设置一个较短的超时时间（如30ms）等待所有goroutine返回。
    *   **去重:** 使用`map[string]SuggestItem`对来自不同数据源的相同建议词进行去重。
    *   **排序:** 这是核心。排序分 `FinalScore` 的计算是一个动态加权过程：
        `FinalScore = w1 * BaseScore + w2 * TrendScore + w3 * PersonalizationBonus`
        *   `BaseScore`: 来自ES，代表内容的静态权威度或历史热度。
        *   `TrendScore`: 来自实时Trie，代表当前的流行程度。
        *   `PersonalizationBonus`: 如果词条出现在用户历史中，给一个较高的加权分。
        *   权重`w1, w2, w3`可通过AB实验平台动态调整，以优化最终的点击率。
4.  截断返回Top 10结果。

#### d. 性能与高可用

*   **极致的缓存策略:**
    *   **热点前缀本地缓存:** 对于最高频的查询前缀（如"三"、"庆"），其查询结果可以在Suggest服务的本地内存中（如使用FreeCache）缓存几十秒。这可以覆盖大量请求，避免访问后端。
    *   **空结果缓存:** 对查询无结果的前缀，也应缓存一个"空结果"，防止缓存穿透。
*   **高可用与降级:**
    *   **无状态服务:** Suggest API和聚合服务都是无状态的，可以部署在K8s上，通过HPA进行弹性伸缩。
    *   **依赖降级:** 使用`gobreaker`等熔断器库。
        *   若个性化服务超时或失败，则只返回热点和通用结果。
        *   若实时Trie服务超时或失败，则降级为只返回通用结果。
        *   若Elasticsearch集群抖动，可以暂时只提供来自实时和个性化数据源的建议。
        *   最坏情况下，如果所有后端都不可用，可以返回一个静态的、预先配置好的兜底热门词列表，或直接返回空，保证服务不崩溃。
*   **连接优化:** 服务与后端Redis/ES之间使用长连接池，减少握手开销。

#### e. 优化与扩展

*   **拼音与纠错:**
    *   **拼音支持:** 在ES中配置Pinyin插件，将中文标题同时索引为全拼和首字母。例如"庆余年" -> `qingyunian`, `qyn`。这样用户输入拼音或首字母也能匹配。
    *   **纠错:** 对于简单的拼写错误，可以利用ES的Fuzzy Query或Suggest API中的`term suggester`来实现。
*   **结果多样性:** 在排序阶段引入多样性惩罚。如果返回的Top N结果中，有多个条目指向同一个IP（如《庆余年第一季》、《庆余年第二季》），可以适当降低相似条目的分数，为其他类型的实体（如演员"张若昀"）留出位置。
*   **A/B测试:** 建议系统的排序模型、UI展示方式、召回策略等，都非常适合接入AB实验平台，通过线上真实流量来验证和迭代算法，持续优化业务指标（如搜索转化率）。

#### f. 内容安全与审核

内容安全是搜索建议系统不可或缺的一环，需要多层过滤和审核机制来防止不当内容的出现。

*   **多级过滤体系:**
    *   **机审过滤:** 这是第一道防线。所有进入建议系统的候选词（无论是来自媒资库、用户搜索日志还是运营配置），都必须先通过机器审核系统。该系统通常基于一个庞大的、动态更新的**敏感词库**进行匹配过滤。
    *   **模型过滤:** 对于一些变体、谐音或隐晦的表达，简单的关键词匹配难以覆盖。可以引入基于NLP（自然语言处理）的文本分类模型，对候选词进行风险等级判断（如：正常、低俗、涉政等），过滤掉高风险词汇。
    *   **人工审核:** 对于机器审核标记为疑似风险或热度极高的新增词汇，应进入人工审核流程，由运营人员进行最终判断。运营也可以直接添加或拉黑某些建议词。

*   **反馈与闭环:**
    *   **用户举报:** 提供便捷的用户举报入口。一旦有用户举报某个建议词，该词应被立即下线，并进入人工审核流程。
    *   **模型迭代:** 将人工审核的结果和被举报的词汇作为新的训练样本，持续迭代和优化文本分类模型，提高机审的准确率。

*   **架构集成:**
    *   内容审核可以作为数据处理流程中的一个独立微服务。无论是离线构建全量索引，还是实时计算热词，数据都必须先流经审核服务，只有"安全"的数据才能被索引和缓存。 