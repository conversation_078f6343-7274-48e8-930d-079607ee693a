### 系统设计题："腾讯视频播放质量监控系统 (QoE/QoS System)"

#### 面试官视角

好的，这是一个非常核心的后台系统，直接关系到用户的留存和满意度。设计这样一个系统，需要综合考虑海量数据上报、实时流式计算、多维数据分析和快速告警定位等挑战。我的设计思路如下：

### a. 整体架构

这是一个典型的海量数据采集、处理和应用的综合性平台。其架构可以分为以下几层：

```mermaid
graph TD
    A[Client SDK<br/>客户端采集] -->|设备/播放日志| B[Data Gateway & Kafka <br/>数据接收与缓冲]
    B --> C[Real-time Computing (Flink) <br/>实时计算]
    C --> D[Real-time Storage & Alerting <br/>实时库/告警(ClickHouse/Prometheus)]
    C --> E[Batch Computing (Spark/Clickhouse) <br/>离线计算]
    E --> F[Batch Storage (数据湖, S3)<br/>原始数据]
    D --> G[Application Service <br/>监控API/告警服务/诊断平台]
    F --> G
    G --> H[监控大盘, Ad hoc数据查询, Single Trace查询]

    style A, fill: #d3eafb, stroke: #131313, stroke-width: 2px
    style B, fill: #ffcccc, stroke: #131313, stroke-width: 2px
    style G, fill: #d3eafb, stroke: #131313, stroke-width: 2px
    style H, fill: #d3eafb, stroke: #131313, stroke-width: 2px
```

1.  **数据采集层 (Client SDK)**：
    内嵌在App、Web、OTT等所有客户端播放器中的一个轻量级SDK。它负责在播放生命周期的各个阶段（如开始播放、播放中、卡顿、seek、暂停、结束、错误）采集关键性能指标(KPI)，并生成结构化日志。

2.  **数据接收与传输层 (Ingestion & Message Queue)**：
    *   **高可用数据网关 (Gateway)**：一组无状态的Go服务，接收来自全球客户端SDK的上报。网关只做最轻量级的处理，如鉴权、协议转换，然后迅速将数据推送到下游的Kafka集群。
    *   **消息队列 (Kafka)**：作为数据缓冲和解耦的核心。所有原始日志都会进入一个统一的Kafka Topic。利用Kafka的分区机制，可以轻松实现水平扩展，应对峰值流量。

3.  **数据计算层 (Computation)**：
    *   **实时计算 (Real-time)**：使用 **Apache Flink** 作为核心引擎，消费Kafka中的原始日志，进行实时的聚合、分析和告警判断。例如，计算每分钟、每个CDN节点、每个城市的平均卡顿率。
    *   **离线/批量计算 (Batch)**：使用 **Spark** 或 **ClickHouse** 的物化视图能力，对全量原始数据进行更复杂的批量分析。例如，计算上周不同网络类型下的P95起播耗时，或者为某个新上线版本做专项数据分析。

4.  **数据存储层 (Storage)**：
    *   **原始数据湖**：所有从Kafka接入的原始日志，都会被不间断地投递到成本低廉的对象存储中（如AWS S3、HDFS），并使用 Parquet/ORC 列式存储格式。这是所有数据分析的黄金数据源。
    - **实时指标库 (Time-Series DB)**：Flink实时计算出的聚合指标（如卡顿率、起播耗时等），会写入 **ClickHouse** 或 InfluxDB。这类时序数据库非常适合监控仪表盘的高效查询。
    - **告警/问题实例库**：实时计算发现的异常事件（如某CDN节点故障），其详细信息会存储在 **MySQL** 或 PostgreSQL中，用于问题的跟踪和管理。

5.  **应用与服务层 (Application & Service)**：
    *   **Dashboard & API**: 提供可视化的监控大盘和数据查询API。前端通过API查询ClickHouse中的聚合指标，展示给运维和开发人员。
    *   **告警系统**: Flink计算任务检测到指标异常后，通过Webhook、企业微信、短信等方式，将告警信息推送给相应的负责人。
    *   **根因定位平台**: 提供一个自助式的数据诊断平台，允许工程师通过下钻、筛选、对比等方式，在海量数据中快速定位问题根源。

### b. 关键技术点 - 数据采集与上报

数据源的质量决定了整个系统的价值。
*   **核心指标 (KPIs)**：
    *   **起播性能**: `First Frame Latency` (首帧耗时), DNS解析耗时, 建连耗时, CDN响应首包耗时。
    *   **流畅度**: `Stutter Rate` (卡顿率，总卡顿时长/总播放时长), `Stutter Count` (卡顿次数)。
    *   **播放成功率**: `Playback Success Rate` (播放成功次数/总尝试播放次数)。
    *   **错误码**: 播放器定义的各种错误码，如404 (内容不存在), 403 (无版权), 超时等。
    *   **会话信息**: `Session ID` (唯一标识一次播放), `Content ID`, `User ID`, `App Version`, `OS Version`。
    *   **网络与环境**: `CDN Node IP`, `Client IP`, `ISP` (运营商), `Network Type` (4G/5G/WiFi), `Geo` (地理位置)。

*   **上报策略**:
    *   **事件上报**: 在播放生命周期的关键节点（`start`, `end`, `error`, `stutter_start`, `stutter_end`）立即上报。
    *   **心跳上报**: 在播放过程中，每隔1分钟上报一次当前状态的"快照"，即使没有发生任何事件。这对于统计在线用户数、掌握大盘整体健康度至关重要。
    *   **本地缓存与重试**: SDK必须具备本地缓存能力。当网络请求失败时，日志会暂存在本地，在下次合适的时机（如网络恢复、App下次启动）进行重试上报，确保数据不丢失。

### c. 关键技术点 - 实时计算与告警

这是整个系统的大脑，要求"快"和"准"。
*   **技术选型**: **Flink**。理由同热点榜单系统，其事件驱动的真流式处理、强大的状态管理和精确一次 (Exactly-Once) 语义是此场景的最佳选择。
*   **核心计算逻辑**:
    1.  **流关联 (Stream Join)**: 消费播放日志流的同时，可以关联一个来自CMDB（配置管理数据库）的流，为数据附加上更丰富的维度信息，例如将CDN节点的IP地址关联上节点的机房、归属厂商等。
    2.  **多维度聚合**: 使用 `keyBy()` 按多种维度组合进行开窗聚合。例如，`keyBy(cdn_provider, city, isp)` 然后使用一个5分钟的滑动窗口 (`SlidingWindow`) 来计算窗口内的平均卡顿率、起播耗时P90分位值等。
    3.  **异常检测与告警**: 这是最核心的部分。
        *   **固定阈值**: 对某些核心指标设置固定告警阈值，例如，任何维度的播放成功率低于95%，立即告警。
        *   **同比/环比**: 更高级的策略是动态阈值。例如，Flink作业会记住每个维度（如"CDN-A"在"北京"）过去4周同一时间点的指标基线。如果当前值相比上周同时间点上涨超过30%（例如卡顿率），则触发告警。这能有效过滤掉业务高峰期带来的正常波动，减少误报。
        *   **告警收敛**: 当一个大范围的故障（如某个骨干网中断）发生时，可能会同时触发成百上千个告警。告警系统必须有收敛机制，例如，如果在1分钟内收到超过50个关于同一CDN厂商的告警，就将其合并为一个根源告警，并抑制掉后续的同类告警。

### d. 存储方案的权衡

*   **为什么不用Elasticsearch？**: 虽然ES能做查询和分析，但在海量指标的写入和聚合查询性能上，不如专业的时序数据库（如ClickHouse/InfluxDB）。ES的成本也相对更高。在这里，更适合用ES来做单个`Session ID`的全链路日志检索。
*   **为什么用ClickHouse？**: 它是一个面向OLAP的列式数据库，对于聚合查询（`GROUP BY`）性能极强。其宽表模型非常适合存储带有大量维度的监控指标。写入性能也非常高，可以轻松支撑Flink的实时写入。
*   **数据生命周期管理**: 在ClickHouse和S3中，都必须设置数据生命周期(TTL)。例如，ClickHouse中聚合指标保留3个月，S3上的原始日志保留1年，超过期限的数据可以自动归档或删除，以控制成本。

### e. 数据应用：从"看报表"到"智能诊断"

一个高级的监控系统不应止步于展示报表和告警。
*   **自助诊断平台**: 提供一个Web UI，让非数据专家（如后台开发工程师）也能分析问题。用户可以通过点选的方式，选择时间范围、指标、以及任意维度（CDN、版本、城市...）进行下钻、过滤、对比。其后端直接将前端操作翻译成对ClickHouse或Presto的SQL查询。
*   **单用户链路追踪**: 当客服收到用户反馈"我的视频很卡"时，运营人员可以输入用户的`UserID`，系统能立即拉出该用户近期的所有播放会话 (`Session ID`)。点击任意一个会话，就能看到该次播放的完整生命周期日志：从DNS解析开始，到从哪个CDN节点下载，下载速率多少，何时发生卡顿，卡顿了多久，最后因何种原因结束。这需要将同一`Session ID`的日志流写入到一个适合做单Key查询的系统，如HBase或Elasticsearch。
*   **智能根因推荐**: 这是最高级的形态。当告警发生时，系统不仅是发出告警，还会自动运行一系列诊断算法。例如，发现上海地区的卡顿率飙升后，系统会自动下钻分析，对比该地区不同CDN厂商、不同运营商的表现，如果发现95%的卡顿都集中在"CDN-B"厂商的"联通"用户上，它就会在告警信息中直接给出根因推断："上海联通用户访问CDN-B出现故障，请检查CDN-B上海节点"，极大地缩短了MTTR（平均修复时间）。

这个系统设计涵盖了从端到端的全链路数据流，考验了候选人在数据架构、实时计算、存储选型和数据应用等多方面的综合能力，是一个很好的高级岗位面试题。 