# 系统设计题："内容媒资入库与处理管线"系统

### a. 整体架构与核心流程

这是一个典型的事件驱动、基于工作流的分布式处理系统。

**核心流程:**

1.  **内容接入 (Ingestion)**: 版权方或UGC创作者通过专用的上传工具或API网关，将视频源文件、字幕、海报、以及元数据（metadata.xml）等物料上传到**临时存储区**（通常是对象存储，如腾讯云COS）。每次上传批次会生成一个唯一的`package_id`。
2.  **触发入库 (Triggering)**: 物料上传完成后，上传服务会发送一个"新内容包到达"的事件到消息队列（如Kafka/Pulsar），消息体包含`package_id`和临时存储路径。
3.  **工作流启动 (Workflow Initiation)**: 一个**工作流管理服务 (Workflow Manager)** 消费此消息，根据内容的类型（电影、剧集、UGC短视频等）和预设的模板，创建一个工作流实例（Workflow Instance）。这个工作流实例本质上是一个有向无环图 (DAG)，定义了所有需要执行的处理步骤和它们之间的依赖关系。
4.  **任务分发与执行 (Task Dispatch & Execution)**:
    *   工作流管理器按照DAG的拓扑顺序，将可以执行的**原子任务**（如：格式分析、转码、截图、添加水印、内容审核）逐一投递到不同的任务队列中。
    *   各种功能的**处理节点 (Worker Nodes)** 集群（如转码集群、审核集群）订阅自己关心的任务队列，获取任务并执行。这些Worker通常是无状态的，并部署在K8s上以实现弹性伸scaling。
    *   Worker执行完成后，将结果（如转码后的视频地址、审核结果）写回，并更新任务状态。
5.  **状态推进与完成 (State Progression & Completion)**: 工作流管理器监听任务完成事件，更新工作流实例中对应节点的状态。当一个节点完成后，它会检查并触发其所有下游依赖节点。当DAG中所有节点都成功完成后，整个工作流结束。
6.  **媒资入库 (Asset Registration)**: 工作流成功结束后，工作流管理器会触发最后一步："媒资注册"。它会收集所有处理过程产生的元数据和媒资地址，组装成一个完整的、结构化的媒资对象，并将其正式写入**媒资中心数据库 (MAM DB)**。同时，视频文件会从临时存储区移动到正式存储区。此时，内容才处于"可发布"状态。
7.  **对外分发 (External Notification)**: 媒资成功入库后，工作流管理器会发布一个"内容就绪"事件到专用的Kafka主题（如 `AssetReady`）。消息中包含内容ID和关键信息。下游的各个业务系统（如内容分发、推荐、搜索等）会消费此事件来触发后续的业务逻辑，实现了核心管线和业务的解耦。

**架构图:**
```mermaid
graph TD
    subgraph 接入层
        A[内容提供方] -- 视频/元数据 --> B(上传网关/SDK)
        B -- 上传至 --> C{COS临时存储}
    end

    subgraph 处理与调度层
        B -- 触发事件 --> D[Kafka 主题: NewPackage]
        E[工作流管理器] -- 消费 --> D
        E -- 创建工作流实例 --> F(Workflow DB)
        E -- 分发原子任务 --> G[Kafka 主题: TranscodeTask]
        E -- 分发原子任务 --> H[Kafka 主题: AuditTask]
        E -- 分发原子任务 --> I[Kafka 主题: ...]
    end

    subgraph 执行层 (K8s)
        J[转码Worker] -- 消费 --> G
        K[审核Worker] -- 消费 --> H
        L[...] -- 消费 --> I
        J -- 输出 --> M{COS正式存储}
        K -- 输出 --> M
        J -- 上报结果 --> E
        K -- 上报结果 --> E
    end
    
    subgraph 存储与服务层
      E -- 成功后注册 --> N[媒资中心DB(MySQL/PG)]
      E -- 成功后发布 --> R[Kafka 主题: AssetReady]
      M -- 提供播放地址 --> P[CDN]
      Q[媒资管理后台] -- 查询/管理 --> N
      S[下游业务系统] -- 消费 --> R
      O[搜索引擎(ES)] -- 消费以更新索引 --> R
      Q -- 搜索 --> O
    end

    linkStyle 0 stroke-width:2px,fill:none,stroke:blue;
    linkStyle 1 stroke-width:2px,fill:none,stroke:blue;
    linkStyle 2 stroke-width:2px,fill:none,stroke:orange;
    linkStyle 3 stroke-width:2px,fill:none,stroke:orange;
    linkStyle 4 stroke-width:2px,fill:none,stroke:orange;
    linkStyle 5 stroke-width:2px,fill:none,stroke:orange;
    linkStyle 6 stroke-width:2px,fill:none,stroke:green;
    linkStyle 7 stroke-width:2px,fill:none,stroke:green;
    linkStyle 8 stroke-width:2px,fill:none,stroke:green;
    linkStyle 9 stroke-width:2px,fill:none,stroke:purple;
    linkStyle 10 stroke-width:2px,fill:none,stroke:purple;
    linkStyle 11 stroke-width:2px,fill:none,stroke:purple;
    linkStyle 12 stroke-width:2px,fill:none,stroke:purple;
    linkStyle 13 stroke-width:2px,fill:none,stroke:red;
    linkStyle 14 stroke-width:2px,fill:none,stroke:darkred;
    linkStyle 15 stroke-width:2px,fill:none,stroke:red;
    linkStyle 16 stroke-width:2px,fill:none,stroke:red;
    linkStyle 17 stroke-width:2px,fill:none,stroke:darkred;
    linkStyle 18 stroke-width:2px,fill:none,stroke:darkred;
    linkStyle 19 stroke-width:2px,fill:none,stroke:red;
```

### b. 核心挑战与设计考量

1.  **可靠性与容错**:
    *   **幂等性**: 所有任务接口必须设计成幂等的。因为网络问题或节点故障，任务可能会被重试。例如，对同一个源文件发起两次相同的转码任务，应该只产生一个结果或第二个任务能直接返回已有的结果。
    *   **断点续传/续处理**: 对于动辄几十上百GB的源文件，上传和处理过程必须支持断点续传。工作流也必须是持久化的，即使工作流管理器宕机，重启后也能从数据库（`Workflow DB`）恢复到之前的状态，继续执行。
    *   **优雅的失败处理**: 任何一个环节失败，都不能导致整个流程卡死。工作流需要支持自动重试、失败降级（如高清转码失败，可降级用标清）、或人工介入处理。失败信息需要清晰地上报到告警系统。

2.  **效率与性能**:
    *   **并行处理**: 工作流中的DAG设计，天然支持任务的并行化。例如，不同码率的转码任务、截图任务、音频轨抽取任务，在源文件分析完成后就可以并行执行。
    *   **资源密集型任务优化**: 转码是CPU密集型任务。我们会使用专门的转码硬件（如GPU卡）或云厂商提供的媒体处理服务，并将其池化管理。通过任务队列的优先级和消费者组，可以实现对不同业务（如VIP内容、热点内容）的资源倾斜。
    *   **I/O优化**: 视频文件巨大，网络I/O和磁盘I/O是主要瓶颈。Worker节点应尽可能部署在离COS存储地域近的机房。可以采用COS的内网访问域名，避免公网带宽消耗。对于超大文件，可以考虑分片处理。

3.  **可扩展性**:
    *   **微服务化**: 每个处理功能（转码、审核、水印、加密）都封装成独立的微服务。当需要支持新的功能（如智能封面、内容指纹提取）时，只需开发一个新的Worker服务并将其注册到工作流模板中即可，对现有流程无侵入。
    *   **弹性伸缩**: 整个Worker层都构建在K8s之上。可以根据各个任务队列的堆积长度，配置HPA (Horizontal Pod Autoscaler) 自动伸缩各个Worker集群的实例数量，从容应对业务潮汐。

### c. 工作流引擎设计

这是整个系统的"大脑"。

*   **技术选型**:
    *   **成熟开源方案**: 优先考虑使用 [Temporal](https://temporal.io/) 或 [Cadence](https://cadenceworkflow.io/)。它们提供了非常强大的工作流抽象（Workflow, Activity），内置了状态持久化、重试、定时器、信号等机制，能极大简化开发复杂性。
    *   **自研方案**: 如果业务场景相对简单固定，也可以基于**状态机**模型自研。
        *   **状态存储**: 将每个工作流实例的状态和DAG结构持久化到MySQL或PostgreSQL中。
        *   **状态驱动**: 依赖消息队列驱动状态流转。工作流管理器每完成一个任务，就更新数据库中的状态，并查找下一步可以执行的任务，再将其投递到消息队列。这种方式相对轻量，但需要自己处理很多分布式事务和一致性的问题。

*   **核心功能**:
    *   **模板管理**: 支持通过Web界面可视化地拖拽、编排和管理工作流模板（DAG）。
    *   **版本控制**: 工作流模板需要有版本。老的内容可以继续按旧版流程执行完毕，新的内容则使用新版流程。
    *   **可视化监控**: 提供每个工作流实例的实时进展视图，可以清晰地看到哪个节点正在运行、哪个失败了、耗时多久，方便运营和开发人员排查问题。

### d. 媒资存储方案

*   **对象存储 (COS/S3)**:
    *   **职责**: 存储所有媒体文件，包括源文件、中间处理文件、最终的转码输出文件（各种码率）、海报、字幕等。
    *   **设计**:
        *   使用不同的Bucket来隔离临时区、正式区、冷备区。
        *   利用对象存储的生命周期管理能力，自动将旧的、不活跃的源文件归档到低成本的存储类型（如归档存储、深度归档存储）。
        *   开启版本控制，防止文件被误删或覆盖。

*   **关系型数据库 (MySQL/PostgreSQL)**:
    *   **职责**: 存储结构化的核心媒资元数据。例如，`content_id`, `title`, `description`, `tags`, 演员, 导演, 版权周期, 以及关联的播放地址（指向COS）。
    *   **设计**: 数据库设计需要遵循范式，通过清晰的主外键关系保证数据一致性。例如，会有剧集表、季表、集表等。对核心表需要进行分库分表以应对海量数据。

*   **搜索引擎 (Elasticsearch)**:
    *   **职责**: 提供对媒资的快速、复杂条件的检索能力。例如，让运营人员可以根据标题、演员、标签、甚至视频的OCR文字等多种维度进行模糊搜索。
    *   **设计**: 通过Canal或其它数据同步工具，监听媒资数据库的binlog，将数据准实时地同步到Elasticsearch中，构建索引。

### e. 可观测性 (Observability)

一个复杂的分布式管线系统，没有良好的可观测性就是个黑盒，一旦出问题就是灾难。

*   **Logging**: 所有服务（网关、工作流管理器、Workers）都输出结构化的JSON日志。日志中必须包含 `trace_id`，用于串联一次完整处理流程中的所有相关日志。日志统一收集到ELK或Loki平台。
*   **Metrics**:
    *   **业务指标**: 各任务队列长度、任务处理成功率/失败率、平均处理耗时、工作流完成的P95/P99耗时。
    *   **系统指标**: CPU、内存、磁盘、网络IO。
    *   使用Prometheus + Grafana进行采集、存储和可视化，并配置核心指标的告警。
*   **Tracing**: 引入分布式追踪系统（如Jaeger, SkyWalking）。从上传网关开始生成一个 `trace_id`，并通过消息队列和RPC调用的header一路透传下去。这样，可以清晰地看到一个内容从接入到入库的完整调用链，快速定位性能瓶颈和错误的根源。 