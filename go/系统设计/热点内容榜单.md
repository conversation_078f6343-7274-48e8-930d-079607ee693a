# 热点内容榜单系统设计

本文档旨在设计一个能够处理海量用户行为、实时计算并展示热点内容榜单的系统。该系统需要具备高可用、高并发和有效的反作弊能力。

## 1. 系统目标与挑战

### 1.1. 核心目标

*   **实时性**: 榜单需在分钟级别内更新，以反映最新的内容热度。
*   **准确性**: 精确计算内容热度，并有效过滤作弊行为。
*   -**高并发**: 能够应对千万级的榜单读取QPS。
*   **高可用**: 系统无单点故障，能够在部分组件失效时持续提供服务。
*   **可扩展性**: 架构设计能够支持未来的业务增长和流量增长。

### 1.2. 主要挑战

*   **数据风暴**: 如何处理海量、瞬时高并发的用户行为日志上报。
*   **计算效率**: 如何在海量内容中高效、低延迟地完成热度计算和TopN排序。
*   **存储压力**: 如何以低成本、高效率的方式存储和读取频繁更新的榜单数据。
*   **热点冲击**: 如何应对热点事件引发的突发流量，保证系统稳定。
*   **作弊对抗**: 如何识别并剔除刷榜、刷量等作弊行为，保证榜单公正性。

---

## 2. 整体架构

系统采用典型的流式处理架构，分为数据采集、数据计算、存储服务和前端展现四个层次。

*   **数据采集层**:
    *   客户端产生的用户行为日志（播放、点赞等）通过日志网关上报至 **Kafka**。
    *   内容的元数据（标题、分类等）通过 Canal 监听 MySQL binlog 后也推送到 **Kafka**。
*   **数据计算层**:
    *   **Apache Flink** 作为核心计算引擎，消费 Kafka 中的行为数据和媒资数据。
    *   Flink 内部进行数据清洗、双流 Join、滑动窗口聚合和 TopN 计算。
*   **存储与服务层**:
    *   实时榜单（热数据）存储在 **Redis Cluster** 中，提供高性能读写。
    *   全量及历史榜单（冷数据）持久化到 **ClickHouse**，用于分析和历史查询。
    *   无状态的 Go API 服务集群负责从存储层读取数据，向前端提供接口。
*   **前端展现层**:
    *   客户端通过 **CDN** 和 API 网关请求榜单数据，CDN 缓存大幅降低后端压力。

---

## 3. 核心模块设计

### 3.1. 计算引擎 (Apache Flink)

*   **技术选型理由**:
    *   **真流式处理**: Flink 是逐条处理事件的真流式引擎，延迟可达毫秒级，完美匹配分钟级更新的需求。
    *   **强大的状态管理**: 成熟的状态后端（如 RocksDB）支持海量 Key 的状态存储和 Exactly-Once 语义，保证计算的准确无误。
    *   **灵活的窗口操作**: 提供滑动窗口、滚动窗口等，能满足复杂的业务统计需求。

*   **热度计算逻辑**:
    1.  **双流Join**: Flink 消费行为日志和媒资信息两个 Topic，按 `content_id` 进行关联。
    2.  **数据清洗**: 过滤无效数据和作弊流量。
    3.  **窗口聚合**: 使用**滑动窗口**（例如：窗口大小5分钟，滑动步长1分钟）聚合每个视频的热度。
        *   **热度公式**: `Score = W1 * play_count + W2 * like_count + ...` (权重 W 可后台配置)
    4.  **TopN计算**: 利用 Flink 内置的 `KeyedProcessFunction` 和 TopN 算子高效计算出各分类下的 Top 榜单。

### 3.2. 存储选型 (Redis & ClickHouse)

*   **热数据存储: Redis**
    *   **数据结构**: `Sorted Set (ZSET)` 是存储实时榜单的绝佳选择。
        *   **Key**: `rank:list:{category}:{timestamp}` (e.g., `rank:list:drama:202310271500`)
        *   **Member**: `content_id`
        *   **Score**: 热度分
    *   **优势**: `ZADD` 和 `ZREVRANGE` 命令可实现高效的分数更新和 TopN 查询，复杂度为 O(log(N)+M)。
    *   **持久化**: 采用 AOF + RDB 混合模式，兼顾数据安全和恢复速度。

*   **冷数据存储: ClickHouse**
    *   **职责**: Flink 将计算出的全量榜单数据或快照写入 ClickHouse，用于历史榜单归档查询和数据分析。
    *   **优势**: 作为一款高性能的 OLAP 数据库，非常适合海量数据的分析查询场景。

### 3.3. 高可用与高并发设计

应对千万级 QPS 的核心策略是 **"分层缓存"** 和 **"水平扩展"**。

*   **多级缓存**:
    1.  **CDN 缓存**: 在 CDN 边缘节点缓存榜单结果 30-60 秒，抵挡绝大部分用户请求。
    2.  **接入层缓存**: 在 Nginx/API Gateway 层设置秒级本地缓存（如 `lua_shared_dict`）。
    3.  **服务本地缓存**: 在 API 服务内部使用 `freecache` 等库再缓存数秒，降低对 Redis 的请求穿透。

*   **架构高可用**:
    *   **服务无状态化与弹性伸缩**: API 服务集群无状态，部署于 K8s，配置 HPA (Horizontal Pod Autoscaler) 自动扩缩容。
    *   **Redis 集群**: 使用 Redis Cluster 模式进行水平扩展，并为每个 master 配置 slave 实现读写分离和故障转移。

*   **流量防护**:
    *   **限流与熔断**: API Gateway 层需配置精细化限流策略。对 Redis 等下游服务的调用必须有熔断机制（如 gobreaker），在下游故障时快速失败或返回兜底数据（如一小时前的榜单），防止雪崩。
    *   **资源预留**: Flink 和 Redis 等核心组件需预留一定资源冗余，应对突发流量。

### 3.4. 反作弊策略

反作弊是一个动态对抗的系统工程，需要实时与离线相结合。

*   **实时过滤 (Flink)**:
    *   **规则引擎**: 基于用户行为频率进行过滤。例如，限制单 IP/设备对同一视频的单位时间点赞次数。
    *   **黑名单**: 实时过滤来自黑名单（IP、设备ID、用户ID）的请求。

*   **离线分析 (ClickHouse/Spark)**:
    *   **行为模式分析**: 通过离线分析，识别出"机器"行为模式（如集中在凌晨、操作间隔高度一致等）。
    *   **关联图分析**: 将用户、设备、IP 建成图。通过图算法（如 Louvain）发现刷榜的"水军"团伙。

*   **反馈闭环**:
    *   离线分析挖掘出的作弊实体会**动态更新**到在线的黑名单库中。
    *   对于已产生的作弊分数，需要执行**分数冲正**，从榜单中扣除作弊贡献值。