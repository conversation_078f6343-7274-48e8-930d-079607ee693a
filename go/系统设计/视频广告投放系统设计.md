系统设计题："腾讯视频信息流广告系统"系统
好的，这是一个集推荐、广告、高并发于一体的复杂系统，非常考验综合设计能力。我的设计思路如下：
a. 整体架构
广告系统与内容推荐系统需要紧密结合，但为了业务和技术上的解耦，架构上会清晰分离。
graph TD
    SUBGRAPH (Client)
        client[app/WEB 客户端] --> |ad request| ad_gateway[ad gateway]
    END

    subgraph ad_server (AdService Cluster)
        ad_gateway --> ad_decision{Ad Decision Engine}
        ad_decision --> ad_retrieval{Ad Retrieval}
        ad_decision --> user_profile{User Profile}
        ad_decision --> ranking{Ranking / Bidding}

        ad_retrieval --> |get ad candidates| ad_index{Es/Lucene}
        user_profile --> |get user tags| redis_cluster[Redis Cluster]
        ranking --> |get budget, freq| redis_cluster

        ad_decision --> |final ad response| ad_gateway
        ad_gateway --> |ad creative| client
    end

    subgraph data_pipeline (Data Pipeline)
        client --> |impression, click report| ad_tracking{Ad Tracking Gateway}
        ad_tracking --> kafka{Kafka}
        kafka --> flink{Real-time Computing (Flink)}
        flink --> |batch aggregation| clickhouse{ClickHouse}
        flink --> |real-time ul profiling, counting| redis_cluster
        mysql_ad_db[MySQL ( Ad Metadata)] -.canal.-> |ad metadata update| kafka
        kafka -.-> |update ad index| ad_index
    end
1.  **广告请求 (Ad Request)**: 当客户端（如App）的推荐Feed流需要插入广告时，它会向广告系统的网关（Ad Gateway）发起请求。请求中会携带关键信息：用户信息（`user_id`）、设备信息（`device_id`）、网络环境、地理位置、以及广告位信息（`placement_id`，如首页第五个卡位）。
2.  **广告决策 (Ad Decision)**: 这是广告系统的大脑。
    *   **广告决策引擎 (Ad Decision Engine)** 是总指挥，它会依次调用下游服务。
    *   **用户画像服务 (User Profile Service)**: 根据`user_id`获取用户的标签，如年龄、性别、兴趣偏好（体育迷、美剧控）等。
    *   **广告召回服务 (Ad Retrieval Service)**: 根据用户标签、上下文信息（如正在浏览体育频道），从海量的广告库中，通过倒排索引快速召回一批（几百到上千个）符合定向要求的候选广告。
    *   **过滤与排序 (Filtering & Ranking)**: 对召回的广告进行精排。首先过滤掉不满足条件的广告（如预算已用尽、投放已结束、不符合内容安全策略等）。然后，通过一个复杂的模型预估每个广告的eCPM (effective Cost Per Mille，有效千次展示成本)，`eCPM = pCTR (预估点击率) * pCVR (预估转化率) * Bid (出价)`。最终选出eCPM最高的广告。
3.  **广告投放 (Ad Serving)**: 决策引擎将最终胜出的广告素材（图片/视频URL）、落地页链接以及用于追踪的曝光/点击上报URL返回给客户端。客户端SDK负责渲染广告并绑定上报事件。
4.  **数据与追踪 (Data & Tracking)**:
    *   用户的每一次广告曝光（Impression）和点击（Click）都会通过追踪网关（Tracking Gateway）上报到Kafka消息队列。
    *   Flink或Spark Streaming等实时计算引擎消费Kafka中的数据，进行实时的反作弊过滤、更新广告的实时消耗和用户频次控制（存在Redis中），并将聚合后的数据写入OLAP数据库（如ClickHouse）用于后续分析。
    *   广告主信息、广告计划、出价等元数据存储在MySQL中，通过Canal等工具监听binlog，实时同步到广告索引和决策系统。

b. 核心模块详解
1.  **广告决策引擎 (Ad Decision Engine)**
    *   **技术选型**：使用Go语言开发，追求极致的性能和低延迟。
    *   **关键流程**:
        *   **召回 (Retrieval)**: 这是性能瓶颈之一。我们会使用Elasticsearch或自建的Lucene索引。索引的key是各种定向标签（如"男_20-25岁_北京_篮球迷"），value是广告ID列表。通过将用户标签组合成查询条件，可以快速圈定候选广告。
        *   **排序 (Ranking)**: 排序模型至关重要。早期可以采用简单的逻辑回归（LR）模型，但为了提升效果，会逐步演进到GBDT+LR或深度学习模型（如DeepFM），这些模型能更好地捕捉特征之间的交叉关系。模型训练是离线完成的，线上服务只做推理（inference），要求在毫秒级内完成。
        *   **竞价 (Bidding)**: 对于效果广告，会采用GSP (Generalized Second-Price) 拍卖机制，即胜出者付第二名的出价+0.01元，这能鼓励广告主写真实出价。

2.  **广告追踪 (Ad Tracking)**
    *   **可靠性**: 追踪数据的丢失意味着收入的损失。因此，追踪网关必须是高可用的集群。收到上报请求后，会立即返回一个`200 OK`的HTTP响应（或一个1x1的GIF），然后将日志异步写入Kafka。这个过程要非常快。
    *   **实时性**: 广告的预算消耗、用户的频次控制（如一个用户一天最多看某个广告3次）都要求非常高的实时性。Flink集群会准实时地（秒级延迟）计算这些指标，并更新到Redis中，供决策引擎在下次请求时使用。

c. 存储选型
*   **广告元数据 (Ad Metadata)**: **MySQL集群**。存储广告计划、单元、创意、广告主资质、出价等。这部分数据一致性要求高，但访问量相对不大，MySQL是成熟的选择。
*   **广告索引 (Ad Index)**: **Elasticsearch集群**。用于构建广告的倒排索引，满足多维度、高性能的召回需求。
*   **用户画像/实时计数器 (User Profile/Counters)**: **Redis集群**或**Aerospike**。存储用户标签、广告实时预算、用户对特定广告的频次计数。这些数据要求极低的访问延迟（亚毫秒级），Redis的内存存储和原子操作（如`INCR`）非常适合。
*   **追踪日志管道 (Tracking Log Pipeline)**: **Kafka集群**。作为数据总线，承载海量的曝光、点击日志，削峰填谷，解耦后端处理系统。
*   **分析与报表 (Analytics & Reporting)**: **ClickHouse**。用于存储和分析海量的广告日志数据。其出色的OLAP性能可以支持广告主报表、内部运营分析等复杂查询。

d. 高可用与高并发
广告请求的QPS可以达到千万级，且对延迟极其敏感。
*   **核心原则：广告系统故障绝不能影响主站核心体验**。
    *   **严格的超时控制**: 整个广告决策流程必须在极短的时间内完成（例如80-100ms）。如果超时，决策引擎必须立即向上游返回一个"无广告"的空响应。前端SDK收到空响应后，会直接展示下一条内容，用户无感知。
    *   **服务降级与熔断**: 对所有下游服务的调用（Redis, ES, MySQL等）都必须有Hystrix或Go-breaker这样的熔断器包裹。当某个下游服务出现故障，可以快速失败，返回一个默认值或直接进入降级逻辑（比如放弃部分非核心的定向逻辑，返回一个通投广告），而不是无限期等待。
*   **应对海量QPS**:
    *   **无状态服务**: 决策引擎自身是无状态的，所有状态都存储在外部（如Redis）。这使得服务可以部署在K8s上，通过HPA进行弹性、快速的水平扩缩容。
    *   **多级缓存**:
        *   **本地缓存**: 在决策引擎实例内部，用`FreeCache`或`BigCache`缓存广告计划的元数据、模型参数等几乎不变的数据，避免频繁请求MySQL。
        *   **CDN**: 广告的图片、视频素材必须全部上CDN，客户端直接从CDN获取。
    *   **网络优化**: 使用GeoDNS，将用户的广告请求导向最近的IDC，减少网络延迟。服务间通信采用gRPC和Protobuf，性能优于HTTP/JSON。

e. 精准投放与反作弊
*   **精准投放**:
    *   **用户画像 (User Profile)**: 通过分析用户的长期观影历史、搜索行为、互动行为，构建用户的兴趣图谱。
    *   **上下文定向 (Contextual Targeting)**: 分析用户当前正在观看的内容的标签、分类，投放与内容相关的广告。
    *   **重定向 (Retargeting)**: 对访问过广告主网站或App的用户，再次向其展示相关广告，转化率更高。
*   **反作弊 (Anti-Fraud)**: 保护广告主的利益，是广告平台的生命线。
    *   **实时过滤**: 在追踪数据进入Kafka之前或在Flink中进行。基于规则过滤，例如：检查User-Agent是否合法，判断单个IP/设备在短时间内的点击次数是否异常。
    *   **离线分析**: 通过机器学习模型对历史数据进行分析，识别异常模式。例如：分析点击发生的时间分布（正常用户点击分布在全天，作弊流量可能集中在深夜），点击和曝光的地理位置是否一致，构建用户-设备-IP的关系图，发现作弊团伙。
    *   **反馈闭环**: 将离线分析识别出的作弊IP、设备ID等加入实时过滤的黑名单中。对于已确认的作弊流量，要从广告主的账单中扣除，并将预算返还。 