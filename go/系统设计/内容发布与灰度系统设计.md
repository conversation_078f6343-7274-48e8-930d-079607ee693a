## 系统设计题：内容发布与灰度系统

### 1. 需求背景与核心挑战

#### 1.1. 需求背景
为腾讯视频这样拥有千万级DAU的平台设计一个内容发布与灰度系统。该系统需要支持电影、电视剧、综艺等内容的上线，以及相关新功能（如互动特性）的发布。

#### 1.2. 核心挑战
*   **高可用性**: 在巨大的用户流量下，发布过程不能影响核心服务的稳定性。
*   **灵活性**: 支持多维度的灰度策略（用户比例、画像、地理位置等）。
*   **可靠性**: 具备精准的监控和快速的回滚能力，有效控制发布风险。
*   **功能与内容分离**: 不仅要支持UI、算法等功能的灰度，也要支持视频编码、码率等内容本身的灰度。

### 2. 整体架构

系统采用"控制面"与"数据面"分离的设计，以实现发布管理与策略执行的解耦。

```mermaid
graph TD
    subgraph operator["操作员/平台"]
        SystemUI1["发布管理后台UI"]
    end

    subgraph kontrol-plane["控制平面 (Control Plane)"]
        API[控制API网关]
        Engine{"发布流程引擎 (MySQL存储状态)"}
        API --> Engine
    end

    subgraph config-center["配置中心 (Config Center)"]
        Config[(Apollo/Nacos)]
    end

    subgraph monitoring["监控与告警 (Monitoring)"]
        Monitor(Prometheus+Grafana)
        Alert(AlertManager)
    end

    subgraph data-plane["数据平面 (Data Plane)"]
        Gateway["API网关/BFF"]
        ClientApp["客户端App"]
    end

    SystemUI1 -->|"1. 创建发布单"| API
    Engine -- >|"2. 将规则写入"| Config
    Gateway -..->|"3. 实时拉取规则"| Config
    ClientApp -->|"4. 携带用户信息请求"| Gateway
    Gateway -->|"5. 决策并返回灰度/全量内容"| ClientApp
    
    Gateway -.->|业务数据上报| Monitor
    Alert -- >|"6. 异常告警/触发自动回滚"| Engine
    Monitor --> Alert
    Engine -..->|"7. 执行回滚"| Config
```

*   **发布管理平台**: 面向运营和开发人员的Web界面，用于配置和管理发布流程。
*   **控制平面**: 核心是**发布引擎**，它是一个状态机（状态通过**MySQL**持久化），负责执行发布单的生命周期（如：待发布 -> 灰度 -> 全量 -> 回滚）。它将最终的灰度规则推送到配置中心。
*   **配置中心 (Apollo/Nacos)**: 灰度规则的唯一可信源（Single Source of Truth），为数据面提供近实时的规则查询。
*   **数据平面**: 包括API网关、BFF或客户端本身。它们是灰度策略的执行者，负责根据规则对用户请求进行决策。
*   **监控与告警**: 采集并对比灰度组与对照组的核心指标，是发布成功的"眼睛"。

### 3. 核心设计：灰度策略与执行

#### 3.1. 灵活的灰度规则引擎
为了做到精细化流量切分，规则引擎需支持多维度组合：
*   **用户百分比**: `hash(user_id) % 100 < 1`，保证同一用户始终在同一分桶。
*   **用户画像标签**: `tags.contains("vip_level > 5")` 或 `tags.contains("region:beijing")`。
*   **设备/版本信息**: `os == "Android" && app_version > "8.5.0"`。
*   **白名单**: `user_id in (internal_staff_list)`，用于内部测试。
*   **分层抽样与正交实验**: 设计流量分层模型，允许不同实验在各自独立的流量层进行，避免交叉影响。

#### 3.2. 规则的存储与下发
灰度规则以JSON格式存储在配置中心（如Apollo）中，Key为发布项，Value为规则详情。
*   `Key`: `release.content.c00123abcde`
*   `Value` (JSON):
    ```json
    {
      "enabled": true,
      "rule": "hash(user_id) % 100 < 10 && tags.contains('region:shanghai')",
      "payload": { "feature_flag": "new_comment_style" }
    }
    ```

#### 3.3. 灰度执行点
*   **服务端执行 (推荐)**: 决策逻辑在API网关或BFF层。优点是规则更新快，逻辑收敛，对客户端透明。
*   **客户端执行**: 客户端App直接订阅规则进行决策，适用于纯UI表现类的灰度。

### 4. 核心设计：内容的灰度发布
针对视频编码、码率等内容本身的灰度，需要媒资、CDN和播放逻辑的联动。
1.  **媒资版本化**: 在媒资管理系统(MAM)中，对同一内容ID，标识出多个版本，如`production`（正式版）和`grayscale_v1`（H.266编码灰度版）。
2.  **动态清单生成 (核心)**: 当灰度用户请求播放时，系统不下发最终的视频CDN地址，而是返回一个指向**动态清单服务**的URL。该服务根据用户身份，实时生成指向不同版本视频切片（`.ts`文件）的播放清单（`.m3u8`），从而实现对播放器透明的内容切换。
3.  **流程示意图**:
    ```mermaid
    sequenceDiagram
        participant ClientApp as 客户端
        participant Gateway as API网关/BFF
        participant ManifestService as 动态清单服务
        participant CDN as 内容分发网络

        ClientApp->>+Gateway: 请求播放视频
        Gateway->>Gateway: 决策用户命中灰度
        Gateway-->>-ClientApp: 返回带签名的"动态清单URL"

        ClientApp->>+ManifestService: 携带签名请求该URL
        ManifestService->>ManifestService: 验证身份，生成指向新版切片的清单
        ManifestService-->>-ClientApp: 返回m3u8清单文件

        loop 播放过程
            ClientApp->>+CDN: 根据清单，请求新版视频切片
            CDN-->>-ClientApp: 返回切片数据
        end
    ```

### 5. 保障措施：高可用与监控

#### 5.1. 高可用设计
*   **引擎无状态化**: 发布引擎自身无状态，可水平扩展；状态存储在MySQL中，通过乐观锁保证并发操作的一致性。
*   **发布审批流**: 核心内容的发布需经开发、测试等多级审批。
*   **紧急刹车 (Kill Switch)**: 在配置中心设置全局开关，可在极端情况下禁用所有灰度逻辑，一键恢复全量稳定版本。

#### 5.2. 监控与度量
*   **数据采集**: 客户端与服务端日志必须包含用户所属的灰度组标签，以便分割对比。
*   **指标对比**: 在监控平台（Grafana）上，对灰度组和对照组的核心指标进行同屏对比，是衡量发布效果的关键。
    *   **业务核心指标**: 播放成功率、卡顿率、用户平均观看时长。
    *   **系统健康指标**: API错误率、接口延迟。
*   **A/B实验思想**: 灰度发布即为一次A/B实验。除关注"播放失败率"等负向指标外，也要关注"人均视角切换次数"等正向业务指标。

#### 5.3. 快速回滚机制
*   **一键回滚**: 在管理后台提供"回滚"按钮，操作后发布引擎会立即更新配置中心规则，秒级生效。
*   **自动熔断/回滚**: 监控系统与发布引擎联动。当检测到灰度组核心指标（如播放失败率）相比对照组有恶化趋势（如上涨20%）时，可自动调用回滚API，实现无人干预的风险控制。 