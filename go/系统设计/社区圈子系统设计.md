# 系统设计题："腾讯视频社区(圈子)系统"设计

请你设计一个支撑亿级用户的社区系统。用户可以在社区里加入不同的"圈子"（比如某个明星的粉丝圈、某部剧的剧迷圈），在圈子里发帖、评论、点赞，关注其他用户。你需要考虑Feed流设计、数据存储、高并发挑战和内容安全。

### a. 整体架构

这是一个高度服务化的架构，将不同的职责拆分到独立的微服务中，通过API网关和消息队列进行协作。

1.  **客户端 (Client)**：iOS, Android, Web端，负责UI展示和用户交互。
2.  **接入层 (API Gateway)**：使用Tencent APIGW 或自研网关。负责请求路由、用户认证、流量控制、熔断降级、协议转换等。是所有后端服务的统一入口。
3.  **核心服务层 (Core Services)**：
    *   **用户服务 (User Service)**：管理用户基本信息、圈子列表、关注列表等。
    *   **关系服务 (Relation Service)**：核心服务，管理用户与用户（关注）、用户与圈子（加入）的关系链。
    *   **圈子服务 (Group Service)**：管理圈子的元信息（名称、简介、封面）、成员列表、权限等。
    *   **发布服务 (Post Service)**：处理帖子的发布、编辑、删除，并将帖子内容写入存储。这是写的入口。
    *   **Feed流服务 (Feed Service)**：核心服务，为用户生成个性化的动态Feed流。这是读的入口。
    *   **互动服务 (Interaction Service)**：处理点赞、评论等操作，并更新相应的计数。
4.  **基础平台层 (Platform Layer)**：
    *   **消息队列 (Message Queue)**：使用Kafka。作为服务间异步解耦和数据同步的核心。例如，用户发帖后，发布服务将消息推送到Kafka，由下游的Feed流扇出（Fan-out）服务、搜索索引服务、内容审核服务进行消费。
    *   **缓存 (Caching)**：使用Redis Cluster。大规模缓存用户信息、关系链、热点帖子、Feed流等，是应对高并发读的关键。
    *   **数据存储 (Storage)**：
        *   **关系型数据库**: 使用Tencent TDSQL (或分库分表的MySQL集群)。存储用户、帖子、评论、圈子等核心业务数据。
        *   **搜索引擎**: Elasticsearch。用于帖子、用户、圈子的全文搜索功能。
        *   **对象存储**: COS。用于存储用户上传的图片、短视频等多媒体内容。
5.  **离线与数据处理层 (Offline & Data Processing)**：
    *   **内容安全 (Content Security)**：独立的AI审核平台和人工审核后台，对社区内容进行多维度审核。
    *   **数据分析 (Data Analytics)**：使用Flink/Spark对用户行为日志进行分析，用于推荐排序优化、用户画像构建等。

### b. 核心功能设计 - Feed流

Feed流是社区系统的核心，有"推（Push）"和"拉（Pull）"两种主要模式。

1.  **模式选择：推拉结合 (Hybrid Model)**
    *   **推模式 (Fan-out on Write)**：当一个用户发帖时，系统主动将这个帖子的ID推送给他所有粉丝的Feed流收件箱（Timeline）。优点是读取时速度极快，因为用户的Feed流是预计算好的。缺点是对于大V（拥有千万粉丝），一次发帖会造成巨大的写扩散（Fan-out Storm）。
    *   **拉模式 (Fan-out on Read)**：用户请求Feed流时，系统实时去拉取他所关注的所有用户的帖子，聚合排序后返回。优点是发帖时很简单。缺点是读取时延迟高，计算压力大。
    *   **我们的选择：推拉结合**
        *   对普通用户（粉丝数 < 10000），采用**推模式**。发帖后，通过异步任务将帖子ID写入粉丝的Timeline Redis ZSET中。
        *   对大V（粉丝数 >= 10000），采用**拉模式**。他们的帖子不主动推送。
        *   当一个用户请求Feed流时，Feed服务会执行一个**聚合操作**：
            1.  从Redis中取出自己的Timeline ZSET（这包含了所有普通关注者的帖子）。
            2.  获取自己关注的大V列表。
            3.  实时从这些大V的"发件箱"（Outbox）中拉取最近的帖子。
            4.  将两部分帖子（推模式的+拉模式的）在内存中合并、排序，然后返回。同时可以将合并后的结果在Redis中缓存几秒钟，应对该用户的连续刷新。

2.  **数据结构 (Redis)**
    *   **用户Timeline（收件箱）**: `Sorted Set` -> `timeline:{user_id}`
        *   `Member`: `post_id`
        *   `Score`: `timestamp` (用于排序)
    *   **用户Outbox（发件箱）**: `Sorted Set` -> `outbox:{user_id}`
        *   存储该用户发布的所有帖子的ID，用于拉模式。
    *   **帖子内容缓存**: `Hash` -> `post_content:{post_id}`
        *   缓存帖子的具体内容、作者信息、点赞数、评论数等，减少对DB的直接查询。

### c. 存储选型与数据一致性

1.  **数据库设计**
    *   **分库分表**：用户、帖子、评论、关系链等核心数据表，必须进行水平拆分。例如，按`user_id`或`post_id`的hash进行分库分表，将压力分散到多个物理DB实例上。
    *   **关系链存储**：`follow`表（`user_id`, `follower_id`）、`membership`表（`user_id`, `group_id`）。可以考虑使用如图数据库（如Neo4j）来处理复杂的关系查询（如"我关注的人也关注了谁"），但在超大规模下，使用分库分表的关系型数据库依然是更成熟、稳定的方案。
    *   **计数器存储**：点赞数、评论数、转发数等频繁更新的字段，如果直接`UPDATE`数据库，并发性能会很差。我们会将这些计数器**单独存储在Redis中** (`INCR`命令是原子的，性能极高)，然后通过定时任务或在读取时，异步、批量地写回MySQL，保证最终一致性。

2.  **数据一致性**
    *   大部分社交场景对一致性要求不高，**最终一致性**是最佳选择。
    *   **使用Kafka保证最终一致性**：
        *   用户发帖后，发布服务在写入主DB后，立即向Kafka发送一条消息`{post_id: 123, author_id: 456, ...}`。
        *   **Feed扇出服务**订阅此Topic，获取消息后执行推模式的扇出逻辑。
        *   **搜索服务**订阅此Topic，获取消息后更新Elasticsearch索引。
        *   **内容审核服务**订阅此Topic，进行异步审核。
        *   这个模型保证了核心数据先落盘，下游的扩展功能通过消息队列解耦，即使某个下游服务失败，也可以通过重试消费来保证数据最终同步成功。

### d. 高并发与性能优化

1.  **读操作优化 (QPS可达千万级)**
    *   **多级缓存**：
        *   **CDN缓存**：圈子介绍、帖子中的图片/视频等静态资源。
        *   **接入层缓存 (Nginx/Gateway)**：对于变化不频繁的API（如圈子信息），可在接入层做短时间缓存。
        *   **分布式缓存 (Redis)**：核心。缓存了95%以上的读请求，包括用户Timeline、热点帖子、用户信息、关系链等。
        *   **本地缓存 (In-Process Cache)**：在Feed服务内部，用`FreeCache`/`BigCache`缓存一些超高热度的元数据，避免跨网络请求Redis。
    *   **读写分离**：对于DB层，配置主从复制，大量的读请求可以路由到从库。

2.  **写操作优化**
    *   **异步化**：核心原则。发帖、评论、点赞等操作，对用户来说，只要请求成功写入Kafka，就可以立即返回成功。后续的所有复杂操作（写缓存、写ES、写粉丝Timeline）全部异步化，极大提升了写入接口的吞吐量和响应速度。
    *   **写请求合并**：对于点赞等操作，可以在缓存层先聚合，比如每隔1秒或每100次点赞，再批量更新一次DB，而不是每次都写库。

3.  **连接管理**
    *   对于需要实时通知的场景（如有人回复了你的评论），可以为每个在线用户维持一个轻量级的**WebSocket长连接**。通过一个独立的、高可用的长连接网关集群来管理这数亿的连接，后端服务通过向这个网关推送消息，来实现对特定用户的实时通知。

### e. 内容安全与审核

这是一个与业务逻辑并行，但又至关重要的系统。

1.  **多层过滤系统**：
    *   **客户端预处理**：在用户发布前，在本地对文本进行一次简单的敏感词检测。
    *   **实时机审 (AI a审核)**：帖子发布后，内容进入Kafka，AI审核服务会消费消息，利用NLP和图像识别模型对文本、图片进行涉政、涉黄、广告等风险识别。根据置信度，做出**直接拦截**、**放行**或**送入人工审核**的决策。
    *   **人工审核平台**：一个高效的后台系统，供审核团队对机器无法确定的内容进行最终裁定。平台需要支持快速打标、批量操作、反馈模型等功能。
2.  **用户举报与反馈闭环**：
    *   提供便捷的用户举报入口。
    *   建立用户信誉体系。被多次举报并查实违规的用户，其后续发布的内容会被更严格地审核，甚至需要"先审后发"。反之，信誉良好的用户内容可以被更快地放行。
3.  **溯源与打击**：
    *   记录所有发布内容的用户ID、设备ID、IP地址等信息。
    *   通过分析，找出恶意发布内容的团伙（例如，同一IP段、同一批设备在短时间内发布大量相似违规内容），进行批量封禁和处理。

通过以上设计，我们可以构建一个功能完备、高性能、高可用且安全的社区系统，能够良好地应对亿级用户的挑战。 