### 系统设计题："互动视频"系统设计

**面试官**：你好，我们今天来聊一个比较有趣的系统设计题。近年来，像Netflix的《黑镜：潘达斯奈基》这样的互动剧集获得了很多关注，观众可以通过选择来影响剧情走向。现在，假设腾讯视频也想构建这样一个强大的**互动视频平台**，允许创作者制作和发布互动式内容，并为上亿用户提供流畅、沉浸的观看体验。请你来设计这个系统。


#### **a. 整体架构 (Overall Architecture)**

首先，我们需要将这个复杂的系统解耦为几个协同工作的核心部分：**创作与注入平台**、**内容分发网络 (CDN)**、**智能播放器** 和 **后端互动服务**。

```mermaid
graph LR
    subgraph 创作者 (Creator)
        A[创作工具]
    end
    
    subgraph 注入与处理 (Ingestion & Processing)
        B[内容管理系统<br/>CMS]
        C[转码集群]
        D[媒资数据库]
        E[对象存储 S3]
        
        A -- "剧本(JSON),<br/>视频文件" --> B
        B -- "触发转码" --> C
        B -- "写入剧本元数据" --> D
        C -- "写入切片" --> E
        C -- "更新切片地址" --> D
    end

    subgraph 分发 (Delivery)
        F[CDN]
        D -- "同步剧本" --> F
        E -- "同步切片" --> F
    end

    subgraph 用户 (User)
        G[智能播放器<br/>Client]
    end
    
    subgraph 后端服务 (Backend)
        H[互动核心服务]
        I[用户状态DB]
        J[数据上报网关]
        H -- R/W --> I
    end

    subgraph 数据分析 (Analytics)
        K[Kafka]
        L[Flink/ClickHouse]
        M[数据洞察看板<br/>Dashboard]
    end

    %% -- Connections --
    G -- "1. 拉取剧本/切片" --> F
    G -- "3. 保存/获取选择" --> H
    G -- "2. 上报事件" --> J
    J -- "写入" --> K
    K -- "消费" --> L
    L -- "分析结果" --> M
    M -- "反馈" --> A
```

1.  **创作与注入 (Authoring & Ingestion):**
    *   **创作工具:** 这不仅是一个简单的可视化Web工具，而是一个集成了强大后端与AI能力的综合性创作平台。前端提供拖拽式的界面，让创作者像画流程图一样设计剧情"节点"（视频片段）和"边"（用户选择）。其后端服务负责项目管理、版本控制，并与CMS无缝对接。更进一步，平台集成AI能力，可用于分析剧本结构，甚至辅助生成对话或剧情摘要，极大地提升创作效率和质量。最终，该工具会导出一个描述整个剧情逻辑的`剧本图`文件（例如JSON格式），作为驱动后续流程的核心。
    *   **CMS与转码:** 创作者将`剧本图`和所有视频源文件上传到内容管理系统(CMS)。后台系统会将每一个剧情节点视为一个独立的短视频，送入转码集群进行切片（HLS/DASH），并存储到媒资库和对象存储中。
    *   **元数据管理:** `剧本图`本身（包含每个剧情节点对应的视频切片地址）作为核心元数据，与视频切片一同被推送到CDN。

2.  **分发与播放 (Delivery & Playback):**
    *   **CDN:** 承载了所有的视频切片和`剧本图`JSON文件，为全球用户提供低延迟的访问。
    *   **智能播放器:** 这是整个体验的核心。它不再是一个简单的视频播放器，而是一个"互动引擎"。它需要加载并理解`剧本图`，根据用户的选择无缝地切换播放路径，并管理后台预加载。

3.  **后端服务 (Backend Services):**
    *   **互动核心服务:** 提供API，让播放器可以保存用户的每一次选择，并能在用户下次观看时恢复进度。这是实现"跨端续播"的关键。
    *   **用户状态数据库:** 使用高可用、低延迟的NoSQL数据库（如Redis Cluster或DynamoDB）来存储用户的观看路径。`Key`可以是`(user_id, content_id)`，`Value`可以是一个记录了用户已选节点ID的列表。
    *   **数据上报与分析:** 播放器会上报丰富的行为事件（如进入节点、做出选择、观看时长等），通过数据网关进入Kafka，供大数据平台进行实时监控和离线分析，从而为内容创作者提供数据洞察。

#### **b. 核心挑战与设计关键：无缝播放体验**

这个系统成败的关键在于，当用户做出选择后，能否像看普通视频一样无缝地过渡到下一段剧情，任何卡顿都会严重破坏沉浸感。

**技术方案：智能预加载 (Intelligent Pre-loading)**

1.  **剧本图感知:** 播放器启动时，会完整加载`剧本图`JSON。因此，在播放任何一个节点（如`Node A`）时，它都明确知道接下来所有可能的节点（如`Node B`和`Node C`）。
2.  **并行缓冲:** 当播放器正在播放`Node A`的视频时，其底层的下载模块会**同时启动**对`Node B`和`Node C`初始视频片段的下载，并将它们放入独立的缓冲区中。
3.  **决策点管理:** `剧本图`中为每个节点都定义了一个`decision_point_timestamp`（决策点时间戳）。
    *   当播放进度到达这个时间点时，播放器会在视频上层渲染出选项UI。
    *   **用户做出选择 (如选B):** 播放器立即丢弃`Node C`的缓冲区，然后将播放流无缝切换到已经准备好的`Node B`的缓冲区，实现零延迟过渡。
    *   **用户超时未选:** 系统会根据`剧本图`中定义的默认选项，自动切换到对应的预加载路径。
4.  **网络自适应:** 预加载策略必须与ABR（自适应码率）逻辑深度结合。在网络状况良好时，可以为所有分支路径预加载更高码率、更多时长的片段；在网络不佳时，则可能只预加载较低码率的片段，甚至只预加载默认路径的片段，以保证主路径的流畅播放。

#### **c. 剧本定义与状态管理**

1.  **剧本数据结构 (Script Data Structure):** 一个描述有向无环图 (DAG) 的JSON结构是理想选择。

    ```json
    {
      "interactive_video_id": "iv001_bandersnatch_clone",
      "start_node_id": "node_01_intro",
      "nodes": {
        "node_01_intro": {
          "segment_url_hls": "https://.../intro.m3u8",
          "duration": 60.5,
          "decision_point_timestamp": 55.0,
          "choices": [
            { "text": "接受工作", "next_node_id": "node_02a_accept" },
            { "text": "拒绝工作", "next_node_id": "node_02b_reject" }
          ],
          "default_choice_index": 0,
          "timeout_seconds": 10.0
        },
        "node_02a_accept": {
          "segment_url_hls": "https://.../accept.m3u8",
          "duration": 120.0,
          // ... 更多选择或结局
        },
        "ending_01_success": {
          "segment_url_hls": "https://.../ending01.m3u8",
          "duration": 45.0,
          "is_end_node": true
        }
      }
    }
    ```

2.  **状态管理API:**
    *   `POST /v1/interaction/state`: 保存用户选择。请求体可以是 `{"user_id": "xxx", "content_id": "iv001", "chosen_node_id": "node_02a_accept"}`。后端服务会将这个选择追加到用户的观看路径中。
    *   `GET /v1/interaction/state?user_id=xxx&content_id=iv001`: 获取用户在该互动视频的最新状态（即观看路径），以便播放器从上次的节点恢复播放。

#### **d. 高可用与扩展性**

*   **无状态服务:** 互动核心服务、数据网关等都应设计为无状态的，部署在K8s上，配置HPA，根据负载自动扩缩容。
*   **数据库扩展:** 用户状态数据库是唯一的有状态核心。使用Redis Cluster或DynamoDB这类本身就具备高可用和水平扩展能力的数据库，可以有效分摊海量用户的读写压力。
*   **CDN容灾:** `剧本图`和视频切片都可以配置多CDN厂商作为备份，当主CDN出现故障或在某地区访问质量不佳时，可以通过智能调度系统（如DDoS-Guard）切换到备用CDN。

#### **e. 数据驱动与未来展望**

这个系统的价值远不止于"看个热闹"。

*   **创作者洞察:** 我们可以分析所有用户的选择路径数据，生成"剧情热力图"，告诉创作者哪些剧情线最受欢迎，哪些选择点最能引发用户纠结。这为后续的创作提供了宝贵的数据支持。
*   **个性化互动:** 更进一步，系统可以根据用户的画像（例如，从用户画像系统得知该用户偏爱"冒险"类内容），在用户无感知的情况下，动态调整`剧本图`的默认路径，为他推荐更可能喜欢的剧情走向。
*   **实时互动直播:** 将这套逻辑应用在直播中，主播可以发起实时投票，观众的选择可以真实地影响直播的走向（如决定下一场玩什么游戏，或选择下一个互动环节），这将带来革命性的互动体验。

总而言之，这个系统的设计体现了从内容生产、技术实现到数据闭环的完整思考，是一个非常有价值和挑战性的方向。 