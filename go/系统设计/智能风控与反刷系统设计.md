### 系统设计题："腾讯视频智能风控与反刷系统"

#### a. 整体架构 (Overall Architecture)

风控系统是一个典型的"数据驱动"的系统，其架构可以分为数据层、计算层、服务层和策略运营层。

1.  **数据源与采集层 (Data Sources & Ingestion):**
    *   **实时行为数据**: 用户在端上的一切行为日志（登录、注册、播放、评论、点赞、参与活动、修改资料等），通过统一的日志网关上报至 **Kafka**。数据必须携带丰富的上下文，如 `user_id`, `device_id`, `ip`, `app_version`, `trace_id`, `action_type`, `content_id` 等。
    *   **业务数据**: 核心业务数据库的变更数据（如订单、优惠券核销），通过 Canal 订阅 Binlog 后也推送到 Kafka。
    *   **三方情报数据**: 购买或合作的外部风险情报，如代理IP库、黑产手机号库、设备风险画像等，定期同步到内部存储中。

2.  **计算与分析层 (Computation & Analysis):**
    *   **实时计算 (Stream Processing)**:
        *   **核心引擎**: **Apache Flink**。它是实时风控的基石。
        *   **任务**: 消费 Kafka 中的各类数据流，进行实时特征计算。例如："该设备最近1分钟的登录失败次数"、"该IP在过去5分钟内关联了多少个新账号"、"该视频的评论热度是否异常飙升"。
        *   **产出**: 将计算出的实时特征低延迟地写入 **特征存储**。
    *   **离线计算 (Batch Processing)**:
        *   **核心引擎**: **Apache Spark** / **Hive**。
        *   **任务**:
            1.  **复杂特征挖掘**: 在 T+1 的数据上进行批量计算，挖掘更深层次、更长周期的特征。例如："某用户近30天的活跃度方差"、"用户社交关系图的社区紧密度"。
            2.  **算法模型训练**: 使用历史的正负样本（已知作弊和正常行为），训练机器学习模型（如 GBDT, GNN, RandomForest）。
            3.  **团伙挖掘**: 通过 Spark GraphX 或图数据库进行关联图分析，识别出"薅羊毛"、"刷量"团伙。
        *   **产出**: 周期性地将离线特征、更新后的模型、以及挖掘出的黑名单（用户、设备、IP）推送给线上服务。

3.  **存储层 (Storage Layer):**
    *   **特征库 (Feature Store)**:
        *   **实时特征**: **TiKV / HBase**。需要支持高并发的 KV 读写，低延迟（毫秒级）是关键。Key 可以是 `user_id`, `device_id` 等，Value 是各类特征值。
        *   **离线/准实时特征**: 同样存入 TiKV / HBase。
    *   **名单库 (List Database)**: **Redis Cluster**。用于存储黑、白、灰名单，要求极高的读取性能。
    *   **图数据库 (Graph Database)**: **NebulaGraph / Neo4j**。用于存储用户-设备-IP之间的关联关系，支撑离线团伙挖掘。
    *   **数据湖/数仓 (Data Lake/Warehouse)**: **HDFS / ClickHouse**。存储全量的原始日志和业务数据，用于离线分析和模型训练。

4.  **服务与策略层 (Service & Strategy Layer):**
    *   **风控决策引擎 (Risk Decision Engine)**:
        *   这是系统的核心，一个高可用的 **Go** 语言服务集群。它对外提供统一的风险评估 gRPC/HTTP 接口。
        *   当业务方（如登录、评论服务）发起请求时，它会执行b部分的实时风控流程。
    *   **策略与规则管理平台**:
        *   一个可视化的Web平台，让风控运营人员可以**动态配置和调整风控规则**，而无需代码上线。规则可以保存在配置中心（如 Apollo）或数据库中，风控引擎会实时加载。
    *   **模型服务 (Model Serving)**:
        *   将训练好的机器学习模型部署为服务（如使用 Triton Inference Server 或自建服务），供风控引擎在需要时调用。
    *   **案例分析与标注平台 (Case Management & Labeling)**:
        *   对于被系统判定为"可疑"（REVIEW）的请求，会进入此平台由人工审核。审核员的标注结果（"确认为作弊"、"误判"）会成为新的高质量样本，反哺给模型训练，形成**反馈闭环**。

#### b. 实时风控流程 (Real-time Risk Control Flow)

当一个业务请求（如"发布评论"）需要进行风控时，其背后的调用链路如下：

1.  **业务服务调用**: `Comment-Service` 在执行数据库写入前，先调用 `Risk-Engine-Service` 的 `decide()` 接口，传入请求的所有上下文信息。
2.  **风控引擎决策过程 (耗时必须在 50ms 内完成)**:
    *   **Step 1: 前置名单校验 (Pre-check)**:
        *   从 Redis 中检查请求的 `user_id`, `device_id`, `ip` 是否在黑名单（直接拒绝 REJECT）或白名单（直接通过 ACCEPT）中。此步骤耗时 < 1ms。
    *   **Step 2: 特征获取 (Feature Fetching)**:
        *   并发地从 TiKV/HBase 和 Flink 状态中拉取该 `user_id`, `device_id`, `ip` 相关的**实时特征**和**准实时特征**。例如：
            *   `realtime_feat_user_comment_freq_1m` (用户1分钟内评论次数)
            *   `profile_feat_device_register_days` (设备注册天数)
            *   `profile_feat_ip_associated_account_num_7d` (IP七天内关联账号数)
    *   **Step 3: 规则引擎执行 (Rule Engine Execution)**:
        *   将获取到的特征灌入一个轻量级的规则引擎。
        *   运营人员配置的规则在此被执行，例如：`IF realtime_feat_user_comment_freq_1m > 10 AND profile_feat_device_register_days < 1 THEN score += 50`。
        *   规则执行后会得出一个风险分数。
    *   **Step 4: 模型预测 (Model Inference)**:
        *   如果规则分数达到一定阈值，或者针对某些高风险场景，风控引擎会继续调用**模型服务**。
        *   将特征向量传给模型，模型返回一个作弊概率（0.0 ~ 1.0）。
    *   **Step 5: 决策聚合 (Decision Aggregation)**:
        *   结合规则分数和模型概率，通过一个最终的决策树或加权模型，得出最终结论：**ACCEPT(通过)**, **REJECT(拒绝)**, 或 **REVIEW(人工审核)**。
    *   **Step 6: 返回与记录**:
        *   将决策结果返回给 `Comment-Service`。
        *   **异步**地将本次请求的 `trace_id`、所有特征、规则命中详情、模型输出、最终决策等详细信息，作为一个"风控日志"写回 Kafka，用于监控、归因分析和后续的模型训练。

#### c. 离线团伙挖掘

这是对抗专业黑产的关键。

*   **技术选型**: Spark GraphX 或 NebulaGraph。
*   **挖掘流程**:
    1.  **构图**: 每天 T+1，将过去一段时间（如30天）的用户行为数据抽象成一个巨大的异构图。图中包含 `User`, `Device`, `IP`, `PaymentAccount` 等多种类型的节点，通过行为（如"登录"、"下单"）建立边。
    2.  **社区发现**: 在图上运行 **Louvain**, **LPA** 等社区发现算法，寻找那些内部连接紧密、但与正常用户群体连接稀疏的"团伙"。这些团伙成员共享设备、IP的可能性远高于正常用户。
    3.  **风险传播**: 使用类似 **PageRank** 的算法，从已知的黑产节点出发，计算图中其他节点的"风险分数"。一个节点连接的风险节点越多，其自身的风险分就越高。
    4.  **名单生成**: 将挖掘出的高风险团伙、高风险分数的节点，加入到黑名单中，并通过数据总线同步到线上的 Redis 名单库中。

#### d. 高可用与性能挑战

*   **低延迟**: 整个实时决策链路必须在 50ms 以内完成。优化点在于：特征库的性能（TiKV/HBase 选型与调优）、特征获取的并发度、规则引擎的执行效率（避免在规则中使用正则表达式等慢操作）。
*   **高可用**: 风控系统是核心业务的"看门人"，其 SLA 必须极高（如 99.99%）。
    *   服务自身必须无状态、异地多活部署。
    *   强依赖的存储（Redis, TiKV）也必须是高可用的集群架构。
    *   **必须有降级预案**：当风控系统出现故障或超时时，业务方不能被无限期阻塞。必须有明确的 Fail-fast 策略，是选择**Fail-Open(默认放行，业务风险)**还是**Fail-Close(默认阻断，用户体验风险)**，需要根据业务场景权衡。
*   **数据一致性**: 风控系统对一致性的要求不是最高级别（允许短暂不一致），但数据的最终一致性必须保证。Flink 的 Checkpoint 机制和 Kafka 的 At-Least-Once/Exactly-Once 语义是关键。

#### e. 演进与对抗

风控的本质是持续的攻防对抗。

*   **策略A/B测试 (Shadow模式)**: 新的规则或模型上线前，会先以"影子模式"运行。它会进行完整的计算和决策，但决策结果只被记录而不实际生效。通过对比新旧策略的拦截率、误杀率，来评估新策略的优劣，确保上线是安全的。
*   **反馈闭环**: 人工审核的标注结果是系统自我进化的"养料"。这个闭环必须是自动化的，标注数据能自动流入样本库，并触发模型的自动重新训练和上线流程。
*   **可解释性**: 当一个用户被拦截时，客服或运营需要知道"为什么"。因此，风控日志必须清晰地记录下每一条命中规则和模型的贡献分数，这对于申诉处理和策略优化至关重要。 