# 系统设计题："腾讯视频创作者（UP主）粉丝动态Feed流"系统

在腾讯视频，用户不仅可以看视频，还可以关注自己喜欢的创作者（UP主）。当创作者发布新的动态（比如发布了新视频、发表了图文帖子、发起了直播预告等），我们需要将这些动态实时或准实时地推送给所有关注他的粉丝，形成每个用户个性化的Feed流。

请设计这样一个系统，要求：
1.  **高可用性**：系统不能有单点故障，能够容忍部分组件失效。
2.  **高并发读写**：支持千万级创作者发布动态，亿级用户在线刷新Feed。
3.  **低延迟**：用户刷新Feed时，能毫秒级看到最新动态。粉丝接收到关注的创作者动态的延迟在分钟级以内。
4.  **可扩展性**：架构能够水平扩展，以应对未来用户和业务的增长。

---

好的，这是一个非常经典的社交Feed流设计问题，其核心在于如何平衡"写扩散"和"读扩散"的矛盾。我的设计思路如下：

### a. 整体架构

这是一个集成了实时消息处理、离线分发和在线服务于一体的复杂系统。

1.  **动态发布接入层 (Write Path)**:
    *   创作者通过APP发布动态，请求首先到达API网关。
    *   **动态发布服务 (Post Service)** 是一个无状态的Go服务。它负责处理动态的发布请求，完成内容的初步校验和安全审核（同步调用内容安全服务），然后将动态的元数据（`post_id`, `author_id`, `timestamp`, `content_type`, `payload_ref`等）写入一个高可用的消息队列（如Apache Kafka或Pulsar）中的`post_events` Topic。
    *   动态的正文、图片、视频引用等较大数据，则存入对象存储（COS）或分布式文件系统中。数据库中只存其引用。
    *   写入MQ成功后，立即向创作者返回成功，保证发布操作的低延迟。

2.  **动态分发处理层 (Fan-out Processing)**:
    *   **Feed分发服务 (Fan-out Service)** 是核心处理模块，它消费`post_events` Topic中的消息。
    *   对于每一条动态，分发服务会获取创作者的粉丝列表。
    *   根据选择的扩散模型（详见b部分），将这条动态的ID (`post_id`) 推送到对应粉丝的Feed时间线（Timeline）中。

3.  **Feed存储与服务层 (Read Path)**:
    *   **Feed时间线存储 (Timeline Storage)**: 我们为每个用户维护一个"收件箱"（Inbox），即他们的Feed时间线。我会选择 **Redis Cluster** 作为此场景的存储，使用 `Sorted Set (ZSET)` 数据结构。
        *   `Key`: `feed_timeline:{user_id}`
        *   `Member`: `post_id`
        *   `Score`: `timestamp` (用于排序)
    *   **动态内容存储 (Post Storage)**: `post_id` 对应的完整动态内容（作者信息、文本、图片/视频URL、点赞评论数等）存储在持久化的数据库中。我会选择 **TiDB 或 CockroachDB**，因为它们既支持SQL，又具备分布式扩展能力和高可用性。
    *   **Feed API服务 (Feed Service)**: 这是一个无状态的Go服务集群。当用户下拉刷新Feed时，此服务负责：
        1.  从Redis的ZSET中获取该用户的`post_id`列表（例如，`ZREVRANGEBYSCORE feed_timeline:{user_id} +inf -inf LIMIT 0 20`）。
        2.  使用获取到的`post_id`列表，批量地（`MGET`或`IN`查询）从TiDB中拉取完整的动态内容。
        3.  聚合数据，进行简单的业务逻辑处理（如补充用户是否已点赞等状态），然后返回给客户端。

### b. 核心模型设计：读扩散 vs 写扩散

这是Feed流设计的核心权衡点。

1.  **写扩散 (Fan-out on Write / Push Model)**
    *   **逻辑**: 当创作者A发布动态时，分发服务立即获取A的所有粉丝（F1, F2, ..., Fn），然后将这条动态的ID逐一写入每个粉丝的Redis时间线（收件箱）中。
    *   **优点**: 读取逻辑极其简单快速。用户刷新时，只需从自己的收件箱里拿数据即可，无需进行任何复杂的计算或关联查询，读性能极高。
    *   **缺点**:
        *   **写放大 (Write Amplification)**: 如果一个创作者有千万粉丝，发布一条动态就需要执行千万次写操作，对分发服务和Redis都会造成巨大压力。这就是"大V"问题。
        *   **僵尸粉资源浪费**: 大量粉丝可能是不活跃的"僵尸粉"，为他们推送和存储Feed是一种资源浪费。
        *   **不灵活**: 如果要修改Feed的排序逻辑（比如从纯时间序变为加入推荐权重），需要修改所有用户的收件箱，成本极高。

2.  **读扩散 (Fan-out on Read / Pull Model)**
    *   **逻辑**: 动态发布时，只将动态写入创作者自己的"发件箱"（Outbox）。当用户F刷新Feed时，系统实时获取F关注的所有创作者列表，然后去每个创作者的发件箱里拉取最新的动态，在内存中聚合、排序后返回。
    *   **优点**: 写入逻辑非常简单，发布动态几乎没有延迟和压力。
    *   **缺点**:
        *   **读放大 (Read Amplification)**: 如果一个用户关注了上千个创作者，每次刷新都需要做N次查询再聚合，读延迟会非常高，不可接受。
        *   **实现复杂**: 需要在读取时进行复杂的聚合排序，对Feed API服务性能要求高。

3.  **混合模型 (Hybrid Model) - 最终选择**
    为了兼顾读写性能，业界通用的最佳实践是采用混合模型。
    *   **划分标准**: 根据创作者的粉丝数，设定一个阈值K（例如K=10万）。
        *   **粉丝数 < K 的普通创作者**: 采用 **写扩散** 模型。他们发布的动态，由分发服务实时推送到其所有粉丝的收件箱中。
        *   **粉丝数 >= K 的大V/明星创作者**: 采用 **读扩散** 模型。他们发布的动态，只写入自己的发件箱。
    *   **用户刷新Feed时的逻辑**:
        1.  从用户的Redis收件箱中，获取由普通创作者推送来的动态ID列表。
        2.  获取用户关注的大V列表。
        3.  实时去这些大V的发件箱拉取最新的动态ID列表。
        4.  将两部分动态ID列表在Feed API服务中进行 **内存合并与排序**。
        5.  根据合并后的`post_id`列表，去TiDB拉取完整内容并返回。

    *   **优化**: 为了避免每次都实时拉取大V动态，可以在Feed API服务层增加一个本地缓存（如 `BigCache`），缓存大V的最新动态几秒钟，对同一台服务器上的请求起到加速作用。

    *   **模型优化与深入思考**:
        *   **动态"大V"界定**: 固定的粉丝数阈值K存在"阈值抖动"和不考虑活跃度的问题。可以引入更灵活的策略，如设置缓冲带（防止用户在阈值附近频繁切换模式），或采用综合评分（`Score = w1*粉丝数 + w2*活跃度`）来更准确地识别影响力大的创作者。
        *   **"中V"写扩散优化**: 对于粉丝数接近阈值K的"中V"（如拥有数万粉丝），一次性为其所有粉丝推送动态仍有较大压力。可以将单个"大任务"拆分为多个"小任务"，例如，Fan-out服务将一个9万粉丝的推送任务拆解成90个"为1000人推送"的子任务，再投递到消息队列由更多消费者并行处理，提高分发效率。

### c. 存储选型细节

*   **Redis (Timeline Storage)**:
    *   **高可用**: 采用Redis Cluster模式，数据分片存储，天然支持高可用和水平扩展。每个master至少带一个slave，用于故障转移和读写分离。
    *   **收件箱大小**: 不能无限增长。每个用户的ZSET只保留最近的N条动态ID（比如500条）。更早的历史动态可以通过"查看历史"功能，直接分页查询TiDB中的创作者发件箱。
*   **TiDB (Post Storage)**:
    *   **选型理由**: 它解决了传统MySQL分库分表的复杂性问题，提供了原生的分布式事务和弹性扩展能力。对于帖子内容这种结构化数据非常适合。
    *   **表设计**:
        *   `posts` 表 (`post_id`为主键, `author_id`, `create_time`, `content`等)。在`author_id`和`create_time`上建立联合索引，用于高效查询某个创作者的发件箱。
        *   `follows` 表 (`user_id`, `followed_id`, `timestamp`)，存储关注关系，在`user_id`和`followed_id`上分别建索引。

### d. 高可用与高并发

*   **全链路异步化**: 写入路径通过Kafka解耦，即使下游的分发服务或Redis出现暂时性抖动，发布操作也不会失败，数据被积压在Kafka中，待恢复后继续处理。
*   **服务无状态化**: 所有服务（Post Service, Fan-out Service, Feed Service）都设计为无状态，可以部署在K8s上，利用HPA根据负载（CPU/内存/QPS）进行自动水平伸缩。
*   **多级缓存**: 缓存是提升读性能和系统容量的关键。
    1.  **客户端缓存**: APP本地可以缓存上一刷的结果，用于网络异常或快速返回场景。
    2.  **CDN**: 动态中的图片、短视频等静态资源，其URL应推上CDN网络，加速全球用户访问。
    3.  **Feed合并结果缓存**: 针对混合模型，用户每次刷新都需要合并"推"和"拉"两部分数据，计算开销大。可以在Feed Service层增加缓存（如Caffeine或Redis），将用户**合并排序后的最终`post_id`列表**缓存一个短时间（如5-10秒）。后续刷新请求可直接命中此缓存，极大降低延迟。
    4.  **热点动态内容缓存 (Post Cache)**: 当爆款动态出现时，大量用户会用同一个`post_id`请求后端数据库，易造成数据库读热点。应在Feed Service和TiDB之间增加一个独立的动态内容缓存层，使用Redis或Memcached。服务优先从缓存获取动态详情，未命中再回源数据库，有效保护后端。
    5.  **基础数据本地缓存**: Feed Service内部可以使用本地缓存（如`BigCache`）短时间缓存热点大V的发件箱ID、用户的关注列表等基础数据，减少对下游存储的重复请求。
*   **数据库代理/连接池**: 服务层和数据库之间使用分布式数据库中间件或高性能连接池，管理海量连接。
*   **限流与熔断**: API网关层必须配置精细化的限流策略（基于用户ID、IP），防止恶意请求。服务间调用（如Feed Service -> Redis/TiDB）必须有熔断和重试机制，防止下游故障导致雪崩。

### e. 关键技术细节与挑战

*   **新关注用户的好友圈填充**: 当用户A新关注创作者B时，A的Feed流里应该立即看到B的历史动态。这需要触发一个一次性的 **回填（Backfill）任务**：异步地拉取B的发件箱中最近的M条动态，写入A的收件箱。对于大V，则无需回填，因为是读扩散模型。
*   **取关/拉黑**: 用户A取关B时，需要将B后续的动态从推送列表中移除。更复杂的是，是否要从A的收件箱中删除B已经发布的动态？这取决于产品策略。通常为了简单，可以不删除，让它自然滚动沉底。
*   **Feed排序**: 纯时间序的Feed流体验不一定最好。可以在混合模型的基础上，引入轻量级的排序。例如，在拉取Feed后，根据动态的互动分数（点赞、评论）、与用户的关系亲密度等，用一个简单的公式 `FinalScore = f(Time, Interaction, Relevancy, ...)` 在服务内存中进行重排（re-ranking），再返回Top N条。这就为未来的推荐算法介入预留了扩展点。
*   **分布式事务**: 发布动态涉及写入TiDB和写入Kafka两个操作，需要保证原子性。可以使用"事务性发件箱模式"（Transactional Outbox）来保证最终一致性：将"待发送的MQ消息"和业务数据在同一个本地事务中写入数据库，然后一个独立的realy服务轮询这个发件箱表，将消息可靠地投递到Kafka。
*   **实时互动计数**: 动态的点赞、评论数是实时变化的。如果将计数值与动态内容存在同一张表里，每次点赞都会引发对主表的热点更新，性能极差。更优的做法是引入独立的**计数服务 (Counter Service)**，利用Redis的`INCR`等原子操作来维护这些计数值。Feed Service在组装数据时，分别从内容存储（TiDB/Post Cache）和计数服务获取数据，最后聚合返回，实现读写分离。

### f. 远期演进与高级优化方向

当系统规模与业务复杂度进一步提升后，可以考虑以下更前沿的优化方向：

1.  **演进为算法推荐Feed**: 引入机器学习，从"人关注人"的社交Feed，演进为"算法服务人"的推荐Feed。通过"召回-排序-重排"的推荐管线，对海量内容进行个性化排序，提升用户粘性。这需要构建完整的特征工程、模型训练和在线预估系统。

2.  **基于活跃度的智能分发**: 打破对所有粉丝"一视同仁"的推送模式。系统可根据用户活跃度进行差异化分发：对高活用户，采用实时推送保证体验；对低活或流失用户，则不主动推送，当其回归时再通过"拉模式"临时拉取，极大节省无效的计算和存储资源。

3.  **时间线数据分层存储 (Tiered Storage)**: 针对用户可能产生的深度翻页行为，将Feed时间线数据按热度分层。近期活跃数据存放在高性能的Redis中（热层），较早的数据迁移至成本更低的持久化存储（如ScyllaDB/Cassandra，温层），而非常古老的数据则归档到对象存储（冷层），实现存储成本和访问性能的平衡。

4.  **与WebSocket结合提升实时感**: 对于正在线浏览Feed的用户，可结合WebSocket技术，当其关注的创作者发布新动态时，主动向客户端推送一个轻量级的"有新动态"提醒，实现"无刷新"加载，打造极致的实时互动体验。