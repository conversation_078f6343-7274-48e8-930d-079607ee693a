### a. 整体架构与数据流

这是一个典型的用户状态管理系统，但其挑战在于读（获取追剧列表）和写（一部剧集更新，需要通知所有追剧用户）的模式非常不同。

1.  **数据采集/事件触发层**：
    *   **用户主动操作**：用户在客户端点击"追剧"或"取消追剧"，请求通过API网关到达核心业务服务。
    *   **内容更新事件**：上游的媒资管理系统在有剧集更新（比如发布新的一集）时，会通过消息队列（如Kafka/Pulsar）发布一个`content_update_event`。这个消息是驱动后续"更新通知"流程的源头。

2.  **核心服务层**：
    *   **追剧核心服务 (Tracking Service)**：Go编写的无状态微服务集群。它负责处理所有核心业务逻辑，包括：用户的追剧/取消追剧操作、查询用户的追剧列表、接收内容更新事件并触发后续流程。
    *   **更新扇出服务 (Update Fan-out Service)**：这是一个独立的消费者服务，专门订阅`content_update_event`。它的唯一职责是，根据更新的剧集ID，高效地找出所有正在追该剧的用户，并为他们创建通知任务。
    *   **通知任务服务 (Notification Service)**：负责将"更新"这个信息，通过Push、站内信等方式下发给用户。

3.  **数据存储层**：
    *   **主存储 (Source of Truth)**：我会选择一个支持海量数据、高并发写入、易于水平扩展的NoSQL数据库，如**Cassandra**或**ScyllaDB**。用来存储用户和剧集的追剧关系。
    *   **缓存层 (Caching Layer)**：使用**Redis Cluster**。它至关重要，用于缓存用户的追剧列表，应对高并发的读取请求。
    *   **反向索引存储 (Reverse Index)**：这是本系统设计的关键之一。用于存储`剧集ID -> 用户ID列表`的映射，解决更新扇出问题。同样可以使用Cassandra或专用的KV存储。

4.  **数据流**：
    *   **读流程（查询追剧列表）**: `Client -> API Gateway -> Tracking Service -> Redis Cache (Hit) -> Client`。如果Cache Miss，则 `Tracking Service -> Cassandra (Miss) -> Rebuild Cache -> Client`。
    *   **写流程（用户点击追剧）**: `Client -> API Gateway -> Tracking Service -> Cassandra (Write) -> Redis (Cache Invalidate/Update)`。
    *   **更新通知流程**: `媒资系统 -> Kafka -> Fan-out Service -> Reverse Index (Read) -> Kafka (for Notification Tasks) -> Notification Service -> 用户`。

### b. 核心数据模型与存储选型

1.  **用户追剧关系表 (user_tracking_table)** - 存储在Cassandra
    *   这是系统的核心数据，以用户为中心进行建模。
    *   `PRIMARY KEY (user_id, series_id)`
    *   `user_id`: 分区键 (Partition Key)。一个用户的所有追剧数据会落在同一个节点上，查询效率极高。
    *   `series_id`: 聚簇键 (Clustering Key)。
    *   `Columns`: `status` (追剧中/已放弃), `last_watched_episode_id`, `watched_episodes_bitmap` (用bitmap记录已看集数，节省空间), `notification_enabled`, `update_time`。
    *   **选型理由**：视频平台用户量巨大，追剧关系数据量可达百亿甚至千亿级别。Cassandra的去中心化架构和优秀的写性能、水平扩展能力完美契合这个场景。以`user_id`做分区键可以避免热点问题。

2.  **用户追剧列表缓存** - 存储在Redis
    *   `Key`: `tracking:user:{user_id}`
    *   `Type`: `HASH` 或 `String(JSON)`
    *   `Value`: `series_id_1 -> {追剧详情}`， `series_id_2 -> {追剧详情}`... 或者直接缓存一个JSON序列化后的列表。
    *   **作用**：用户打开APP或个人中心时，需要立刻看到追剧列表。这个读QPS非常高，必须通过缓存来扛。缓存的失效策略采用Cache-Aside Pattern，写主库后，直接`DEL`掉缓存Key，下次读取时再重建。

3.  **剧集-用户反向索引表 (series_to_user_index)** - 存储在Cassandra
    *   这是为了解决"一部剧更新，如何找到所有追它的用户"的**扇出（Fan-out）**问题。
    *   `PRIMARY KEY ((series_id, shard_id), user_id)`
    *   `(series_id, shard_id)`: **复合分区键**。这是避免热点剧集问题的关键设计。如果只用`series_id`做分区键，一部几千万人追的热剧更新时，会对此分区造成巨大压力。引入一个`shard_id`（如0-99），当用户追剧时，`user_id`哈希到其中一个`shard_id`。
    *   `user_id`: 聚簇键。
    *   **工作流程**：当一部剧集（如`series_123`）更新时，扇出服务会并行的查询`100`个分区（`series_123_0`, `series_123_1`, ..., `series_123_99`），高效地获取全量用户列表，然后分发到下游的通知任务队列。

### c. 关键业务流程剖析

**剧集更新与通知扇出 (Fan-out on Write)**

这是系统中最具挑战的流程。

1.  **事件触发**：媒资系统在Kafka的`content_update` Topic中投递一条消息，如 `{ "series_id": "got_s8", "new_episode_id": "e6", "publish_time": "..." }`。
2.  **扇出服务消费**：`Update Fan-out Service`集群消费此消息。
3.  **查询用户**：服务根据`series_id`，启动100个并发的Goroutine，分别去查询反向索引表`series_to_user_index`的100个分区。
4.  **生成通知任务**：每个Goroutine查询到一批`user_id`后，不是直接调用通知服务，而是将这些`user_id`包装成一个个独立的通知任务（如`{"user_id": "...", "series_id": "...", "type": "push"}`），再投递到另一个专门的Kafka Topic，如`notification_tasks`。
5.  **解耦与削峰**：`Notification Service`集群按照自己的处理能力，从`notification_tasks` Topic中拉取任务进行处理。这样做的好处是：
    *   **完全解耦**：扇出服务不关心通知服务的死活和处理速度。
    *   **削峰填谷**：热剧更新瞬间产生的大量通知任务被Kafka承接，下游可以平滑处理，避免了对通知服务的瞬时冲击。
    *   **可靠性**：Kafka保证了任务不丢失，即使通知服务短暂宕机，恢复后也能继续处理。

### d. 高可用与数据一致性

*   **服务高可用**：所有Go服务都是无状态的，部署在K8s上，通过HPA自动伸缩，单个节点故障不影响整个系统。
*   **存储高可用**：
    *   Cassandra/ScyllaDB采用多数据中心部署，副本数（RF）至少为3，保证数据冗余和高可用。
    *   Redis使用Cluster模式，并为每个master配备slave节点。
*   **数据一致性**：
    *   **主库与缓存一致性**：采用"先更新数据库，再删除缓存"（Cache-Aside）的模式。这是一个经典的模式，能基本保证一致性。对于删除缓存失败的场景，可以引入重试机制，或通过订阅数据库binlog（如用Canal）的方式来异步、可靠地更新缓存，保证最终一致性。
    *   **主表与反向索引表的一致性**：当用户点击"追剧"时，需要同时写`user_tracking_table`和`series_to_user_index`。这可以通过客户端双写，或者更可靠地，只写主表，然后通过订阅主表的数据变更（如Cassandra的CDC或MySQL的Binlog）来异步更新反向索引表。后者架构更清晰，但有一定延迟。对于本场景，秒级延迟完全可以接受。

### e. 监控与预案

*   **核心监控指标**：
    *   **扇出耗时**：从收到更新事件到所有通知任务被投递到Kafka的总时长。这是衡量扇出效率的关键。
    *   **Kafka Topic积压**：`notification_tasks` Topic的积压量，反映了通知服务的处理能力是否成为瓶颈。
    *   **API响应时间**：获取用户追剧列表API的P99响应时间。
    *   **缓存命中率**：Redis缓存命中率，低于95%就需要关注。
*   **应急预案**：
    *   **通知风暴**：如果因为Bug或配置错误导致对一个剧集短时间产生大量重复的更新事件，需要在扇出服务入口处增加基于Redis的去重逻辑（例如，一个剧集在5分钟内只处理一次更新事件）。
    *   **热点剧集预热**：对于已知的即将上线的超级热剧，可以提前将其反向索引的分区（如果分片仍然不够）手动打散到更多的物理节点，甚至提前对缓存进行预热。

通过以上设计，我们可以构建一个能够支持亿级用户、千万级日活的视频平台，在保证数据高可用的同时，实现高效的追剧状态管理和秒级的更新触达。 