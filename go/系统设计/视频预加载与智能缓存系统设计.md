### 系统设计题："腾讯视频的视频预加载与智能缓存系统"

这是一个典型的结合了大数据、机器学习和高性能后端服务的复杂系统。其核心目标是在用户点击播放前，就将他们大概率会观看的视频内容（或部分内容）提前下载到客户端或边缘节点，从而实现"零等待"的极致播放体验。

---

#### a. 整体架构

这是一个数据驱动的、闭环的实时推荐与调度系统。

1.  **数据采集与处理层 (Data Collection & Processing)**:
    *   **实时数据流**: 用户在客户端的所有行为，如播放、暂停、快进、完播、点赞、评论、搜索、甚至是在某个页面上的停留时长，都会被封装成结构化日志，通过高可用的日志网关实时上报到 **Kafka** 集群。这些数据是预测模型最主要的输入。
    *   **离线数据**: 用户的静态画像（如性别、年龄、地域、会员等级）、视频的元数据（分类、标签、演员、导演）等，会从业务数据库（如 MySQL）通过 CDC (Change Data Capture) 工具（如 Canal）同步到数据湖或数仓中（如 HDFS, Iceberg）。

2.  **模型训练与预测层 (Model Training & Prediction)**:
    *   **离线训练 (Offline Training)**: 使用 **Spark** 或 **Flink** 对存储在数据湖中的海量历史数据进行分析和模型训练。产出的模型（如协同过滤、序列模型、图模型）会被推送到模型库中。
    *   **在线预测 (Online Prediction)**: 一个由 **Go** 语言编写的高性能微服务集群。它会加载最新的预测模型，接收来自客户端的实时预测请求。请求中会包含 `user_id` 和当前的上下文信息（如正在观看的视频 `content_id`、所在的页面等）。服务会迅速返回一个有序的、最可能被用户消费的视频 `content_id` 列表及其置信度分数。

3.  **策略与调度层 (Policy & Dispatch)**:
    *   这是系统的大脑。它接收来自预测层的候选视频列表，但并**不**直接将其下发给客户端。
    *   它会根据一系列动态策略进行决策，例如：
        *   用户的网络状态（WiFi 环境下可以更激进，蜂窝网络下则保守或需要用户授权）。
        *   设备的当前状态（电量、剩余存储空间）。
        *   用户等级（VIP 用户可能享有更优先的预加载服务）。
        *   成本控制（预加载会消耗 CDN 带宽，需要控制总体成本）。
    *   决策后，它会生成具体的预加载任务（例如：对视频A预加载 manifest 和前2个TS分片；对视频B仅预加载 manifest），通过长连接（如 WebSocket）或在客户端下一次心跳请求时，将任务下发。

4.  **客户端执行与反馈层 (Client Execution & Feedback)**:
    *   客户端内嵌一个 **预加载SDK**。它负责接收和管理预加载任务，利用系统空闲资源（如空闲网络IO、CPU）在后台执行下载。
    *   它需要精细化地管理本地缓存（如使用 LRU 策略进行淘汰，设定总大小上限）。
    *   当用户实际点击播放时，SDK会拦截播放请求，如果发现本地已有缓存，则直接从本地读取数据，实现"秒开"。
    *   最关键的是，客户端必须上报 **反馈数据**：预加载任务是否成功、缓存是否命中、最终用户播放的是哪个视频。这个数据形成了系统的闭环，用于评估模型效果和策略有效性，并作为下一轮模型训练的输入。

---

#### b. 核心：预测模型

预加载的成败，九成取决于预测的准确率。一个好的模型能最大化"缓存命中率"，减少"无效预加载"带来的带宽浪费。

1.  **召回 (Candidate Generation)**:
    *   这是模型的第一步，目标是快速从海量的视频库中圈选出一个较小的候选集（几百到几千个）。召回策略必须多样化，以保证覆盖率。
    *   **规则召回**: 优先级最高。例如，用户正在看《繁花》第5集，直接召回第6集。用户将某电影加入了"待看清单"，直接召回该电影。
    *   **协同过滤召回**: "User-CF"（看了A视频的人还看了什么）和 "Item-CF"（和A视频相似的视频）。这是最经典的召回方式。
    *   **序列模型召回**: 将用户的观看历史看作一个时间序列，使用 **LSTM** 或 **Transformer** 模型来预测序列中的下一个item。这能很好地捕捉用户的短期兴趣变化。
    *   **向量召回**: 利用 **Swing**, **Word2Vec** 等算法为每个视频生成 Embedding 向量。在线上，根据用户最近观看的几个视频的向量，在向量空间中（使用 **Faiss** 或 **Annoy** 这类索引库）寻找最相似的K个视频。

2.  **排序 (Ranking)**:
    *   召回层产出了候选集，排序层的目标是精准地预测用户对这个集合中每一个视频的"点击概率"。
    *   **技术选型**: **LightGBM** 或 **XGBoost**。这类梯度提升决策树模型是效果与性能的最佳平衡点，训练和预测速度都很快，非常适合工业界。
    *   **特征工程 (Feature Engineering)**: 这是决定排序模型上限的关键。
        *   **用户特征**: 年龄、性别、地域、活跃度、会员等级、历史观看偏好（类别、标签）。
        *   **物品特征**: 视频的分类、标签、时长、发布时间、近期热度（播放、点赞数）。
        *   **上下文特征**: 当前时间（工作日/周末、白天/深夜）、网络环境、设备型号。
        *   **交叉特征**: 这是最重要的部分，例如"用户偏好的类别"与"当前视频类别"的匹配度，"用户历史观看时长"与"当前视频时长"的相似度等。

---

#### c. 缓存策略与调度

预测模型给出了可能性，但"预加载什么"和"预加载多少"是需要精细权衡的工程问题。

1.  **分级缓存内容 (Tiered Content Caching)**:
    *   **P1 (高置信度)**: 对于模型预测置信度极高（如>0.9）的视频（如连续剧下一集），可以直接预加载 **完整的视频文件** 或 **关键的媒体头信息+前30秒内容**。
    *   **P2 (中等置信度)**: 对于置信度中等的视频，只预加载 **DNS解析结果、M3U8播放列表文件、密钥文件（如有）、以及视频的第一个GOP（通常是2-4秒）**。这已经能极大优化首帧时间，且成本很低。
    *   **P3 (低置信度)**: 仅做 **DNS 预解析 (DNS Prefetching)**。成本几乎为零，但也能带来一定的网络建连收益。

2.  **动态策略引擎 (Dynamic Policy Engine)**:
    *   技术上可以基于一个规则引擎（如 Drools 或自研）实现。
    *   **输入**: 预测结果（`content_id` 列表+分数）、用户实时状态（网络、设备）、全局配置（成本预算、不同网络下的最大预加载大小）。
    *   **输出**: 具体的 `(content_id, cache_level)` 任务列表。
    *   **示例规则**: `IF user.network == 'WIFI' AND prediction.score > 0.8 AND device.storage > 1GB THEN cache_level = P1`。

3.  **任务下发 (Dispatching)**:
    *   **Push 模式**: 推荐使用 **WebSocket** 长连接。当用户在App内的行为触发了一次新的预测和策略计算后，服务端可以主动将最新的预加载任务推送到客户端，实现毫秒级响应。
    *   **Pull 模式**: 作为补充。如果长连接断开，客户端可以在心跳请求或其它API请求中顺便"拉取"预加载任务。

---

#### d. 高可用与高并发挑战

1.  **预测服务**:
    *   必须是无状态的，易于水平扩展。部署在 **K8s** 上，根据QPS和CPU使用率配置 **HPA** 自动伸缩。
    *   **本地+分布式缓存**: 服务的每个实例可以在本地缓存（如 **FreeCache**）热门视频的预测结果。同时，使用 **Redis Cluster** 作为分布式缓存，存储用户画像特征和召回结果，减少对后端数据库和模型的直接请求。

2.  **客户端健壮性**:
    *   SDK 必须妥善处理各种异常：网络切换（预加载任务需要能断点续传）、存储空间满、系统因资源紧张杀死后台进程等。
    *   **资源谦让**: SDK 的后台下载任务必须设置为低优先级，绝不能与用户的前台操作（如刷信息流、看视频）抢占网络和CPU资源。

3.  **系统降级与熔断**:
    *   如果预测模型或策略引擎出现故障，必须有降级方案。可以降级为使用简单的规则（如只预加载下一集），甚至完全关闭预加载功能，保证核心的播发功能不受影响。
    *   对所有下游依赖（Redis、Kafka、模型服务）的调用，都必须配置严格的超时和熔断器（如 **GoBreaker**）。

---

#### e. 效果评估与迭代

一个无法衡量效果的系统是没有生命力的。

1.  **核心指标**:
    *   **业务指标**: **平均首帧耗时 (Avg. FFS)**、**视频卡顿率 (Buffering Rate)**。这是最终要优化的目标。
    *   **系统指标**: **预加载缓存命中率 (Cache Hit Rate)**（这是衡量模型和策略有效性的核心）、**无效流量占比 (Waste Bandwidth Ratio)**（衡量成本）。
    *   **模型指标**: **Precision@K**, **Recall@K**, **GAUC** (Group Area Under Curve)。

2.  **A/B实验平台**:
    *   这是验证所有模型和策略优化效果的唯一科学方法。
    *   必须建立严格的A/B实验流程，分出 **对照组**（无预加载）、**策略A组**、**策略B组** 等，通过小流量实验，对比核心指标的差异，验证新策略的有效性后才能全量上线。

3.  **反馈闭环**:
    *   A/B实验和线上监控的结果，要能反过来指导模型的迭代。例如，发现某个类型的视频预加载命中率很低，可能就需要调整该类别的特征权重或召回策略。这个持续优化的闭环是系统长期成功的保证。 