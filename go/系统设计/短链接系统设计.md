# 短链接系统设计

### **摘要**
本方案旨在设计一个支持海量请求、高可用、低延迟的短链接服务。方案采用读写分离的核心思想，并通过**预生成短码池**、**布隆过滤器**、**多级缓存策略**、**跨地域容灾**和**数据生命周期管理**等关键技术，来应对高性能、高可用、高扩展性及成本控制等核心挑战。

### **1. 整体架构**

系统采用读写分离架构，针对"生成短链"（写）和"解析重定向"（读）两条链路进行极致优化。

```mermaid
graph TD
    subgraph "用户与CDN"
        W_User("用户 (写)")
        R_User("用户 (读)")
        CDN
    end

    subgraph "核心服务"
        ShortURLService["ShortURL Service (写)"]
        RedirectService["Redirect Service (读)"]
    end

    subgraph "数据与存储 (在线)"
        CodePool["短码池 (Redis Set)"]
        MainDB["主数据库 (分片MySQL/TiDB)"]
        RedisCache["Redis 缓存"]
        BloomFilter["布隆过滤器"]
    end

    subgraph "离线与分析"
        CodeGenerator["离线短码生成服务"]
        Kafka["Kafka (数据分析)"]
        CDC["CDC (Canal)"]
    end
    
    %% 写路径
    W_User --> |1. 请求生成| ShortURLService
    ShortURLService --> |2. 获取短码| CodePool
    ShortURLService --> |3. 存映射关系| MainDB
    ShortURLService --> |5. 返回短链接| W_User

    %% 读路径
    R_User --> |1. 访问短链接| CDN --> RedirectService
    RedirectService --> |2. 查布隆过滤器| BloomFilter
    BloomFilter -- "不存在" --> R_User_404(返回404)
    BloomFilter -- "可能存在" --> RedirectService
    RedirectService --> |3. 查缓存| RedisCache
    RedisCache -- "命中" --> |5a. 重定向| R_User
    RedisCache -- "未命中" --> RedirectService
    RedirectService --> |4. 查数据库| MainDB
    MainDB --> RedirectService
    RedirectService -- "回写缓存" --> RedisCache
    RedirectService --> |5b. 重定向| R_User
    RedirectService --> |6. 异步发日志| Kafka

    %% 数据同步与离线任务
    CodeGenerator --> |持续补充| CodePool
    MainDB -- "变更数据捕获" --> CDC
    CDC --> |同步| RedisCache
    CDC --> |同步| BloomFilter
```

### **2. 核心挑战与设计目标**

*   **功能**:
    *   **写入**: 将长链接高效转换为唯一的、固定长度的短链接。
    *   **读取**: 将短链接以极低的延迟重定向至原始长链接。
*   **核心挑战 (非功能性要求)**:
    *   **海量读取 (High QPS)**: 重定向请求远超生成请求，需支持百万级QPS。
    *   **高可用性 (High Availability)**: 服务需7x24小时可用，具备跨地域容灾能力。
    *   **低延迟 (Low Latency)**: 重定向必须在几十毫秒内完成。
    *   **安全性 (Security)**: 短码需随机、不可预测，防止被恶意遍历和抓取。
    *   **可扩展性 (Scalability)**: 所有服务和存储都能平滑地水平扩展。
    *   **成本效益 (Cost-Effective)**: 在满足所有要求的同时，优化资源和运维成本。

### **3. 关键设计决策**

#### **3.1 短码生成策略：预生成短码池**
*   **痛点**: 传统的"自增ID + Base62编码"方案，生成的短码长度不固定且有规律，安全性差。
*   **优化方案**:
    1.  **离线生成**: 启动一个独立的后台服务，持续生成固定长度（如7位）的随机Base62字符串。
    2.  **唯一性校验**: 每生成一个，就去主数据库校验其唯一性。
    3.  **放入池中**: 将校验通过的可用码放入一个专用的**Redis Set**中，作为"短码池"。
    4.  **实时补充**: 监控池中可用码数量，当低于水位线时自动补充，确保池子永不枯竭。
*   **优点**:
    *   **性能极高**: 在线写服务仅需从Redis中`SPOP`一个码，逻辑简单，速度飞快。
    *   **安全无序**: 短码随机无规律，无法被顺序遍历。
    *   **体验一致**: 所有短码长度固定。

#### **3.2 缓存策略：三级防护**
*   **防缓存穿透：布隆过滤器 (Bloom Filter)**
    *   **问题**: 恶意用户用大量不存在的短码请求，导致请求穿透缓存，直击数据库。
    *   **方案**: 在所有读请求前置一个布隆过滤器。如果过滤器判断短码"一定不存在"，则直接返回`404`，拦截大量无效查询，保护后端系统。
*   **防缓存雪崩：高可用集群 + TTL随机化**
    *   **问题**: 大量缓存Key在同一时间集体失效，导致流量洪峰涌向数据库。
    *   **方案**: ① 为缓存TTL增加一个随机值，打散过期时间。② 部署高可用的Redis集群，避免单点故障。
*   **防缓存击穿：热点Key永不过期 或 分布式锁**
    *   **问题**: 单个热点链接的缓存失效，瞬时大量并发请求全部打到数据库。
    *   **方案**: ① 对可预知的热点Key（如活动链接）设置为永不过期，由后台任务主动刷新。② 在缓存未命中时，使用分布式锁（如`SETNX`）确保只有一个请求去回源数据库，其他请求短暂等待后即可命中新写入的缓存。

#### **3.3 高可用与容灾**
*   **跨地域部署**: 核心服务、缓存、数据库均进行多机房、跨地域部署，利用**GSLB/GeoDNS**实现流量的智能路由和故障转移。
*   **降级与熔断**:
    *   **服务降级**: 当后端存储完全不可用时，服务可降级返回一个友好的静态提示页面，而非直接报错。
    *   **熔断机制**: 对所有下游依赖（DB, Redis）的调用都配置**熔断器**，当依赖持续故障时能快速失败，防止雪崩效应。

#### **3.4 成本与数据生命周期管理**
*   **冷热数据分离与归档**:
    *   **策略**: 识别长时间（如90天）未被访问的"冷数据"。
    *   **归档**: 后台任务定期将冷数据的映射关系从昂贵的主数据库迁移到**低成本对象存储 (如AWS S3, 阿里云OSS)**中。
    *   **数据唤醒**: 当已归档的链接被再次访问时，触发一个"唤醒"流程，将其数据恢复到主库和缓存中。这次访问的延迟会稍高，但这是可接受的业务与成本的权衡。

通过以上改进，我们的短链接系统设计方案在**健壮性、安全性、性能、可用性和成本控制**等多个维度上都得到了显著增强，更能体现出资深工程师的架构设计水准。 