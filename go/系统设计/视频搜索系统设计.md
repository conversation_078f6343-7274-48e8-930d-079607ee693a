### 系统设计题："腾讯视频内容搜索系统"

#### a. 整体架构

整个系统可以分为两大块：**离线/近实时的数据处理层** 和 **在线查询服务层**。

1.  **数据采集与预处理层 (Data Ingestion)**
    *   **内容元数据**: 视频的基本信息（标题、简介、演员、导演、分类、标签等）存储在媒资库（如MySQL或TiDB）。通过Canal订阅其binlog，将变更数据实时推送到Kafka。
    *   **用户行为数据**: 用户的搜索、点击、播放、停留时长等行为，由客户端上报至日志网关，然后进入Kafka。这是排序模型的重要特征来源。
    *   **AI分析数据**: 由视频理解中台产出的数据，如通过ASR（语音识别）生成的字幕、OCR识别的视频内文字、CV识别的场景和物体标签。这些数据极大地丰富了视频的可检索文本维度。

2.  **索引构建层 (Indexing Pipeline)**
    *   **核心引擎**: 使用Flink或Spark进行流式或批处理。
    *   **处理流程**:
        1.  消费上述多种来源的Kafka数据。
        2.  **文档构建 (Document Construction)**: 将同一个视频相关的所有数据（元数据、AI标签、用户行为统计等）关联起来，形成一个大宽表，也就是搜索引擎中的"文档"。
        3.  **文本分析 (Text Analysis)**: 对文档中的文本字段进行分词（如使用Jieba分词）、去除停用词、同义词扩展等处理。
        4.  **索引生成**: 将处理好的文档批量写入到下游的搜索引擎集群中，构建核心的倒排索引和正排索引。

3.  **在线查询服务层 (Online Serving)**
    *   **API网关**: 接收来自客户端的请求，负责鉴权、路由、限流等。
    *   **查询理解服务 (Query Understanding, QU)**: 这是提升搜索体验的关键。负责对用户的原始查询进行解析和改写，如拼写纠错（"zhoushen" -> "周深"）、同义词转换（"星爷" -> "周星驰"）、实体识别（识别出电影名、人名）。
    *   **搜索服务 (Search Service)**: 核心的查询处理服务，内部通常是多阶段的：
        *   **召回 (Recall)**: 根据QU处理后的查询，从多个通道并行获取候选视频集。例如，从文本索引召回、从向量索引召回、从热门榜单召回等。目标是"求全"，保证相关结果不被漏掉。
        *   **排序 (Ranking)**: 使用机器学习模型（如LR、GBDT或深度学习模型）对多路召回的几千个结果进行精准排序，得到最终展示给用户的Top N结果。目标是"求准"。
    *   **缓存与数据库**: 高速缓存（如Redis Cluster）用于存储热门查询结果、视频特征等；后端数据库（如MySQL）用于获取最新的视频元数据作为补充。

---

#### b. 核心模块：查询理解与多阶段排序

1.  **查询理解 (Query Understanding)**
    *   **目的**: 填平用户输入与机器理解之间的鸿沟。
    *   **关键技术**:
        *   **拼写纠错 (Spelling Correction)**: 基于编辑距离算法或预先构建好的FST（有限状态转换器）模型。
        *   **查询建议/自动补全 (Autocomplete)**: 使用Trie树或FST存储高频查询前缀，实现快速响应。
        *   **实体链接 (Entity Linking)**: 依赖知识图谱（Knowledge Graph），识别出查询中的实体并链接到唯一的ID。例如查询"功夫里的包租婆"，能识别出电影《功夫》和角色"包租婆"。这使得结构化搜索成为可能。
        *   **意图识别 (Intent Classification)**: 判断用户是想找长视频、短视频、影人还是资讯，从而可以展示不同的结果样式。

2.  **多阶段搜索 (Recall & Rank)**
    *   **必要性**: 直接用复杂的模型在数十亿的视频库中计算，延迟无法接受。必须通过分层过滤来平衡效果和性能。
    *   **第一阶段：召回层 (Recall)**
        *   **技术**: Elasticsearch / OpenSearch集群，或自研索引。
        *   **目标**: 速度快，保证召回率。从全量数据中快速找出几千个相关的候选集。
        *   **召回策略**:
            *   **文本召回**: 最基础的一路。基于标题、简介、ASR字幕等文本信息，使用BM25算法。
            *   **向量召回**: 将视频和查询都表示为向量（Embedding），在向量空间中查找最近邻。可以解决"同义不同词"的问题，扩大语义相关性。需要FAISS或HNSW等近似近邻搜索库的支持。
            *   **其他召回**: 热门榜单召回、用户历史行为召回、同导演/演员召回等。
    *   **第二阶段：排序层 (Ranking)**
        *   **技术**: 一个独立的、可水平扩展的微服务，内置机器学习排序模型（如LightGBM）。
        *   **目标**: 在候选集上做精细化计算，保证排序的精准性。
        *   **模型特征 (Features)**:
            *   **文本相关性特征**: 来自召回层的BM25分数、向量相似度分数等。
            *   **视频自身特征**: 播放量、完播率、点赞数、上传时间（新鲜度）等。
            *   **用户个性化特征**: 用户画像标签、历史观看偏好、与查询视频的交互历史。
            *   **上下文特征**: 当前时间、用户设备、地理位置等。

---

#### c. 存储选型

*   **主搜索引擎**: **Elasticsearch / OpenSearch 集群**
    *   **理由**: 成熟的开源解决方案，功能强大，社区活跃，水平扩展能力强。非常适合处理文本搜索和聚合分析。新版本对向量搜索的支持也越来越好。
    *   **部署**: Master/Data/Coordinating节点分离；跨可用区部署分片副本，保证高可用；通过索引别名(Alias)实现平滑的索引重建和切换。
*   **元数据/在线特征存储**: **Redis Cluster**
    *   **理由**: 内存数据库，读写性能极高。
    *   **用途**: 存储排序模型需要的实时/近实时特征（如视频最近1小时的点击数）、缓存视频元数据、缓存热门查询的结果。
*   **离线数仓/数据湖**: **HDFS + Hive/Spark SQL + ClickHouse**
    *   **理由**: HDFS提供海量、廉价的日志存储能力。Hive/Spark用于大规模的离线ETL和模型训练。ClickHouse作为高性能的OLAP引擎，用于对用户行为进行快速的统计分析，为运营和算法迭代提供数据支持。
*   **知识图谱库**: **NebulaGraph / JanusGraph**
    *   **理由**: 存储实体（如影人、影视剧）及其复杂关系。图数据库在处理多跳关联查询时，性能远超传统关系型数据库，是实现高级查询理解的基础。

---

#### d. 高可用与高并发

搜索是一个读多写少（QPS上读写比可能超过1000:1）的场景，高可用和低延迟是生命线。

*   **多级缓存**:
    *   **客户端缓存**: 客户端可以缓存用户的搜索历史。
    *   **CDN/边缘节点缓存**: 对一些时效性不强的通用热搜词（如"电视剧排行榜"），可以在CDN层面缓存。
    *   **网关/服务本地缓存**: 在API网关或搜索服务内部，使用本地缓存（如FreeCache/BigCache）缓存热点查询的最终结果ID列表，TTL可设为分钟级。
    *   **分布式缓存**: Redis缓存视频的详细信息，避免每次都去"穿透"查询后端的DB和ES。
*   **服务容灾与弹性**:
    *   **全链路无状态化**: 在线服务（QU、排序服务等）全部设计为无状态，便于在K8s中进行快速的水平扩缩容（HPA）。
    *   **异地多活**: 在多个数据中心部署完整的服务集群，通过DNS或GSLB进行流量分配和故障切换。
    *   **降级与熔断**:
        *   **降级**: 如果精排服务超时或故障，可以降级直接返回召回层的结果，保证服务可用。如果视频详情服务故障，可以暂时只展示标题和缩略图。
        *   **熔断**: 对下游所有服务的RPC调用都设置严格的超时和熔断器（如gobreaker）。当某个下游（如ES）出现抖动时，能迅速切断请求，防止故障扩散导致雪崩。
*   **索引更新**:
    *   采用蓝绿部署（或别名切换）的方式更新索引。新索引在后台完全建好并测试通过后，通过原子切换别名的方式，瞬间将查询流量指向新索引，整个过程对用户无感知。

---

#### e. 搜索结果质量与反作弊

*   **A/B测试框架**: 这是持续优化搜索质量的核心。任何算法（召回、排序、QU）的修改，都必须通过A/B测试，在线上小流量进行效果验证。关注的核心指标包括：**搜索点击率 (CTR)**、**有效播放转换率 (Search CVR)**、**零结果率**等。
*   **人工干预平台**: 提供一个后台系统，让运营同学可以对特定查询（如热门事件、节假日）进行结果干预，如置顶、加精、下架。这是满足业务强需求的必要补充。
*   **反搜索作弊**:
    *   **识别**: 识别那些通过在标题、简介中堆砌不相关热词来骗取曝光的低质内容。
    *   **策略**:
        1.  **文本分析**: 通过算法识别关键词堆砌模式。
        2.  **行为分析**: 分析搜索结果的**"点击后行为"**。如果一个视频的曝光量很高，但点击率很低；或者点击后用户立刻退出（停留时间极短），这都是"标题党"或内容与标题不符的强烈信号。
    *   **惩罚**: 将这些信号作为强力的负向特征加入排序模型，对作弊内容进行打压。严重的直接从索引中清除。 