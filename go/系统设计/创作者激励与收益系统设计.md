### 系统设计题："腾讯视频创作者激励与收益系统"

#### a. 整体架构

这是一个典型的集成了大数据处理、业务规则引擎和在线服务的复合型系统。其核心挑战在于处理海量数据、应对复杂多变的激励规则，并保证结算的绝对准确。

```mermaid
graph TD
A["数据源 Data Sources"] --> Kafka
subgraph "Data Sources"
    A1["用户行为日志"] --> Kafka["Behavior Job"]
    B["广告曝光点击"] --> Kafka["Ad Job"]
    C["内容元数据"] --> Kafka["Content Info"]
end

subgraph "Calculation Layer"
    Kafka --> |"Real-time Estimation"| Flink
    Kafka --> |"T+1 Batch Settlement"| Spark
    Flink --> |"调用估算规则"| RuleEngine["规则引擎"]
    Spark --> |"调用结算规则"| RuleEngine

    Flink --> Redis["估算结果缓存"]
    Spark --> MySQL-CLUSTER["最终结算库"]
    Spark --> ClickHouse["指标明细库"]
end

subgraph "Service & Application"
    Redis --> CreatorBackend[Go 后台]
    MySQL --> CreatorBackend
    ClickHouse --> CreatorBackend

    MySQL --> AdminBackend[Go/Java 运营后台]
    RuleEngine --> AdminBackend

    MySQL --> |"发起支付"|PaymentGateway["支付网关"]
    CreatorBackend --> CreatorPortal["创作者平台"]
    AdminBackend --> AdminUI["运营后台UI"]
end
```

1.  **数据采集层 (Data Ingestion)**:
    *   **行为数据**: 用户播放、点赞、评论、分享、关注等行为日志，以及广告的曝光、点击日志，通过统一的SDK上报至Kafka集群。
    *   **内容数据**: 创作者发布的内容元信息（如原创/二创、独家/非独家、分类标签）从内容库通过Canal订阅Binlog的方式，同步到独立的Kafka Topic。

2.  **数据计算层 (Computation Layer)**:
    *   **核心引擎**: 采用"实时+离线"的混合架构。
        *   **实时估算引擎 (Flink)**: Flink消费上游所有Kafka数据流，进行轻量级聚合，为创作者提供一个近乎实时的"预估收益"仪表盘。这对于提升创作者体验至关重要。
        *   **离线结算引擎 (Spark)**: 每日凌晨（T+1），启动Spark批处理任务。这是进行正式、精确结算的核心，负责生成具有法律效力的账单。
    *   **规则引擎 (Rule Engine)**: 一个独立的微服务，负责管理和执行复杂且易变的激励规则。计算引擎在处理数据时，会调用此服务来获取并应用规则。

3.  **数据存储层 (Storage Layer)**:
    *   **实时估算缓存 (Redis Cluster)**: Flink计算的实时估算结果写入Redis，供创作者后台API快速查询。
    *   **结算结果库 (MySQL/TiDB Cluster)**: Spark计算出的最终结算数据是金融级别数据，必须存储在支持事务、高一致性的关系型数据库中。这是支付的黄金标准数据源。
    *   **指标与明细库 (ClickHouse/Doris)**: Spark同时会将聚合后的关键指标（如有效播放量、不同来源的收益构成）和明细数据写入OLAP数据库，用于复杂的Ad-hoc查询、报表生成和纠纷追溯。

4.  **服务与应用层 (Service & Application Layer)**:
    *   **创作者后台API (Go)**: 为创作者提供仪表盘、收益报表、提现等功能的后端服务。
    *   **运营后台API (Go)**: 供运营人员配置激励规则、审核结算单、处理申诉等。
    *   **支付网关 (Payment Gateway)**: 负责与公司财务系统或第三方支付渠道（如微信、支付宝）对接，执行最终的支付流程。

#### b. 核心计算与结算引擎

这是系统的"心脏"，准确性和稳定性是首要目标。

*   **技术选型对比**:
    *   **Flink (实时)**: 优点是延迟极低（秒级），能提供优秀的实时反馈。缺点是对于需要全局数据、跨天关联的精确计算，实现复杂且资源消耗大。因此，它最适合做"估算"。
    *   **Spark (离线)**: 优点是生态成熟稳定，非常适合T+1的批处理模式，能从容地处理一整天的全量数据，进行复杂的关联、聚合和精确计算。它的容错机制和确定性输出能力是金融结算场景的基石。

*   **离线结算流程 (T+1 Spark Job)**:
    1.  **数据源加载**: 加载前一天（T）的所有相关数据，包括行为日志、广告日志、内容元数据。
    2.  **数据清洗与反作弊**: 这是结算的第一道关卡。必须接入公司的风控系统，剔除机器流量、刷量行为等产生的无效数据。结算必须基于"有效行为"。
    3.  **指标聚合**: 以`(creator_id, content_id)`为键，聚合核心业务指标，如：有效播放时长、有效播放次数、广告预估收入、新增粉丝数等。
    4.  **规则匹配与计算**:
        *   对于每个创作者，调用**规则引擎**，获取其当前生效的激励计划（例如，他是新手创作者、腰部作者还是独家签约作者，规则完全不同）。
        *   将聚合指标输入规则，计算出各项收益，如：`基础播放收益`、`广告分成`、`活动奖励`、`付费内容分成`等。
    5.  **结果持久化**: 将最终的结算明细（包含计算过程快照）和总额，以事务的方式写入MySQL集群。**整个结算过程必须保证幂等性**，即重复运行T日的结算任务，结果永远一致。

#### c. 规则引擎设计

将易变的商业规则从代码中解耦出来，是系统灵活性的关键。

*   **必要性**: 创作者激励政策变化非常频繁（如"春节投稿激励活动"、"某游戏专项征稿"），硬编码会导致开发和上线周期过长，无法满足业务敏捷性。
*   **设计要点**:
    1.  **规则定义与存储**: 通过运营后台UI，将业务规则结构化（或使用AviatorScript、Groovy等脚本语言）并存储在数据库中。规则包含匹配条件（如用户等级、内容标签、时间范围）和计算逻辑（如阶梯单价、百分比分成）。
    2.  **规则版本化**: 所有规则的创建和修改都必须有版本记录。结算结果必须与当时生效的规则版本进行关联，这是审计和追溯的生命线。
    3.  **高性能执行**: 规则引擎服务在启动时，会将常用和热门的规则加载到本地缓存。执行时，根据入参（创作者、内容属性）快速匹配并执行计算逻辑。
    4.  **可测试性与回溯**: 提供"试算"功能，允许运营在上线新规则前，用历史数据进行模拟，验证其正确性和影响范围。

#### d. 数据一致性与准确性

对于金融系统，这是最高优先级的非功能性需求。

*   **黄金数据源**: 明确定义：**只有T+1离线结算引擎在MySQL中生成的结算单，才是唯一可以用于支付的依据**。实时估算数据仅供参考，必须在UI上明确告知创作者。
*   **对账系统 (Reconciliation)**:
    *   **内部对账**: 定期（如每日、每周）运行独立的校验程序，检查`SUM(创作者收益) + 平台成本/利润`是否与`总业务收入`（如广告总收入、会员费中用于分成的部分）能对应上。
    *   **外部对账**: 与支付渠道进行定期对账，确保发起的支付请求都已成功处理，金额无误。
*   **事务与幂等性**: 从数据聚合到结果写入，关键步骤要封装在Spark的事务性操作中。同时，通过"先删除T日数据，再插入新数据"的模式，保证任务可重复执行而不产生脏数据。
*   **纠错与调账机制**: 如果因规则配置错误或Bug导致结算错误，必须有一套标准流程（SOP）来进行处理。首先要能通过规则版本和日志定位到所有受影响的创作者和账单，然后生成"调账单"（正向或负向），在下个结算周期合并或单独处理。所有调账操作需要审批和记录。

#### e. 安全与风控

*   **结算反作弊**: 与单纯的业务反作弊不同，这里的作弊直接导致金钱损失。因此，在T+1结算时，不仅要过滤单个作弊行为，还要引入更长周期的分析，比如：识别"抱团刷量"的MCN机构、识别内容搬运但伪装成原创的账号等，并对这些账号的收益进行冻结或降级处理。
*   **权限管控**: 对规则引擎、结算审核、支付发起等后台功能，实施严格的RBAC（基于角色的访问控制）和多级审批流（Maker-Checker机制）。任何核心操作都需要多人确认。
*   **资金安全**: 严格遵守公司财务安全规范。服务本身不存储创作者的银行卡号等敏感信息，而是通过安全协议与支付网关交互，使用临时的、加密的Token。
*   **异常监控与告警**: 必须建立完善的监控体系。例如：
    *   某创作者单日收益突增100倍。
    *   当日总结算金额远超历史平均水平。
    *   规则引擎在短时间内被频繁修改。
    *   这些异常情况都应立即触发高级别告警，通知相关负责人进行干预。

#### f. 设计思考：为何要向创作者展示"实时预估收益"？

在系统设计评审中，一个常见且深刻的质疑是：**既然实时估算与最终结算存在偏差，直接展示给创作者，难道不会引发误解和麻烦吗？为何不只将它用作内部成本监控？**

这是一个非常核心的设计权衡。尽管存在偏差风险，但几乎所有头部内容平台（YouTube、Bilibili、抖音等）都选择向创作者提供预估收益数据。这背后的逻辑是：

1.  **核心价值：即时反馈是顶级激励**
    *   对于创作者而言，内容发布后最渴望的就是反馈。除了播放、点赞等互动数据，**收益反馈是最直接、最强大的正向激励**。
    *   当创作者看到新发布的内容能立刻带来收益数字的跳动时，这种"即时满足感"会极大地激发其后续的创作热情，并增强对平台的归属感。这是在激烈的内容平台竞争中，留存和激励核心创作者的关键运营手段。如果收益必须等待T+1甚至更久才能看到，这种激励效应将大打折扣。

2.  **风险管理：通过产品设计化解偏差问题**
    *   **明确的功能定位与用户预期管理**: 这是化解风险的基石。平台必须在产品UI/UX层面做出清晰区隔：
        *   **仪表盘 (Dashboard)**: 展示"预估收益(Estimated Revenue)"，并用醒目文字、帮助文档等形式，反复强调这只是一个参考值，用于分析内容表现趋势。
        *   **钱包/结算 (Wallet/Payout)**: 展示最终可提现的"账户余额(Balance)"，这个数字来源于严肃的、经过离线精算的"黄金数据源"。
    *   **偏差的合理性与透明化**: 平台需要让创作者理解偏差的来源是合理且必要的。最大的差异通常来自**反作弊和风控过滤**——剔除刷量等无效行为产生的虚假收益。这符合所有正常创作者的利益，保证了平台的公平性。因此，平台等于是在传递："我们让你先快速看到一个大概的结果，但最终结算时会扣除那些不应得的部分。"

3.  **一箭双雕：对内监控，对外激励**
    *   这个实时数据流实现了双重价值：
        *   **对内**: 它是一个高时效性的**成本监控工具**，让运营和财务团队能近乎实时地掌握成本动态，快速响应。
        *   **对外**: 它是一个高强度的**创作者激励工具**，是现代内容平台生态运营的重要组成部分。
