### 系统设计题："腾讯视频直播间内聊天室"系统

**面试官**：你好，我们来聊一个系统设计。腾讯视频有很多大型直播，比如体育赛事、明星直播、电竞比赛等，这些直播间的同时在线人数可能会达到千万级别。请你设计一个高可用、低延迟的直播间聊天室系统。

需要重点考虑以下几个方面：
1.  **海量用户与高并发消息**：如何支撑千万级用户同时在线，以及每秒数十万甚至上百万条消息的实时收发？
2.  **消息同步模型**：群聊消息的同步机制是什么？请详细阐述**读扩散（Read Diffusion）**和**写扩散（Write Diffusion）**两种模型的原理、优缺点以及在此场景下的选型考量。
3.  **消息的顺序与可靠性**：如何保证大部分用户看到的消息是基本有序的？如何确保消息"至少送达一次"？
4.  **系统整体高可用**：从客户端到服务端，如何保证系统的高可用性？

---

好的，这是一个极具挑战性的高并发即时通讯（IM）系统设计，特别是叠加了"直播间"这个超大规模群聊的场景。我的设计思路如下：

### a. 整体架构

这是一个典型的分层、解耦的IM系统架构，核心在于如何处理海量连接和消息的实时分发。

1.  **客户端 (Client)**
    *   通过专门的IM SDK接入。
    *   负责与接入层建立和维持长连接（WebSocket/TCP）。
    *   实现心跳保活、断线重连、消息去重、本地消息排序与渲染。
    *   管理每个聊天室的消息序列号（Sequence ID），用于拉取历史/离线消息。

2.  **接入层 (Connection Gateway)**
    *   **技术选型**：Go aio或类似的高性能网络框架。
    *   **核心职责**：
        *   维持与客户端的**持久连接**（WebSocket是首选，因为它对Web端友好且能穿透大多数防火墙）。
        *   处理用户认证、心跳、解析客户端协议。
        *   **有状态层**：每个网关节点维护了当前连接的用户ID列表。同时，将`user_id -> gateway_node_ip`的映射关系注册到分布式缓存（如Redis Cluster）中，供逻辑层进行精准推送。
    *   **扩展性**：通过LVS/Nginx等四层负载均衡设备，将客户端连接均匀分发到后端多个网关节点上，实现水平扩展。

3.  **逻辑层 (Logic Service)**
    *   **无状态服务集群**，是整个系统的"大脑"。
    *   **核心职责**：
        *   处理聊天消息的收发逻辑。
        *   管理群组关系（进入/退出直播间）。
        *   调用消息同步模型（读/写扩散）的核心逻辑。
        *   生成全局唯一、趋势递增的消息ID。

4.  **存储与依赖 (Storage & Dependencies)**
    *   **消息存储 (Message Store)**：
        *   **技术选型**：分布式NoSQL数据库，如Cassandra或HBase。这类数据库天然支持水平扩展，并且写入性能极高，非常适合消息流这种时序数据。
        *   **数据模型**：消息以`room_id`作为分区键（Partition Key），以`message_seq`作为聚簇键（Clustering Key）进行存储，保证同一聊天室内的消息物理有序，便于高效地范围查询。
    *   **分布式缓存 (Cache)**：
        *   **技术选型**：Redis Cluster。
        *   **用途**：存储用户信息、群组成员列表（对于小群）、`user_id -> gateway`路由信息等高频读取数据。
    *   **消息队列 (Message Queue)**：
        *   **技术选行**：Apache Kafka或Pulsar。
        *   **用途**：作为逻辑层和存储层之间的缓冲，实现核心流程的**异步化和解耦**。例如，用户发送的消息，逻辑层简单校验后立即写入Kafka，然后就可以给客户端ACK。后续由消费程序写入DB、进行扩散等。这可以极大提高写入接口的吞吐量和可用性。

### b. 核心：群聊消息的读/写扩散模型

这是IM系统，尤其是超大群聊设计的灵魂所在。

#### 1. 写扩散 (Write Diffusion / Push Model)

*   **工作原理**：当一个用户发送消息后，服务端会主动获取该群聊的**所有成员列表**，然后为**每个成员**的消息收件箱（Timeline/Inbox）都写入一条消息副本。用户拉取消息时，只需从自己的收件箱里读取即可。
*   **优点**：
    *   **读逻辑简单**：客户端逻辑简单，延迟低，因为消息是"推"到用户面前的。
    *   **实现直观**：业务逻辑相对好理解。
*   **缺点**：
    *   **写放大严重**：这是其**致命缺陷**。在一个千万人的直播间，一条消息意味着**千万次的写入**操作。
    *   **推送风暴与网络拥塞**：除了数据库写入，服务端还需要向千万个客户端推送**完整的、体积较大**的消息包（包含发送者信息、内容、元数据等）。这会瞬间产生巨大的网络流量，轻易压垮接入层网关和服务器的出口带宽，这个问题极难通过简单增加机器来解决。
*   **此场景下的选型**：**完全不适用**。写扩散模型只适用于成员规模较小的场景，例如几百人以内的微信群聊。对于动辄上万、百万、千万人的直播间，写扩散模型会瞬间压垮系统。

#### 2. 读扩散 (Read Diffusion / Pull Model)

*   **工作原理**：当一个用户发送消息后，服务端**只将这条消息写入一份**到该群聊的公共消息存储中（Group Timeline）。每条消息在群内有一个单调递增的序列号（`room_seq`）。服务端只向群内在线成员推送一个**轻量级通知**（例如：`{room_id: "...", new_seq: 10086}`），告知他们有新消息了。客户端收到通知后，根据自己本地记录的`last_seq`，主动向服务端拉取`(last_seq, new_seq]`之间的新消息。
*   **优点**：
    *   **写入成本极低**：无论群多大，一条消息永远只有**一次写入**。完美解决了写放大的问题。
    *   **扩展性好**：系统压力与群成员数量**解耦**，只与总的消息并发数有关。
*   **缺点**：
    *   **读取逻辑相对复杂**：客户端需要维护每个群的`last_seq`，并主动发起拉取。
    *   **可能产生读峰值**：如果一个"大V"发言，可能瞬间触发大量在线用户同时拉取消息，对读服务造成压力。
        *   **与写扩散的本质区别**：这里的压力与写扩散的"推送风暴"有本质不同。写扩散推送的是**完整的消息"包裹"**，瓶颈在推送链路和网络带宽，难以优化。而读扩散模式下，服务端广播的只是**极轻量的"通知纸条"**，网络开销极小。后续的"读峰值"是大量用户来拉取**同一条、不可变**的热点消息，这个压力可以通过多级缓存（本地缓存、Redis）甚至CDN来轻松化解，因此瓶颈是可控且更容易优化的。
*   **此场景下的选型**：**唯一选择**。对于直播间这种海量用户的场景，必须采用读扩散模型。

**读扩散模型的优化**：
*   **合并推送**：服务端不必每来一条消息就推送一次通知，可以聚合100ms内的多条消息，合并成一个通知，降低推送频率。
*   **分级推送**：可以根据用户活跃度，对核心区的活跃用户实时推送，对潜水用户则延迟或合并推送。
*   **读服务优化**：拉取消息的服务可以做多级缓存（本地缓存 + Redis），并且由于消息的不可变性，可以大量使用CDN来缓存历史消息的拉取结果，进一步降低对源站DB的压力。

### c. 消息的顺序与可靠性

*   **顺序性保证**：
    *   IM系统中的"顺序"通常指"因果一致性"，即用户感知到的顺序符合逻辑。完全的全局绝对顺序很难实现且非必要。
    *   我们通过**单调递增的序列号（SeqID）**来保证。在读扩散模型下，每个聊天室（Room）维护自己的`room_seq`。服务端在写入消息到Kafka前，先从一个分布式的发号器（如Redis的INCR或类Snowflake算法）获取`room_seq`。
    *   客户端根据`seq`进行排序和渲染，即使消息因为网络原因乱序到达，也能在UI上保证正确顺序。如果发现`seq`不连续（如收到了10, 11, 13），就知道12丢了，可以主动去拉取。

*   **可靠性保证 (At-Least-Once)**：
    1.  **客户端 -> 服务端**：客户端发送消息后，会带上一个唯一的请求ID (`req_id`)。在收到服务端明确的ACK（表示消息已被Kafka持久化）前，如果超时或连接中断，客户端会重发。服务端根据`req_id`进行去重。
    2.  **服务端内部**：各层之间通过高可用的消息队列（Kafka）通信，保证了消息不会因为某个逻辑节点宕机而丢失。
    3.  **服务端 -> 客户端**：接入层网关将消息推送给客户端后，会等待客户端的ACK。如果未收到，会进行有限次数的重试。对于不在线的用户，消息会留存在Cassandra/HBase中，用户上线后主动拉取。

### d. 系统高可用设计

*   **接入层高可用**：
    *   网关节点是"有状态"的，节点宕机会导致其上所有客户端连接中断。
    *   **优雅退出**：节点下线前，可主动通知客户端重连，或通过负载均衡将其标记为不可用，引导新连接到其他节点。
    *   **快速恢复**：客户端SDK必须有完善的**自动重连机制**。通过DNS轮询或访问调度服务，快速找到一个可用的新网关节点并重建连接。
*   **逻辑层高可用**：
    *   完全的无状态服务，可以部署在K8s等容器化平台上。
    *   通过服务发现机制（如Etcd, Consul）管理节点，任意节点宕机，负载均衡和RPC框架会自动剔除，不影响整体服务。
*   **存储层高可用**：
    *   选用业界成熟的分布式组件，它们自身都具备高可用特性。
    *   **Redis**：采用哨兵或Cluster模式，实现主从切换和数据分片。
    *   **Kafka/Cassandra**：通过多副本（Replication）机制保证数据冗余。只要不是大多数副本同时失效，服务就是可用的。
*   **全链路降级与限流**：
    *   在网关和逻辑层入口，必须部署针对用户ID、IP、房间ID的**精细化限流**策略，防止恶意攻击或非预期流量冲垮系统。
    *   对下游依赖（如DB、缓存）的调用，必须有**熔断器**包裹。当某个依赖出现故障，可以快速失败，返回兜底数据（如"聊天室繁忙，请稍后再试"），避免请求堆积导致雪崩。
    *   **降级开关**：为非核心功能（如发送礼物特效、表情包）预留降级开关。在极端负载下，可以动态关闭这些功能，优先保证核心文字消息的收发。 