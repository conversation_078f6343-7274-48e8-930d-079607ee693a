在 Redis 中，哈希表（Hash）的底层实现使用的是字典（dict），其核心是哈希表结构。当 Redis 中的哈希表需要存储更多的键值对时，可能会触发哈希表的扩容过程。扩容过程的目的是为了保持哈希表的操作效率（例如查找、插入、删除等操作的时间复杂度）接近 O(1)。

### Redis 哈希表扩容核心要点

**核心思想：渐进式 Rehash (Progressive Rehashing)**
- **目的**：避免因一次性全量数据迁移导致的长时间阻塞，保证服务可用性。

---

#### 扩容过程

1.  **触发条件**：
    *   哈希表的**负载因子**（已用槽位 / 总槽位数）> 1。

2.  **分配新表**：
    *   创建一个大小通常为原表两倍的新哈希表 `ht[1]`，形成 `ht[0]` (旧) 和 `ht[1]` (新) 并存的局面。

3.  **渐进式迁移**：
    *   数据迁移不是一次性完成的，而是"顺便"进行的。
    *   在每次对哈希表进行**增、删、改、查**操作时，都将 `ht[0]` 中的一小部分数据迁移到 `ht[1]`。

4.  **迁移期间的操作**：
    *   **查询/删除/更新**：会同时在 `ht[0]` 和 `ht[1]` 两个哈希表上进行，确保数据不丢失。
    *   **新增**：直接写入新哈希表 `ht[1]`。

5.  **完成与切换**：
    *   当 `ht[0]` 的所有数据都迁移到 `ht[1]` 后，释放 `ht[0]`，并将 `ht[1]` 设置为新的 `ht[0]`，扩容完成。

---

#### 缩容
- 当哈希表元素数量减少，负载因子过低时，也会触发类似的渐进式 `rehash` 过程来收缩哈希表，释放多余内存。

### 具体扩容策略

- **惰性迁移**：数据迁移是渐进式的，只有在对哈希表进行操作时才会发生。这样做的目的是为了避免一次性迁移造成的系统性能下降。
  
- **避免主键竞争**：在渐进式 rehashing 过程中，新旧哈希表会同时存在。因此，在执行插入、删除、查询操作时，Redis 会先在新哈希表中查找，如果未找到，再到旧哈希表中查找。这样可以确保扩容过程中数据的一致性。

- **扩容与缩容**：Redis 不仅会在需要时对哈希表进行扩容，当哈希表中的键值对数量减少时（例如被删除），Redis 也会根据一定的条件触发哈希表的缩容操作，释放多余的内存资源。

### 总结

Redis 通过渐进式 rehashing 实现了哈希表的扩容，确保了在扩容过程中系统的高效性和低延迟响应。通过这种方式，Redis 能够在处理大量数据时依然保持高性能，避免了扩容过程中可能出现的性能瓶颈。