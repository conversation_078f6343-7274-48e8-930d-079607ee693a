### Redis 同步机制核心

Redis 同步机制确保数据在多节点间的一致性和高可用性，主要包含**主从复制**、**哨兵**和**持久化**三大核心。

#### 一、主从复制 (Replication)

**目的**：数据冗余、读写分离、高可用。

**过程**:
1.  **全量同步 (Full Sync)**:
    *   **触发**: 从节点首次连接主节点。
    *   **步骤**:
        1.  主节点执行 `BGSAVE` 生成 RDB 快照。
        2.  主节点将 RDB 文件发送给从节点。
        3.  从节点清空本地数据，加载 RDB 文件。
        4.  在生成 RDB 期间，主节点会将新的写命令缓存起来，在 RDB 发送完毕后，再发送给从节点。

2.  **增量同步 (Incremental Sync)**:
    *   **机制**: 全量同步后，主节点将所有写命令实时、异步地发送给从节点。
    *   **断线重连 (PSYNC)**:
        *   Redis 2.8+ 支持。从节点重连后，会尝试增量同步。
        *   如果主从节点的复制积压缓冲区（replication backlog）中还有断开期间的数据，则直接发送缺失的命令。
        *   否则，触发全量同步。

**特点**:
*   **异步复制**: 主节点不等待从节点响应，可能存在数据延迟。

#### 二、哨兵模式 (Sentinel)

**目的**: 监控 Redis 集群，实现主节点的自动故障转移 (Failover)。

**核心功能**:
1.  **监控 (Monitoring)**: 哨兵持续检查主从节点是否正常工作。
2.  **故障发现 (Failure Detection)**:
    *   **主观下线**: 单个哨兵认为主节点不可达。
    *   **客观下线**: 超过指定数量的哨兵达成共识，认为主节点不可达。
3.  **故障转移 (Failover)**:
    *   **选举领头 Sentinel**: 哨兵之间选举一个领导者来执行故障转移。
    *   **选举新主库**: 领头 Sentinel 从从节点中选出一个作为新的主库。
    *   **更新配置**: 命令其他从节点复制新主库，并通知客户端。

#### 三、持久化与同步的关系

*   **RDB**: 在**全量同步**时，主节点创建 RDB 快照发给从节点，作为数据基础。
*   **AOF**: 在**增量同步**时，主节点将写命令以 AOF 记录的形式发送给从节点，保证数据最终一致。