# Redis内存管理面试题精简版

## 1. 内存淘汰策略 (maxmemory-policy)

- **8种策略:**
    - `noeviction`: (默认) 内存满时报错。
    - `allkeys-lru`: 对所有key淘汰最近最少使用。**(常用)**
    - `allkeys-lfu`: 对所有key淘汰最不常用。
    - `allkeys-random`: 对所有key随机淘汰。
    - `volatile-lru`: 对设置过期时间的key淘汰最近最少使用。
    - `volatile-lfu`: 对设置过期时间的key淘汰最不常用。
    - `volatile-random`: 对设置过期时间的key随机淘汰。
    - `volatile-ttl`: 对设置过期时间的key淘汰剩余时间最短的。

## 2. LRU vs LFU

- **LRU (最近最少使用):**
    - **原理:** 淘汰最久未访问的数据。
    - **Redis实现:** 近似LRU (随机采样)，非精确。
    - **优缺点:** 实现简单，但可能误删突发访问的热点数据。
- **LFU (最不经常使用):**
    - **原理:** 淘汰访问次数最少的数据。
    - **Redis实现:** 基于频率计数器。
    - **优缺点:** 更准确识别热点，但实现复杂，内存开销大。

## 3. 过期删除策略

- **Redis组合策略: 惰性删除 + 定期删除**
    - **惰性删除:** 访问key时检查是否过期，过期则删除。
        - **优点:** 对CPU友好。
        - **缺点:** 过期key可能长期驻留内存。
    - **定期删除:** 定期(默认100ms)随机抽取key检查并删除过期key。
        - **优点:** 平衡CPU和内存，限制内存占用。

## 4. 内存使用优化

- **数据结构:**
    - 选对数据类型 (e.g., `ziplist`, `intset`)。
    - 避免大value。
- **Key设计:**
    - key名短、有意义。
    - 合理设计key的层级。
- **过期时间:**
    - 设置合理的过期时间。
    - 避免大量key同时过期 (可加随机数)。
- **内存监控:**
    - 定期检查内存、大key、碎片率。

## 5. 内存碎片问题

- **原因:** 频繁的内存分配与释放。
- **影响:** `mem_fragmentation_ratio` > 1.5，实际内存占用 >> 数据大小。
- **解决:**
    - **重启实例:** 最直接。
    - `MEMORY PURGE`: (Redis 4.0+) 整理碎片。
    - **jemalloc调优**: 调整内存分配器。
- **预防:**
    - 避免频繁更新和删除大key。
    - 使用合理的数据结构。

## 6. 大key问题

- **定义 (经验值):**
    - String > 10KB
    - Hash, List, Set, ZSet 元素数 > 5000
- **危害:**
    - 内存占用高，阻塞请求。
    - 网络传输开销大，主从复制延迟。
- **发现:**
    - `redis-cli --bigkeys`
    - `MEMORY USAGE <key>`
    - 慢查询日志。
- **解决:**
    - **拆分:** 将大key拆成多个小key。
    - **异步删除:** 使用`UNLINK`命令。

## 7. 核心内存监控指标

- **`INFO memory`:**
    - `used_memory`: Redis数据占用内存。
    - `used_memory_rss`: 操作系统分配给Redis的内存。
    - `mem_fragmentation_ratio`: 碎片率 (`used_memory_rss / used_memory`)。
        - > 1.5: 碎片严重。
        - < 1.0: 内存交换到磁盘 (Swap)。
- **`MEMORY USAGE <key>`:** 查看key的内存占用。
- **`redis-cli --bigkeys`:** 查找大key。

## 8. 内存配置优化

- **`maxmemory` 相关:**
    ```conf
    # 设置最大内存
    maxmemory 2gb
    # 设置淘汰策略
    maxmemory-policy allkeys-lru
    # 设置LRU/LFU采样数量
    maxmemory-samples 5
    ```
- **压缩数据结构配置:** (ziplist, intset等)
    ```conf
    # hash-max-ziplist-entries: Hash中ziplist编码的最大元素数
    # hash-max-ziplist-value: Hash中ziplist编码的元素value最大字节数
    # list-max-ziplist-size: List的ziplist编码配置
    # ... 其他类似 ...
    ```
    *注：保持默认值通常是好的开始。*

## 9. 内存泄漏排查

- **常见原因:**
    - Key未设置过期时间。
    - 大量无用数据堆积。
    - 客户端连接未释放。
- **排查步骤:**
    1. `INFO memory` 监控内存增长趋势。
    2. `MONITOR` 命令抽样分析写入操作。
    3. `redis-cli --bigkeys` 扫描大key。
    4. 检查客户端连接数 `CLIENT LIST`。

## 10. 内存优化最佳实践

- **设计:**
    - 选用合适的数据结构。
    - 控制Key/Value大小。
    - 合理设置过期。
- **运维:**
    - 定期监控内存指标 (使用率, 碎片率)。
    - 配置合理的淘汰策略。
    - 预留足够内存。
- **应急:**
    - 临时调大 `maxmemory`。
    - 手动清理数据 (`UNLINK`)。
    - 扩容或重启。
