# Redis Master 节点宕机了，怎么进行数据恢复？

当 Redis 主节点宕机后，核心目标是**快速恢复服务**并**最小化数据丢失**。恢复策略通常分为以下几种：

### 方案一：自动故障转移 (高可用方案)

这是生产环境的**首选方案**，能最大程度减少人工干预和停机时间。

1.  **Redis Sentinel (哨兵模式):**
    *   **原理:** Sentinel 集群负责监控 Master 状态。当 Master 宕机，它会自动将一个 Slave 提升为新的 Master，并通知客户端更新连接。
    *   **优点:** 自动化、高可用。

2.  **Redis Cluster (集群模式):**
    *   **原理:** 去中心化架构，数据分片存储在多个 Master 节点。当某个 Master 宕机，集群会自动将其对应的 Slave 提升为 Master，继续提供服务。
    *   **优点:** 分布式、高可用、可扩展性好。

### 方案二：手动故障转移

在没有部署高可用方案的情况下，需要手动介入。

1.  **提升新主库:** 在数据最完整的从节点上执行 `SLAVEOF NO ONE` 命令，使其成为新的主节点。
2.  **切换从库:** 将其他从节点通过 `SLAVEOF <new-master-ip> <new-master-port>` 命令指向新的主节点。
3.  **更新应用:** 修改应用配置，连接到新的主节点地址。

### 方案三：从备份文件恢复 (兜底方案)

在主从节点都无法使用的情况下，这是最后的恢复手段。

1.  **找到备份:** 从备份服务器获取最新的 RDB 文件或 AOF 文件。
2.  **启动新实例:** 将备份文件拷贝到新的 Redis 服务器，并启动实例。
    *   **RDB:** 恢复速度快，但会丢失从上次快照到故障发生时的数据。
    *   **AOF:** 数据更完整（取决于 `appendfsync` 策略），但恢复时间较长。
3.  **数据丢失风险:** 此方案通常会造成一定的数据丢失。

### 总结与预防

| 恢复方式 | 优点 | 缺点 | 适用场景 |
| :--- | :--- | :--- | :--- |
| **Sentinel/Cluster** | 自动化，恢复快，数据丢失少 | 配置复杂 | 生产环境 |
| **手动故障转移** | 简单直接 | 需人工介入，恢复时间长 | 开发或简单场景 |
| **备份恢复** | 兜底方案 | 数据丢失风险大 | 灾难性故障 |

**预防措施:**
*   **部署高可用架构:** 优先选择 Sentinel 或 Cluster。
*   **配置数据持久化:** 同时开启 RDB 和 AOF，做好数据备份。
*   **加强监控告警:** 实时监控 Redis 状态，及时发现问题。
*   **定期进行容灾演练:** 确保故障时团队能快速响应。