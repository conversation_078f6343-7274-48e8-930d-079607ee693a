# Redis Stream 速查 (Cheatsheet)

## 1. 核心概念

### Stream 是什么？
- Redis 5.0 引入的**持久化**消息队列数据结构。
- 关键特性: **消费者组**, **消息确认(ACK)**, **可回溯**, **ID自增**。

### 消息ID
- 格式: `时间戳(ms)-序列号` (e.g., `1609459200000-0`)。
- 特点:
    - `*` 可让 Redis 自动生成ID。
    - 保证**绝对单调递增**和**唯一性**。

### 消费者组 (Consumer Group)
- **允许多个消费者**从同一个Stream中读取消息。
- **负载均衡**: Stream将消息分发给组内的不同消费者。
- **独立进度**: 每个消费者组都维护自己的消费进度。
- **消息状态**:
    - **Pending**: 已投递给消费者，但尚未确认(ACK)。
    - **Acknowledged**: 消费者已确认(ACK)处理完成。

## 2. 核心命令

| 命令 | 用途 | 示例 |
|---|---|---|
| `XADD` | 添加消息到Stream | `XADD mystream MAXLEN ~ 1000 * f1 v1` |
| `XREAD` | 独立消费 (从头/最新) | `XREAD STREAMS mystream 0-0` / `$` |
| `XGROUP` | 创建/管理消费者组 | `XGROUP CREATE mystream g1 0-0` |
| `XREADGROUP` | 以组模式消费消息 | `XREADGROUP GROUP g1 c1 COUNT 10 STREAMS mystream >` |
| `XACK` | 确认消息已处理 | `XACK mystream g1 <message-id>` |
| `XPENDING` | 查看待处理消息 | `XPENDING mystream g1` |
| `XCLAIM` | 转移待处理消息的所有权 | `XCLAIM mystream g1 c2 60000 <id>` |
| `XLEN` | 获取Stream长度 | `XLEN mystream` |
| `XINFO` | 查看Stream/Group信息 | `XINFO STREAM mystream` |


## 3. 对比其他方案

### Stream vs List
| 特性 | Stream | List |
|---|---|---|
| 消费者组 | ✅ | ❌ |
| 消息确认 (ACK) | ✅ | ❌ |
| 消息回溯 | ✅ | ❌ |
| 持久化 | ✅ | ✅ |
| 自动ID | ✅ | ❌ |

### Stream vs Pub/Sub
| 特性 | Stream | Pub/Sub |
|---|---|---|
| 持久化 | ✅ | ❌ |
| 离线消费 / 回溯 | ✅ | ❌ |
| 消息确认 (ACK) | ✅ | ❌ |
| 实时性 | 较好 | 极佳 |
| 消费者负载均衡 | ✅ (通过消费者组) | ❌ |

## 4. 常见面试题

### Q1: Stream相比List和Pub/Sub的优势？
- **相比List**: 引入了**消费者组**，实现了负载均衡、消息确认(ACK)和故障转移，支持**消息回溯**。
- **相比Pub/Sub**: 支持**消息持久化**，允许消费者**离线后上线消费**（消息不丢失），并有ACK机制保证消息可靠处理。

### Q2: Stream如何保证消息不丢失？ (可靠性)
1.  **持久化**: Redis的持久化机制(RDB/AOF)能保存Stream数据。
2.  **ACK机制**: 消费者处理完必须`XACK`，否则消息仍在`Pending`状态。
3.  **故障转移**: 消费者宕机，可用`XCLAIM`将`Pending`消息转移给其他消费者处理。
4.  **主从复制**: 配合Sentinel或Cluster保证Redis服务高可用。

### Q3: 如何处理消费者故障或慢消费？
1.  **监控**: 使用 `XPENDING` 监控各消费者的`Pending`消息数和闲置时间，发现异常消费者。
2.  **转移**: 使用 `XCLAIM` 将长时间未ACK的消息转移给其他健康的消费者。
3.  **扩容**: 如果是处理能力不足，可以增加消费者实例来水平扩展。
4.  **死信队列**: 多次`XCLAIM`后仍处理失败的消息，可以将其发送到专门的死信队列（另一个Stream）中，用于问题排查。

### Q4: Stream有哪些应用场景？
- **消息队列**: 异步任务、削峰填谷、日志收集。
- **事件驱动**: 微服务间的事件总线，实现服务解耦。
- **实时数据流**: IoT设备数据上报、用户行为跟踪、实时排行榜。

### Q5: 使用Stream的最佳实践？
- **限制长度**: 使用 `XADD` 的 `MAXLEN` 选项防止内存无限增长，是**必须要做**的内存优化。
- **及时确认**: 消费者处理完消息后应尽快 `XACK`，避免`Pending`列表过大。
- **批量消费**: 使用 `XREADGROUP` 的 `COUNT` 参数批量获取消息，提高吞吐量。
- **监控告警**: 监控Stream长度(`XLEN`)和消费延迟(`XPENDING`)，及时发现问题。
- **死信队列**: 为无法处理的"毒"消息建立死信队列机制。
