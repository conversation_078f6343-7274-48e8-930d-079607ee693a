# Redis 事务与 Lua 脚本核心知识点

## 一、Redis 事务

### 核心命令
- `MULTI`: 开启事务，后续命令入队。
- `EXEC`: 原子性执行队列中的所有命令。
- `DISCARD`: 取消事务，清空命令队列。
- `WATCH key [key ...]`: 监视一个或多个 key。如果在 `EXEC` 执行前这些 key 被其他命令修改，则事务失败。用于实现乐观锁。

### 事务特性与限制
- **原子性**: 事务中的命令要么全部执行，要么因命令入队时语法错误导致全部不执行。**但不支持运行时错误回滚**。
- **隔离性**: 事务执行期间，不会被其他客户端的命令打断。
- **无回滚**: 单个命令在 `EXEC` 后执行失败，其他命令仍会继续执行。
- **乐观锁 (`WATCH`)**: `WATCH` 提供了一种"检查并设置"(Check-and-Set)的机制，用于处理并发冲突。如果在`WATCH`和`EXEC`之间，被监视的`key`被修改，整个事务将失败。

---

## 二、Lua 脚本

### 为什么使用 Lua 脚本？
- **原子性**: 整个脚本作为一个原子操作执行，期间不会被其他命令插入。
- **减少网络开销**: 将多个命令打包在一个脚本中，减少了客户端与服务器之间的网络往返。
- **复用性**: `EVALSHA` 可以通过 SHA1 校验和来执行缓存过的脚本，避免重复传输。
- **功能更强**: 可以在服务端进行复杂的逻辑计算，弥补了 Redis 事务无法进行逻辑判断的不足。

### 核心命令
- `EVAL script numkeys key [key ...] arg [arg ...]`: 执行 Lua 脚本。
- `EVALSHA sha1 numkeys key [key ...] arg [arg ...]`: 执行已缓存脚本的 SHA1 摘要。
- `SCRIPT LOAD script`: 将脚本加载到服务器的缓存中，返回 SHA1 摘要。

---

## 三、高频面试题 (Q&A)

**Q: Redis 事务和数据库（如 MySQL）事务有何区别？**
- **回滚**: Redis 不支持运行时错误回滚；MySQL 支持。
- **隔离级别**: Redis 事务是串行化的，隔离性强；MySQL 提供多种隔离级别 (读未提交, 读已提交, 可重复读, 串行化)。
- **原子性定义**: Redis 的原子性指命令队列的原子执行，而非单个命令的成功；MySQL 的原子性保证事务内所有操作要么全成功，要么全失败。

**Q: 何时使用 Lua 脚本，何时使用事务？**
- **事务 (`MULTI`/`EXEC`)**: 适用于一系列无需逻辑判断的、简单的命令打包执行。
- **Lua 脚本**: 适用于需要复杂逻辑（如条件判断、循环）、需要原子性且高性能的场景（如分布式锁、限流）。

**Q: Lua 脚本有哪些限制？**
- **执行超时**: 默认 5 秒，长时间运行的脚本会阻塞整个 Redis 服务。
- **纯函数要求**: 脚本中不能使用非确定性函数（如涉及随机数），以保证主从复制的一致性。
- **无法访问外部资源**: 如文件系统、网络等。

**Q: `WATCH` 的原理是什么？**
- **乐观锁**。客户端执行 `WATCH` 时，服务器会记录被监视的 `key` 及其当前版本。当客户端执行 `EXEC` 时，服务器会检查这些 `key` 的版本是否改变。如果任何一个 `key` 的版本已变，说明被其他客户端修改过，事务将不会执行，并返回 `nil`。

---

## 四、经典应用场景示例

### 1. 分布式锁 (Lua)
```lua
-- 加锁: 保证原子性 (set if not exist + expire)
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value (通常是唯一请求ID)
-- ARGV[2]: 过期时间 (毫秒)
if redis.call('SET', KEYS[1], ARGV[1], 'PX', ARGV[2], 'NX') then
    return 1
else
    return 0
end

-- 解锁: 防止误删 (先get判断再del)
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value (必须与加锁时相同)
if redis.call('GET', KEYS[1]) == ARGV[1] then
    return redis.call('DEL', KEYS[1])
else
    return 0
end
```

### 2. 限流 (Lua - 滑动窗口)
```lua
-- KEYS[1]: 限流规则key, e.g., "ratelimit:user1"
-- ARGV[1]: 时间窗口大小 (秒)
-- ARGV[2]: 窗口内限制次数
-- ARGV[3]: 当前UNIX时间戳 (秒)
local key = KEYS[1]
local window = tonumber(ARGV[1])
local limit = tonumber(ARGV[2])
local current_ts = tonumber(ARGV[3])

-- 移除时间窗口之前的记录 (score为时间戳)
redis.call('ZREMRANGEBYSCORE', key, 0, current_ts - window)

-- 获取当前窗口内记录数
local count = redis.call('ZCARD', key)

if count < limit then
    -- 添加当前请求记录 (score和member都用时间戳)
    redis.call('ZADD', key, current_ts, current_ts)
    redis.call('EXPIRE', key, window) -- 避免冷数据无限增长
    return 1 -- 允许
else
    return 0 -- 拒绝
end
```
