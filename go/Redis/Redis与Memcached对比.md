# Redis vs. Memcached 快速对比

## 核心定位
- **Redis**: 功能丰富的内存**数据库**，支持多种数据结构、持久化和复杂操作。不仅是缓存。
- **Memcached**: 纯粹、高性能的分布式内存**缓存**系统。专注缓存，简单高效。

## 核心差异对比

| 特性 | Redis | Memcached |
| :--- | :--- | :--- |
| **数据类型** | **丰富**: String, List, Hash, Set, ZSet, ... | **单一**: 仅字符串 (key-value) |
| **持久化** | **支持**: RDB 快照, AOF 日志 | **不支持**: 数据仅存内存，重启丢失 |
| **功能** | **丰富**: 事务, 发布/订阅, Lua 脚本 | **精简**: 核心缓存功能 |
| **集群方案** | **官方**: Redis Cluster, 主从复制 | **客户端**: 依赖客户端实现分片 |
| **高可用** | **官方**: Sentinel (哨兵) 自动故障转移 | **不支持**: 节点间无通信 |
| **网络模型** | 单线程 (v6前) / 多线程IO (v6后) | 多线程I/O |
| **性能** | 非常高 (10W+ QPS) | 极致性能 (多线程下更高) |

## 如何选择？

### 选 Redis 的场景:
- 需要**复杂数据结构** (如排行榜、计数器、粉丝列表)。
- 需要**数据持久化**，重启不丢数据。
- 需要**高可用**的主从架构和自动故障转移。
- 需要**事务**保证操作原子性。
- 需要**发布/订阅**、**Lua脚本**等高级功能。

### 选 Memcached 的场景:
- **纯粹的KV缓存**，用于加速数据库查询、缓存页面片段。
- 对性能要求**极致**，且业务场景简单。
- 缓存集群规模**巨大**，希望水平扩展和运维简单。

## 一句话总结
- **Redis**: 功能强大的"瑞士军刀"，适用场景广泛。
- **Memcached**: 专注、极致、高效的"螺丝刀"。
