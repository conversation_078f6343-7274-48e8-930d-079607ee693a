**布隆过滤器 (Bloom Filter)**

- **核心概念**: 一种高效的概率型数据结构，用于判断元素是否存在于集合中。
  - **特点**: 可能存在误判（假阳性，即不存在的元素被误判为存在），但绝不会漏判（存在的元素一定能判断出存在）。

- **基本原理**:
  - **位数组 (Bit Array)**: 底层数据结构，初始全为 0。
  - **哈希函数 (Hash Functions)**: 多个独立的哈希函数。
  - **插入**: 对一个元素进行多次哈希，将结果对应的位数组位置置为 1。
  - **查询**: 对一个元素进行多次哈希，检查对应的位数组位置。如果**全为 1**，则元素**可能存在**；只要有**一个为 0**，则元素**一定不存在**。

- **优点**:
  - **空间效率高**: 位数组占用空间极小。
  - **查询速度快**: 时间复杂度为 O(k)，k 为哈希函数个数，是常数级别。

- **缺点**:
  - **存在误判率**: 无法完全避免假阳性，误判率随元素增多而升高。
  - **无法删除元素**: 直接删除位数组的 1 会影响其他元素。

- **核心应用场景**:
  - **防止缓存穿透**: 拦截对不存在数据的请求，避免压力直接打到数据库。是主要应用场景。
  - **大规模数据去重**: 如爬虫 URL 去重、海量数据判重。
  - **黑名单过滤**: 如垃圾邮件、恶意 IP 过滤。

- **Redis `RedisBloom` 模块核心命令**:
  - `BF.RESERVE <filter> <error_rate> <capacity>`: 创建一个指定错误率和容量的过滤器。
  - `BF.ADD <filter> <item>`: 添加元素。
  - `BF.EXISTS <filter> <item>`: 判断元素是否存在。
  - `BF.MADD` / `BF.MEXISTS`: 批量添加或判断元素。