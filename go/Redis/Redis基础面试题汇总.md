# Redis基础面试题汇总

## 1. Redis的数据类型及应用场景？

**String（字符串）**：
- **应用场景**：缓存、计数器、分布式锁、session存储
- **常用命令**：SET、GET、INCR、DECR、MSET、MGET
- **底层结构**：SDS（Simple Dynamic String）
- **特点**：二进制安全、O(1)获取长度、最大512MB
- **面试要点**：为什么用SDS而不是C字符串？（长度获取O(1)、防止缓冲区溢出、二进制安全）

**Hash（哈希）**：
- **应用场景**：存储对象信息、用户资料、商品详情
- **常用命令**：HSET、HGET、HMGET、HGETALL、HDEL
- **底层结构**：ZipList（小数据）→ HashTable（大数据）
- **转换阈值**：512个字段或64字节单个值
- **面试要点**：什么时候用Hash而不是String存储对象？（字段较多且需要单独操作时）

**List（列表）**：
- **应用场景**：消息队列、最新消息列表、时间线
- **常用命令**：LPUSH、RPOP、LRANGE、LTRIM、BLPOP
- **底层结构**：QuickList（双向链表+压缩列表）
- **特点**：有序、可重复、支持两端操作
- **面试要点**：为什么不直接用链表？（内存碎片、缓存局部性）

**Set（集合）**：
- **应用场景**：标签系统、好友关系、数据去重、抽奖系统
- **常用命令**：SADD、SMEMBERS、SINTER、SUNION、SREM
- **底层结构**：IntSet（整数小集合）→ HashTable
- **面试要点**：什么时候用IntSet？（元素都是整数且数量少于512个）

**ZSet（有序集合）**：
- **应用场景**：排行榜、延时队列、范围查询
- **常用命令**：ZADD、ZRANGE、ZRANK、ZREM、ZCOUNT
- **底层结构**：ZipList（小数据）→ SkipList+HashTable
- **面试要点**：为什么用跳表而不是红黑树？（实现简单、范围查询友好、并发性能好）

## 2. Redis为什么这么快？

1. **内存存储**：数据全部存储在内存中，避免磁盘IO
2. **单线程模型**：避免线程切换开销和锁竞争（6.0后网络IO多线程）
3. **IO多路复用**：使用epoll/kqueue高效处理网络IO
4. **高效数据结构**：针对不同场景优化的底层数据结构
5. **简单协议**：RESP协议解析简单，减少序列化开销

## 3. Redis单线程模型的演进？

**Redis 6.0之前**：
- 网络IO和命令执行都在单线程中完成
- 通过IO多路复用技术处理多个客户端连接
- 命令执行严格按顺序进行，保证数据一致性

**Redis 6.0之后**：
- **网络IO多线程化**：读写socket操作可以并行处理
- **命令执行仍单线程**：保证数据操作的原子性和一致性
- **性能提升**：提高网络IO处理能力，减少网络延迟影响

## 4. Redis的底层数据结构详解

**SDS（Simple Dynamic String）**：
- **用于**：String类型的底层实现
- **优势**：O(1)获取长度、二进制安全、动态扩容、预分配空间
- **面试要点**：相比C字符串的优势（防止缓冲区溢出、减少内存重分配次数）

**ZipList（压缩列表）**：
- **用于**：小数据量的Hash、List、ZSet
- **特点**：连续内存、节省空间
- **缺点**：连锁更新问题（最坏O(N²)）
- **面试要点**：什么是连锁更新？如何避免？

**HashTable（哈希表）**：
- **用于**：Hash、Set的底层实现
- **特点**：O(1)操作、渐进式rehash
- **扩容**：负载因子>1时触发
- **面试要点**：渐进式rehash如何实现？（分批迁移，避免阻塞）

**SkipList（跳表）**：
- **用于**：ZSet的底层实现
- **特点**：有序、O(logN)操作、支持范围查询
- **面试要点**：跳表的层数如何确定？（随机算法，平均logN层）

**IntSet（整数集合）**：
- **用于**：只包含整数的小Set
- **特点**：有序数组、二分查找、自动升级编码
- **面试要点**：编码升级过程（int16→int32→int64）

**QuickList（快速列表）**：
- **用于**：List的底层实现
- **结构**：双向链表+压缩列表
- **优势**：兼顾内存和性能，可配置压缩深度

## 5. 数据结构转换机制

| 数据类型 | 小结构 | 大结构 | 转换条件 |
|---------|--------|--------|----------|
| Hash | ZipList | HashTable | 元素>512个 或 单个值>64字节 |
| Set | IntSet | HashTable | 元素>512个 或 非整数元素 |
| ZSet | ZipList | SkipList+Hash | 元素>128个 或 单个值>64字节 |

**面试要点**：转换是不可逆的，只能从小结构转为大结构。

## 6. Redis事务机制？

**特点**：
- **原子性**：事务中命令要么全部执行，要么全部不执行
- **不支持回滚**：某个命令失败不会回滚其他命令
- **命令排队**：MULTI后命令排队，EXEC时一次性执行

**相关命令**：
- **MULTI**：开始事务
- **EXEC**：执行事务
- **DISCARD**：取消事务
- **WATCH**：监控key变化（乐观锁）

**使用场景**：
- 需要原子性操作的业务场景
- 配合WATCH实现乐观锁
- 批量操作提高性能

## 7. Lua脚本的优势？

**核心优势**：
- **原子性**：脚本执行期间不会被其他命令打断
- **减少网络开销**：多个命令一次性执行
- **逻辑复杂性**：支持条件判断、循环等控制结构
- **可复用性**：脚本可以被缓存和重复使用

**应用场景**：
- 分布式锁的实现
- 限流算法的实现
- 原子计数器操作
- 复杂的数据处理逻辑

**注意事项**：
- 脚本执行时间不宜过长
- 避免在脚本中使用随机函数
- 合理使用脚本缓存机制

## 8. Redis监控关键指标？

**性能指标**：
- **QPS**：instantaneous_ops_per_sec（每秒操作数）
- **延迟**：平均响应时间和P99延迟
- **命中率**：keyspace_hits/(keyspace_hits+keyspace_misses)

**资源指标**：
- **内存使用率**：used_memory/maxmemory
- **连接数**：connected_clients
- **网络IO**：网络输入输出字节数

**稳定性指标**：
- **慢查询数量**：slowlog长度
- **主从延迟**：master_repl_offset - slave_repl_offset
- **键空间命中率**：缓存效果的重要指标

**告警阈值建议**：
- 内存使用率 > 80%
- 命中率 < 95%
- 慢查询 > 100/分钟
- 主从延迟 > 1秒

## 9. Redis常见运维问题？

**内存问题**：
- 内存使用率过高：检查大key、设置淘汰策略
- 内存碎片率异常：考虑重启或内存整理
- 内存泄漏：排查未设置过期时间的key

**性能问题**：
- 响应时间慢：检查慢查询、网络延迟
- QPS下降：分析命令分布、检查阻塞操作
- 连接数过多：优化客户端连接池配置

**高可用问题**：
- 主从同步延迟：检查网络、调整复制参数
- 哨兵误判：调整心跳检测参数
- 集群脑裂：检查网络分区情况

## 10. 高频面试问题

### Q1: ZSet为什么用跳表而不是红黑树？
**答案**：
1. **实现简单**：跳表实现比红黑树简单得多
2. **范围查询**：跳表天然支持ZRANGE等范围操作
3. **内存局部性**：跳表内存访问模式更友好
4. **并发性能**：跳表更适合读多写少的并发场景

### Q2: Redis的内存布局是怎样的？
**答案**：
- **数据存储**：键值对数据
- **过期字典**：存储键的过期时间
- **复制积压缓冲区**：主从复制使用
- **AOF缓冲区**：AOF持久化使用
- **客户端缓冲区**：客户端输入输出缓冲


## 面试要点总结

**核心知识点**：
1. **数据结构**：5种基本类型及底层实现原理
2. **持久化**：RDB和AOF机制的原理和应用
3. **高可用**：主从复制、Sentinel、Cluster方案
4. **缓存问题**：穿透、击穿、雪崩的解决方案
5. **事务和脚本**：MULTI/EXEC事务和Lua脚本
6. **监控运维**：关键指标监控和故障处理

**面试技巧**：
- 结合实际项目经验回答
- 重点说明解决方案的优缺点
- 展示对Redis原理的深入理解
- 体现运维和优化的实践经验
