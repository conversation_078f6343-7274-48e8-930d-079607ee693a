# Redis分布式锁核心速查

## 1. 核心原理与场景

- **作用**：控制分布式系统中多进程对共享资源的互斥访问。
- **场景**：
    - **防重**：定时任务、消息消费。
    - **保一致**：库存扣减、数据一致性。
    - **抢资源**：秒杀活动、分布式调度。

## 2. 核心实现 (SET + Lua)

- **加锁 (原子操作)**:
  `SET lock_key unique_value PX 30000 NX`
    - `unique_value`: 客户端唯一ID (如UUID)，防误删。
    - `PX 30000`: 锁过期时间30秒，防死锁。
    - `NX`: Key不存在时才设置，保证互斥。

- **解锁 (Lua脚本保原子性)**:
  ```lua
  if redis.call("get", KEYS[1]) == ARGV[1] then
      return redis.call("del", KEYS[1])
  else
      return 0
  end
  ```
  - **逻辑**：先`get`判断是否为自己的锁，再`del`，防止误删。

## 3. 常见问题与对策

| 问题 | 描述 | 解决方案 |
|---|---|---|
| **锁超时** | 业务未执行完，锁已释放 | 启动守护线程（**看门狗**）为锁续期 |
| **误删锁** | A的锁超时，B获取锁，A执行完后删了B的锁 | `unique_value` + Lua脚本，解锁时原子判断 |
| **死锁** | 客户端宕机，锁无法释放 | 设置合理的过期时间 `PX` |
| **单点故障** | Redis Master宕机 | 主从复制 + Sentinel哨兵 或 Redis Cluster |

## 4. Redlock (红锁)

- **目标**：解决Redis单点/主从切换时的锁安全问题，提供更高可靠性。
- **原理**：
    1. 向N个独立的Redis实例申请锁 (N>=3，通常N=5)。
    2. 超过半数(N/2 + 1)成功且总耗时 < 锁有效期，则加锁成功。
    3. 失败则向所有实例发送解锁命令。
- **争议**：实现复杂，性能下降，且在时钟漂移、网络分区等极端情况下仍可能失效，可靠性有争议。

## 5. 方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|---|---|---|---|
| **Redis** | **性能高**、实现简单 | 锁机制不严格(时钟问题) | 高并发、高性能，能容忍少量不一致 |
| **ZooKeeper** | **强一致性**、可靠 | 性能不如Redis、复杂 | 可靠性、一致性要求高的场景 |
| **数据库** | 实现简单、自带事务 | **性能差**、有单点风险 | 并发低、已有数据库依赖的简单场景 |

## 6. 面试快问快答

- **Q: 如何保证加锁和设置过期时间的原子性？**
- **A:** 使用 `SET` 命令的 `NX` 和 `PX` 选项。

- **Q: 为什么要用唯一ID，并且用Lua脚本解锁？**
- **A:** 防止误删锁。Lua脚本保证"判断锁归属"和"释放锁"是原子操作。

- **Q: 锁的续期机制（看门狗）怎么实现？**
- **A:** 客户端开启一个后台线程，在锁过期前定期检查并延长锁的有效期。`Redisson`有内置实现。

- **Q: Redlock很重，实际工作中怎么选？**
- **A:** 绝大多数场景下，单实例Redis或高可用Redis集群（主从+哨兵）的方案已经足够。只有对锁的可靠性要求极高且能接受其复杂性的金融等场景，才会考虑Redlock。
