# Redis Bitmap 面试核心摘要

## 1. 核心概念

- **定义**: Redis中基于String类型的位操作数据结构，每个位只能存0或1。
- **特点**:
    - **内存高效**: 理想情况下每个条目只占1 bit。
    - **操作快**: `SETBIT`, `GETBIT` 时间复杂度为 O(1)。
    - **支持位运算**: `AND`, `OR`, `XOR`, `NOT`，方便进行集合运算。
- **底层**: Redis String，最大可达512MB (2^32位)，按需自动扩容。

## 2. 核心命令

- `SETBIT key offset value`: 设置指定偏移量上的位值 (0或1)。
- `GETBIT key offset`: 获取指定偏移量上的位值。
- `BITCOUNT key [start end]`: 统计指定范围内位为1的数量 (O(N)，大key慎用)。
- `BITOP operation destkey key [key ...]`: 对一个或多个key进行位运算。
- `BITPOS key bit [start] [end]`: 查找第一个指定的位值 (0或1) 的位置。

## 3. 核心应用场景

- **用户签到/打卡**:
    - `SETBIT user:signin:202401 5 1`  (用户在1月第6天签到)
    - `BITCOUNT user:signin:202401` (统计1月签到总数)

- **活跃用户与留存分析**:
    - `SETBIT active:20240101 1001 1` (记录用户1001在1月1日活跃)
    - **日活(DAU)**: `BITCOUNT active:20240101`
    - **连续3日活跃**: `BITOP AND result active:day1 active:day2 active:day3`
    - **7日留存(第1天和第7天都活跃)**: `BITOP AND retention:7day active:day1 active:day7`

- **布尔状态标记 (在线状态、权限等)**:
    - `SETBIT online_users 10086 1` (用户10086在线)
    - `GETBIT online_users 10086` (检查用户是否在线)
    - `BITCOUNT online_users` (统计在线总人数)

## 4. 优缺点与避坑

### 优点
- **内存极省**: 在ID密集且连续的场景下，内存占用极小。
- **计算方便**: 内置位运算，可轻松实现交集、并集、差集等复杂计算。

### 缺点与注意事项
- **稀疏数据问题**:
    - 当ID非常稀疏且跨度大时 (如 `SETBIT key 0 1` 和 `SETBIT key 100000000 1`)，会预分配大量空闲内存，造成浪费。
    - **对策**: ID映射。使用Hash将不连续的字符串ID或稀疏数字ID映射到连续的offset。
- **性能问题**:
    - `BITCOUNT` 和 `BITOP` 是 O(N) 操作，操作的key很大时会阻塞Redis主线程。
    - **对策**:
        1. **分片(Sharding)**: 将一个大Bitmap按ID范围或时间拆成多个小key管理。
        2. **缓存结果**: 对于不频繁变化的统计结果进行缓存。
        3. **离线计算**: 放在从节点或通过定时任务进行分析。

## 5. 面试高频Q&A

### Q1: Bitmap 和 Set 怎么选？
- **场景**: 统计用户签到，ID从1开始连续。
  - **选 Bitmap**: 1亿用户只需要 `100,000,000 / 8 / 1024 / 1024 ≈ 12MB`。
  - **用 Set**: 内存开销会大几个数量级。
- **场景**: 存储文章的点赞用户，用户ID是无规律字符串。
  - **选 Set**: 直接存储用户ID，简单直观。
  - **用 Bitmap**: 需要做一层ID映射，增加复杂度。
- **总结**: ID密集、连续、只关心布尔状态用Bitmap；ID稀疏、无规律、关心元素本身用Set。

### Q2: 1亿用户，统计3天都连续签到的用户数，怎么做？
1.  每天用一个Bitmap记录签到用户：`signin:20240101`, `signin:20240102`, `signin:20240103`。
2.  对这3个key做 `BITOP AND` 运算，结果存入新key `signin:continuous:3days`。
    - `BITOP AND signin:continuous:3days signin:20240101 signin:20240102 signin:20240103`
3.  用 `BITCOUNT` 统计新key中1的数量。
    - `BITCOUNT signin:continuous:3days`
4.  **注意**: 由于数据量大，这个 `BITOP` 和 `BITCOUNT` 操作最好在从节点或凌晨执行。
