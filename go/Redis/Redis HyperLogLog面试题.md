# Redis HyperLogLog (面试精简版)

## 1. 核心概念

- **定义**: 一种概率性数据结构，用于 **估算** 集合的基数（不重复元素数量）。
- **核心特点**:
    - **省内存**: 固定的12KB内存，可以统计 `2^64` 个元素。
    - **有误差**: 存在 `0.81%` 的标准误差。
    - **不可逆**: 只能统计基数，无法获取具体元素。
    - **可合并**: 多个HyperLogLog可以合并，用于计算并集基数。

## 2. 核心原理

1.  **哈希分桶**: 对每个元素进行哈希，根据哈希值的一部分确定桶号（Redis用14位，`2^14=16384`个桶）。
2.  **前导零计数**: 计算哈希值剩余部分的前导零个数 `k`。
3.  **更新桶值**: 每个桶记录最大的前导零数 `max(k)`。直观上，`k` 越大，说明出现这个模式的概率越小，总体基数可能越大。
4.  **调和平均估算**: 使用所有桶的调和平均数来估算最终基数，以减小离群值（如某个桶的 `k` 特别大）的影响。

**为什么是12KB？**
Redis 使用 16384 (2^14) 个桶，每个桶需要 6 bits 来存储前导零的计数值 (`2^6 = 64`，足够表示50位哈希值的前导零个数)。
总内存: `16384 * 6 bits = 98304 bits = 12288 bytes = 12KB`。

## 3. 常用命令

- `PFADD key element [element ...]`：添加一个或多个元素。如果基数估算值改变，返回1，否则返回0。
  ```redis
  PFADD uv:20231201 user1 user2 user3
  ```
- `PFCOUNT key [key ...]`：返回一个或多个HyperLogLog的基数估算值。
  ```redis
  PFCOUNT uv:20231201
  ```
- `PFMERGE destkey sourcekey [sourcekey ...]`：将多个HyperLogLog合并到一个新的HyperLogLog中。
  ```redis
  PFMERGE uv:2023-montly uv:20231201 uv:20231202
  ```

## 4. 应用场景

- **网站UV统计**: 统计每日、每周、每月的独立访客数。
  ```redis
  # 每日UV
  PFADD uv:daily:20231201 user1 user2
  # 每月UV (通过合并每日数据)
  PFMERGE uv:monthly:202312 uv:daily:20231201 uv:daily:20231202 ...
  ```
- **用户行为分析**: 统计特定功能/页面的日活、月活用户。
- **大数据去重计数**: 如统计海量日志中的独立IP数、独立设备数等。

## 5. 与其他方案对比

| 方案 | 内存使用 | 精确度 | 适用场景 |
|---|---|---|---|
| **Set** | `O(N*M)` (N个元素, M为元素大小) | 100% | 需要精确值，数据量小 |
| **Bitmap** | `O(max_id / 8)` | 100% | 元素为连续或密集整数 |
| **HyperLogLog** | 固定 12KB | ~99.19% | 海量数据，可接受误差 |
| **BloomFilter** | 可配置 | 存在误判 | 存在性检查（"可能存在"或"一定不存在"） |

**如何选择?**
- **需要精确值、集合操作、获取成员**: 用 `Set`。
- **数据是连续整数ID（如用户ID）**: 用 `Bitmap`。
- **海量数据、内存敏感、只需近似基数**: 用 `HyperLogLog`。
- **判断一个元素是否存在于海量数据中**: 用 `BloomFilter`。

## 6. 面试要点

- **误差来源**:
  1.  **哈希冲突**: 概率极低，可以忽略。
  2.  **概率估算**: 算法本身的固有误差。
  3.  **边界效应**: Redis针对小基数和超大基数场景有优化和修正算法。

- **HyperLogLog的限制**:
  - 无法获取元素成员。
  - 无法删除元素。
  - 不支持直接计算交集。

- **优化技巧**:
  - **批量操作**: 使用 `PFADD` 一次添加多个元素。
  - **定期合并**: 将细粒度的数据（如日UV）合并为粗粒度数据（如月UV），并删除旧数据以节省内存。
  - **异步处理**: 对于非核心业务，可以将统计操作放入消息队列异步执行。
