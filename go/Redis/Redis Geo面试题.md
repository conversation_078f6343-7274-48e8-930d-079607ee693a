# Redis Geo 面试速查

## 1. 核心概念

- **是什么**: Redis 3.2+ 的地理位置数据类型，用于存储地理坐标并进行相关计算。
- **底层实现**: `ZSet` + `GeoHash`。
  - 使用 `ZSet` (有序集合)，`score` 存储 `GeoHash` 编码后的整数值，`member` 存储位置名称。
  - 使用 `GeoHash` 算法将二维的经纬度编码为一维的整数，方便索引。
- **核心功能**:
  - 存储地理坐标
  - 计算两点间距离
  - 查找指定范围内的点
  - 获取位置的 GeoHash

## 2. 常用命令

- `GEOADD key lng lat member`: 添加/更新一个或多个地理位置。
- `GEOPOS key member`: 获取指定成员的经纬度。
- `GEODIST key member1 member2 [unit]`: 计算两个成员间的距离。
- `GEOHASH key member`: 获取成员的 GeoHash 字符串。
- `GEORADIUS key lng lat radius unit`: 根据坐标范围查询。
- `GEORADIUSBYMEMBER key member radius unit`: 根据成员位置范围查询。
- `GEOSEARCH key ...`: Redis 6.2+ 引入的更强大灵活的范围查询命令。

## 3. GeoHash 算法

- **原理**: 空间填充曲线，将二维经纬度降维成一维字符串/整数。
  1. **二分编码**: 对经纬度范围进行多次二分，得到二进制序列。
  2. **交替合并**: 将经纬度的二进制位交错合并。
  3. **Base32编码**: 将合并后的二进制序列转换为字符串。
- **特性**:
  - **前缀匹配**: GeoHash 字符串前缀越长，代表的区域越小，位置越接近。
  - **精度可控**: GeoHash 字符串长度决定精度。
- **缺点**:
  - **边界问题**: 处于网格边界的两个点，可能 GeoHash 值差异很大。
  - **极地问题**: 在两极附近，经度变化对距离影响小，导致精度下降。

## 4. 应用场景

- **附近的人/物**: 社交、外卖、打车等 LBS 应用。
- **位置服务**: 附近店铺搜索、景点推荐、签到打卡。
- **轨迹追踪**: 物流监控、共享单车管理。

## 5. 性能与优化

- **数据分片**: 按城市或业务ID分片，避免单个 ZSet 过大。
  - 例如 `GEOADD locations:beijing ...`
- **精度选择**: 根据业务需求选择合适的 GeoHash 精度（长度），平衡精度和性能。
- **缓存策略**: 缓存热点区域的查询结果。
- **异步处理**: 对于非核心业务，可以异步更新用户位置。

## 6. 面试高频 Q&A

### Q1: Geo 底层为什么用 ZSet？
**A**:
1.  **核心数据结构**: ZSet 是有序集合，其 `score` 和 `member` 的结构完美匹配 Geo 需求。
2.  **GeoHash 映射**: 将二维经纬度通过 GeoHash 算法转换成一维的 `score` (52位整数)，`member` 存储位置名称。
3.  **高效查询**: 利用 ZSet 的 `score` 排序和范围查询能力 (`zrangebyscore`)，可以快速检索出附近的位置。

### Q2: GeoHash 的边界问题是什么？如何解决？
**A**:
- **问题**: 两个物理上很近的点，可能因为跨越了 GeoHash 编码的网格边界，导致其 GeoHash 字符串前缀完全不同。
- **解决方案**:
  1.  **查询周边区域**: 同时查询目标点所在的网格及其周围8个网格。
  2.  **合并与过滤**: 将9个网格的结果合并，然后在应用层精确计算距离，并根据实际半径进行二次过滤。

### Q3: 如何存储和查询大量的地理位置数据？
**A**:
1.  **数据分片**: 按行政区划 (如城市) 或业务逻辑 (如商圈) 将数据存储在不同的 ZSet 中。
2.  **分级索引**: 建立粗粒度 (如省级) 和细粒度 (如市级) 两级索引。先查粗粒度，再查细粒度。
3.  **冷热分离**: 对不活跃用户的位置数据进行清理或归档。
4.  **使用集群**: 利用 Redis Cluster 进行横向扩展。

### Q4: Geo 与数据库空间索引 (如 MySQL) 的对比？
**A**:
- **Redis Geo**:
  - **优点**: 内存操作，性能极高；API简单，上手快。
  - **缺点**: 功能相对基础，只支持点对点距离和范围查询；内存成本高。
- **MySQL 空间索引**:
  - **优点**: 功能强大，支持复杂空间查询 (如多边形、相交等)；数据持久化存储。
  - **缺点**: 性能通常低于 Redis；使用和优化相对复杂。
- **选型**:
  - 高并发、简单 "附近的人" 场景用 **Redis Geo**。
  - 需要复杂地理分析、持久化存储的场景用 **MySQL** 或 **PostGIS**。
