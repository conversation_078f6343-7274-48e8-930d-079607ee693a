# Redis缓存核心面试题精简版

## 1. 缓存三大问题：穿透、击穿、雪崩

### 缓存穿透 (Penetration)
- **问题**: 查询一个**不存在**的数据，导致请求每次都直接访问数据库。
- **方案**:
    1. **布隆过滤器**: 在访问缓存前，通过布隆过滤器快速判断key是否存在，拦截无效请求。
    2. **缓存空值**: 将数据库中不存在的key也缓存起来（value为null），并设置较短的过期时间。
    3. **接口层校验**: 校验请求参数的合法性，从源头拦截。

### 缓存击穿 (Breakdown)
- **问题**: 单个**热点Key**在某个瞬间过期，大量并发请求直接打到数据库。
- **方案**:
    1. **互斥锁**: 只允许一个线程回源查询数据库并重建缓存，其他线程等待结果。
    2. **热点数据永不过期**: 对热点数据不设置过期时间，由后台异步任务来更新缓存。

### 缓存雪崩 (Avalanche)
- **问题**: **大量Key**在同一时间集体过期，或者Redis实例宕机，导致流量瞬间涌向数据库。
- **方案**:
    1. **随机过期时间**: 在基准过期时间上增加一个随机值，打散过期时间点。
    2. **高可用集群**: 部署Redis哨兵或集群模式，避免单点故障。
    3. **限流降级**: 在应用层做限流，当缓存失效时，限制访问数据库的请求数。
    4. **多级缓存**: 使用本地缓存（如Guava Cache）+ 分布式缓存的架构。

---

## 2. 缓存与数据库一致性

- **核心问题**: 如何保证数据库和缓存两个数据源的状态一致。
- **解决方案**:

### Cache Aside Pattern (旁路缓存) - 最常用
- **读**: 先读缓存，如果缓存没有，则读数据库，然后将数据写回缓存。
- **写**: **先更新数据库，再删除缓存**。
    - **为什么是删除缓存而不是更新缓存？**
        - 懒加载，只有在需要时才加载数据，避免无效写。
        - 避免并发更新时，旧数据覆盖新数据的问题（例如，请求A更新DB，请求B更新DB，然后B更新缓存，A更新缓存，导致脏数据）。
    - **为什么是先操作DB，再删除缓存？**
        - 避免请求A更新DB，请求B读到旧缓存，然后A删除缓存，导致B回写旧数据到缓存。

### 延时双删
- **操作**: `先删除缓存 -> 更新数据库 -> 延时N秒后再次删除缓存`。
- **目的**: 解决在并发场景下，更新数据库后，读请求将旧数据放入缓存的问题。第二次删除是为了兜底。

### 消息队列
- **操作**: 数据库更新操作完成后，向消息队列发送一条消息，由消费者服务来异步更新或删除缓存。
- **优点**: 实现最终一致性，业务解耦。
- **缺点**: 架构变重，存在延迟。

---

## 3. 热点Key问题

- **问题**: 少数Key承载了绝大部分流量，导致单个Redis节点压力过大。
- **解决方案**:
    1. **热点发现**: 通过客户端、代理层或服务端异步任务（如LFU算法）来识别热点Key。
    2. **多级缓存**: 使用 `本地缓存 + 分布式缓存` 的架构。将热点数据缓存在应用服务器的本地内存中，减轻Redis的压力。
    3. **读写分离**: 利用Redis的主从复制，将读请求分散到多个从节点。

---

## 4. 大Key问题

- **影响**:
    - **内存**: 占用过多内存，可能导致OOM或频繁的驱逐。
    - **性能**: 操作大Key耗时长，阻塞Redis主线程。
    - **网络**: 网络传输开销大。
- **解决方案**:
    1. **拆分**: 将一个大的数据结构拆分成多个小的。例如，一个大的Hash拆分成多个小Hash。
    2. **压缩**: 对Value进行压缩（如Gzip, Snappy），以减少内存占用，但会增加CPU开销。
    3. **异步删除**: 使用 `UNLINK` 命令代替 `DEL`，将删除操作放到后台线程执行，避免阻塞。
    4. **监控**: 使用`redis-cli --bigkeys`等工具定期扫描和预警。

---

## 5. 缓存更新策略

- **Cache Aside (旁路缓存)**: **最常用**。应用程序负责维护缓存和数据库的一致性。
- **Read Through (读穿透)**: 应用只与缓存交互。当缓存未命中时，由缓存服务自身负责从数据库加载数据。对应用透明。
- **Write Through (写穿透)**: 应用只与缓存交互。当写数据时，由缓存服务负责同时更新缓存和数据库。保证强一致性。
- **Write Behind (写回)**: 应用只更新缓存。由缓存服务将更新批量、异步地写入数据库。性能高，但有数据丢失风险（例如，缓存宕机）。

---

## 6. 核心监控指标

- **性能指标**:
    - **命中率 (Hit Rate)**: `hits / (hits + misses)`，核心指标，通常要求达到95%以上。
    - **响应时间 (Latency)**: Redis命令的执行时间，需要关注平均值和P99分位值。
    - **QPS (Queries Per Second)**: 每秒查询数。
- **资源指标**:
    - **内存使用率 (Memory Usage)**: 接近 `maxmemory` 时需要注意，可能触发驱逐策略。
    - **CPU使用率**: 持续过高通常意味着有慢查询或高并发。
- **健康指标**:
    - **连接数 (Connected Clients)**: 连接数是否异常增多。
    - **持久化状态**: RDB/AOF的最后一次成功/失败状态。
