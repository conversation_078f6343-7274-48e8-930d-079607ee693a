# Redis 发布订阅核心笔记

## 1. 核心概念

- **定义**: 一种消息通信模式，发布者 (Publisher) 发送消息到频道 (Channel)，多个订阅者 (Subscriber) 接收。
- **优点 (Pros)**:
    - **实时性**: 消息实时推送，延迟低。
    - **解耦**: 发布者和订阅者完全解耦。
    - **广播机制**: 支持一对多消息广播。
    - **模式匹配**: 支持使用通配符 `*`, `?`, `[]` 进行模式订阅。
- **缺点 (Cons)**:
    - **不保证消息可靠性**:
        - **消息不持久化**: Redis 重启或服务宕机，消息会丢失。
        - **订阅者离线丢失**: 客户端离线期间发布的消息无法收到。
        - **无ACK机制**: 不保证消息被成功消费。
    - **无负载均衡**: 消息会发送给所有订阅者，消费压力无法分摊。
    - **慢订阅者问题**: 某个订阅者消费缓慢，可能导致Redis输出缓冲区 (`output buffer`) 溢出，影响其他客户端或整个服务。

## 2. 核心命令

- `SUBSCRIBE channel [channel ...]`：订阅一个或多个指定频道。
- `UNSUBSCRIBE [channel [channel ...]]`：退订指定频道，不指定则退订所有。
- `PSUBSCRIBE pattern [pattern ...]`：按通配符模式订阅。
- `PUNSUBSCRIBE [pattern [pattern ...]]`：退订指定模式。
- `PUBLISH channel message`：向指定频道发布消息。
- `PUBSUB CHANNELS [pattern]`：查看活跃的频道。
- `PUBSUB NUMSUB [channel ...]`：查看一个或多个频道的订阅者数量。
- `PUBSUB NUMPAT`：查看模式订阅的数量。

## 3. 工作原理

- Redis Server 内部通过一个字典（`pubsub_channels`）维护频道与订阅者列表的映射。
- `PUBLISH` 时，Redis 从字典中找到频道对应的所有客户端连接，然后将消息推送到每个客户端的输出缓冲区。
- 模式订阅（`PSUBSCRIBE`）则会额外维护一个链表（`pubsub_patterns`）。

## 4. 应用场景

- **实时通知系统**: 如系统公告、消息提醒、订单状态更新，利用其广播和实时性。
- **实时聊天室**: 多人在线群聊。
- **配置中心**: 当配置变更时，通过发布/订阅通知所有服务节点更新配置。
- **缓存同步**: 各服务节点订阅缓存失效消息，及时同步更新本地缓存。
- **微服务事件总线**: 作为轻量级的事件总线，用于服务间的事件通知与解耦。

## 5. Pub/Sub vs 消息队列 vs Stream

| 特性 | Pub/Sub | 消息队列(List) | Stream (Redis 5.0+) |
|:---|:---|:---|:---|
| 消息持久化 | 否 | 否 (需手动) | 是 |
| 消费者组 | 否 | 否 | 是 |
| 消息确认(ACK) | 否 | 否 | 是 |
| 离线消费 | 否 | 否 (需手动) | 是 |
| 负载均衡 | 否 | 否 (客户端实现) | 是 (通过消费者组) |
| 可靠性 | 低 | 中 | 高 |
| 适用场景 | 实时、可容忍丢失 | 简单任务队列 | 高可靠消息系统 |

## 6. 关键面试题

### Q1: 如何弥补 Pub/Sub 的可靠性问题？

Pub/Sub 本身不保证可靠性。如果业务需要，可以采用以下方案补偿：
1.  **重要消息持久化**: 结合 Redis 其他数据结构（如 List, Stream）或外部存储（如 MySQL, Kafka）来备份重要消息。
2.  **应用层ACK**: 订阅者处理完消息后，通过其他方式（如 `SET` 命令、回调接口）通知发布者。
3.  **连接监控与重试**: 监控订阅者连接状态，断线重连后主动拉取错过的历史消息（如果已做持久化）。

### Q2: 如何处理慢订阅者问题？

慢订阅者会阻塞Redis，导致输出缓冲区持续增长，最终可能被服务器断开连接。
1.  **异步处理**: 订阅者收到消息后立即放入内存队列（如 Go 的 Channel, Java 的 BlockingQueue），由独立的线程池异步消费，快速响应Redis。
2.  **监控与告警**: 通过 `CLIENT LIST` 命令监控客户端的输出缓冲区 (`qbuf` 和 `qbuf-bytes`)，当其大小持续增长时进行告警或主动剔除该客户端。
3.  **合理设计**: 避免在订阅者端执行耗时操作。

### Q3: Redis Stream 与 Pub/Sub 的主要区别是什么？

- **可靠性**: Stream 支持消息持久化、消费者组和消息确认（ACK），可靠性远高于 Pub/Sub。
- **消费模式**: Stream 支持消费者组（Consumer Group），可以实现消息在多个消费者间的负载均衡消费。Pub/Sub 是广播模式，所有订阅者都会收到相同消息。
- **历史消息**: Stream 支持按时间或ID回溯消费历史消息，Pub/Sub 不支持。

**总结**: 如果你需要一个可靠的消息系统，选择 Stream；如果只需要一个简单的实时消息广播、且能容忍消息丢失，Pub/Sub 更轻量、更简单。
