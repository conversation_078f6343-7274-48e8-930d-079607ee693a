# MySQL三大日志核心速查

### 三大日志对比

| 日志类型 | 核心作用 | 谁产生 | 记录内容 | 主要用途 |
|---|---|---|---|---|
| **Binlog** | 复制和恢复 | MySQL Server层 | 逻辑变更(SQL/行) | 主从同步、时间点恢复 |
| **Redo Log**| 保证持久性(D) | InnoDB | 数据页物理变更 | 崩溃恢复 |
| **Undo Log**| 保证原子性(A) | InnoDB | 数据的旧版本 | 事务回滚、MVCC |

### Binlog (二进制日志)
- **作用**: 记录所有对数据库的修改，用于主从复制和数据恢复。
- **格式**:
    - **Statement**: 记录原始SQL语句。
    - **Row**: 记录每一行数据的变更细节。
    - **Mixed**: Statement和Row的混合模式。

### Redo Log (重做日志)
- **作用**: 保证事务的持久性 (Durability)。
- **核心机制 (WAL)**: Write-Ahead Logging，先写日志，再写数据文件。
- **特点**:
    - 记录数据页的**物理**修改。
    - 大小固定，循环写入。
    - 数据库崩溃后，通过Redo Log恢复已提交但未写入磁盘的数据。

### Undo Log (回滚日志)
- **作用**: 保证事务的原子性 (Atomicity) 和实现MVCC。
- **核心机制**:
    - **事务回滚**: 当事务失败或手动回滚时，利用Undo Log恢复到修改前的数据状态。
    - **MVCC**: 在读已提交和可重复读隔离级别下，为读操作提供数据修改前的"快照"。

### 两阶段提交 (保证Binlog与Redo Log一致性)
1. **Redo Log (prepare)**: Redo Log写入磁盘，标记为prepare状态。
2. **Binlog**: Binlog写入磁盘。
3. **Redo Log (commit)**: Redo Log标记为commit状态。
* **目的**: 防止主库崩溃恢复后，主从数据不一致。若在步骤2之前崩溃，事务回滚；若在步骤2之后崩溃，事务提交。

### 核心面试题
**Q: Redo Log 和 Binlog 的区别？**
A:
- **层级不同**: Redo Log是InnoDB引擎层日志；Binlog是MySQL Server层日志。
- **内容不同**: Redo Log是物理日志（数据页修改）；Binlog是逻辑日志（SQL语句或行变更）。
- **写入方式**: Redo Log是循环写的；Binlog是追加写的。
- **作用不同**: Redo Log用于崩溃恢复，保证持久性；Binlog用于主从复制和时间点恢复。

**Q: Redo Log写满了怎么办？**
A: 触发Checkpoint，强制将脏页刷到磁盘，然后覆盖Redo Log。期间会阻塞用户写请求。

**Q: Undo Log何时被清理？**
A: 当没有任何事务再需要该Undo Log记录（即数据的旧版本）时，由后台purge线程回收。
