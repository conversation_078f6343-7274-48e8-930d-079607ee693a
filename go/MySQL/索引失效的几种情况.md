## 索引失效的几种情况（速查版）

1.  **函数/表达式**: `WHERE YEAR(create_time) = 2023` -> 索引列上用函数或计算。
2.  **前导模糊查询**: `WHERE name LIKE '%张'` -> `%` 开头，索引无法确定起始点。
3.  **类型不匹配**: `phone` 是字符串，但用 `phone = 138...` 查询 -> 发生隐式类型转换。
4.  **违反最左前缀**: 联合索引 `(a,b,c)`，查询 `WHERE b=1` -> 查询没有从 `a` 开始。
5.  **范围查询右侧失效**: 联合索引 `(a,b,c)`，`WHERE a=1 AND b>2 AND c=3` -> `c` 列索引失效。
6.  **OR连接非索引列**: `WHERE a=1 OR b=2` (`b` 无索引) -> 优化器可能放弃索引，转为全表扫描。
7.  **不等查询**: `!=`, `<>`, `NOT IN` -> 通常不走索引，但取决于数据分布。
8.  **IS NULL / IS NOT NULL**: -> 通常不走索引，但取决于数据分布和版本。
9.  **选择性过低**: 索引字段区分度小（如性别），优化器可能认为全表扫描更快。
10. **优化器认为全表更快**: 数据量很小或大部分数据都满足条件时。

---

### 核心记忆点

**"模型算糊了，类型范围空，或选择放弃"**

-   **模**型: **模**糊查询 (`LIKE '%...'`)。
-   **算**: 索引列上**算**数、函数、表达式。
-   **糊**了: 违反最左前缀原则，顺序搞**糊**了。
-   **类型**: **类型**不匹配，隐式转换。
-   **范围**: **范围**查询 (`>`,`<`) 右边的列失效。
-   **空**: **IS NULL** / **IS NOT NULL** / **!=** / **NOT IN**。
-   **或**: **OR** 连接的条件中，有一个没索引。
-   **选择放弃**: 优化器根据**选择**性（区分度）和数据量**放弃**索引。

> **终极武器**: `EXPLAIN`！面试时可以说"具体情况需要用 `EXPLAIN` 分析执行计划来判断"。