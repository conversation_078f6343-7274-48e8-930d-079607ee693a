## MySQL事务特性

### ACID特性

#### 1. 原子性（Atomicity）
- **定义**：事务是不可分割的操作单元，要么全部成功，要么全部回滚
- **实现**：Undo Log记录回滚信息

#### 2. 一致性（Consistency）
- **定义**：事务执行前后数据库状态保持一致，满足完整性约束
- **实现**：约束检查、触发器、应用层逻辑

#### 3. 隔离性（Isolation）
- **定义**：并发事务相互隔离，不互相干扰
- **实现**：锁机制 + MVCC

#### 4. 持久性（Durability）
- **定义**：事务提交后永久保存，系统崩溃不丢失
- **实现**：Redo Log记录已提交事务

### 四种隔离级别

| 隔离级别 | 脏读 | 不可重复读 | 幻读 |
|---------|------|-----------|------|
| 读未提交 | ✓ | ✓ | ✓ |
| 读已提交 | ✗ | ✓ | ✓ |
| 可重复读 | ✗ | ✗ | ✓ |
| 可串行化 | ✗ | ✗ | ✗ |

### 并发问题

- **脏读**：读取未提交数据
- **不可重复读**：同一事务多次读取结果不同
- **幻读**：同一事务多次查询行数不同

### MySQL解决幻读

**可重复读级别下使用Next-Key Lock**：
- 记录锁：锁定具体记录
- 间隙锁：锁定记录间的间隙
- 临键锁：记录锁+间隙锁，防止插入新记录

### 面试重点

1. **默认隔离级别**：可重复读（Repeatable Read）
2. **MVCC原理**：多版本并发控制，读不加锁
3. **锁的分类**：共享锁、排他锁、意向锁
4. **如何选择隔离级别**：根据业务对一致性和性能的要求