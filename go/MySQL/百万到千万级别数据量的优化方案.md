# MySQL 千万级数据量优化方案（面试速查版）

处理千万级数据量时，核心优化策略包括架构、索引、查询和配置。

### **1. 数据库架构**
- **分库分表**:
    - **垂直切分**: 按业务或字段拆分表，减少单表宽度。
    - **水平切分**: 按数据行（如ID范围、时间）拆分到多表，管理大数据量。
- **数据归档**: 将不常用的历史数据移至归档表，减小主表大小。

### **2. 索引优化**
- **合理创建索引**:
    - 必须有**主键索引**。
    - 根据 `WHERE` 和 `JOIN` 条件建**辅助索引**。
    - 使用**覆盖索引**避免回表，提升查询效率。
- **索引维护**:
    - 使用 `EXPLAIN` 分析查询计划，找出性能瓶颈。
    - 定期 `OPTIMIZE TABLE` 整理索引碎片。

### **3. SQL与查询优化**
- **SQL语句优化**:
    - 使用 `EXPLAIN` 确保查询走索引，避免**全表扫描**。
    - 尽可能用 `JOIN` 代替子查询。
    - 选择最合适的数据类型。
- **分批处理**:
    - **分页查询**: 使用 `LIMIT` 和 `OFFSET`（或更优化的键集分页）分批拉取数据。
    - **批量操作**: 将 `INSERT`, `UPDATE`, `DELETE` 分批执行，减小事务大小和锁范围。

### **4. 数据库配置**
- **内存配置**: 合理配置 `innodb_buffer_pool_size` (通常为总内存的60%-80%)。
- **连接数配置**: 调整 `max_connections` 以适应并发需求。
- **超时设置**: 调整 `wait_timeout` 和 `interactive_timeout` 管理连接生命周期。
- **注意**: MySQL 8.0 已移除查询缓存 (`query_cache`)。

### **5. 读写分离与集群**
- **主从复制**: 读写分离，将读流量分发到从库，降低主库负载。
- **分布式数据库/集群**: 考虑使用 MySQL Cluster 或其他分布式方案，应对更高的数据量和并发。

### **6. 数据库维护**
- **定期备份与恢复**: 制定全量和增量备份策略，并定期演练恢复流程。
- **性能监控**:
    - 使用监控工具（如 Prometheus, PMM）。
    - 定期分析**慢查询日志**，定位并优化慢SQL。