# MySQL性能优化 (面试速查版)

### 1. SQL与查询优化

- **查询原则**:
  - `SELECT` 具体字段，避免 `SELECT *`。
  - `LIMIT` 分页查询，避免全表扫描。
  - `WHERE` 条件中，高选择性的放前面，避免在列上使用函数。
  - `JOIN` 关联字段建索引，优先用 `INNER JOIN`，小表驱动大表。
- **索引优化**:
  - **核心原则**: 为经常查询、排序、分组的列创建索引。
  - **覆盖索引**: 索引包含所有查询字段，避免回表。
  - **最左前缀**: 联合索引需遵循最左前缀匹配原则。
  - **清理**: 定期清理冗余和未使用的索引。
  - **索引失效场景**: `LIKE` 以 `%` 开头、使用函数/计算、`OR` 条件部分列无索引。

### 2. 数据库设计优化

- **数据类型**: 选择最小最合适的数据类型（如 `INT` vs `BIGINT`），尽量 `NOT NULL`。
- **表结构**:
  - **范式化 (3NF)**: 减少数据冗余。
  - **反范式化**: 为性能适当冗余字段，减少 `JOIN`。
- **分表**:
  - **垂直分表**: 将大字段或不常用字段拆分到新表。
  - **水平分表**: 按规则（如 `user_id`取模）将数据分到多个表。

### 3. 系统与配置优化

- **关键配置 (`my.cnf`)**:
  - `innodb_buffer_pool_size`: 最重要的参数，通常设为物理内存的 70-80%，缓存数据和索引。
  - `max_connections`: 最大连接数，根据应用并发量调整。
  - `binlog_format`: 推荐 `ROW` 格式，主从复制更安全。
  - `slow_query_log`: 开启慢查询日志，设置 `long_query_time` (如 1s)。
- **硬件**:
  - **磁盘**: 使用 SSD 提升 IOPS。
  - **内存**: 增加物理内存，以增大 `innodb_buffer_pool_size`。
  - **CPU**: 多核 CPU 支持更高并发。

### 4. 诊断与监控

- **`EXPLAIN`**: 分析查询计划，重点关注 `type` (目标是 `ref`, `range`, `index`)、`key`、`rows`、`Extra` (避免 `Using filesort`, `Using temporary`)。
- **慢查询日志**: 定位性能低下的 SQL。
- **`SHOW PROCESSLIST`**: 查看当前连接状态，排查死锁或长查询。
- **`SHOW ENGINE INNODB STATUS`**: 查看 InnoDB 内部状态，如锁等待。

### 5. 核心面试问题

- **Q: 性能瓶颈在哪？**
  - **A:** CPU (高计算)、内存 (Buffer Pool小)、磁盘 I/O (慢盘、无索引)、网络带宽、锁竞争 (高并发更新)、SQL 查询效率低。

- **Q: 如何定位慢查询？**
  - **A:** 1. 开启慢查询日志。 2. 使用 `EXPLAIN` 分析 SQL 执行计划。 3. 使用性能监控工具 (如 Prometheus)。

- **Q: `InnoDB Buffer Pool` 的作用？**
  - **A:** 缓存数据页和索引页，是 InnoDB 的核心组件。通过内存读取代替磁盘 I/O，极大提升性能。

- **Q: 何时需要分库分表？**
  - **A:** 单表数据量过大 (如超千万行)、并发量高导致锁竞争激烈、数据增长快超出单机存储容量时。
