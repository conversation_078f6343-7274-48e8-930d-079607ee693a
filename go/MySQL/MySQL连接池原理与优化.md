## MySQL 连接池核心知识

### 1. 为什么需要连接池？
- **核心思想**: 复用数据库连接，避免频繁创建和销毁，从而提高性能。
- **优点**:
    - **资源复用**: 减少连接建立和关闭的开销。
    - **性能提升**: 快速获取可用连接。
    - **并发控制**: 限制最大连接数，保护数据库。

### 2. 工作原理
1.  **初始化**: 服务启动时，创建指定数量的初始连接。
2.  **借用**: 应用向连接池请求连接。
    - 池中有空闲连接 -> 直接返回。
    - 无空闲连接 & 未达最大连接数 -> 创建新连接。
    - 已达最大连接数 -> 请求等待或超时失败。
3.  **使用**: 应用执行SQL操作。
4.  **归还**: 应用将连接放回池中，而不是关闭它。
5.  **维护**: 连接池定期检查、回收无效/空闲过久的连接。

### 3. 关键配置参数
-   `minIdle`: 最小空闲连接数。连接池中至少要保留的空闲连接数量。
-   `maxActive` / `maximum-pool-size`: 最大连接数。池中允许的最多连接总数。
-   `initialSize`: 初始连接数。启动时建立的连接数。
-   `maxWait`: 最大等待时间。当池中无连接时，请求等待的最长时间。
-   `idle-timeout`: 空闲连接超时时间。连接空闲多久后被回收。
-   `validationQuery`: 连接验证查询。用于检查连接是否有效的SQL（如 `SELECT 1`）。

### 4. 连接池大小如何设置？
-   **经验公式**: `连接池大小 = ((核心数 * 2) + 有效磁盘数)`
-   **业务考量**: 根据应用的并发量（QPS）、平均响应时间和数据库处理能力综合评估。
-   **目标**: 找到一个既能满足并发需求，又不会耗尽应用或数据库资源的平衡点。

### 5. 常见问题及解决方案
-   **连接泄漏 (Connection Leak)**
    - **原因**: 连接使用后未被归还给连接池。
    - **解决**:
        - 使用 `try-with-resources` (Java) 或 `defer` (Go) 确保连接归还。
        - 开启连接池的泄漏检测功能（如 Druid 的 `removeAbandoned`）。
-   **连接超时 (Connection Timeout)**
    - **原因**: 在 `maxWait` 时间内无法从池中获取连接。通常是连接池太小或查询太慢。
    - **解决**:
        - 适当调大 `maxActive`。
        - 优化慢查询SQL。
        - 检查应用是否有长时间占用连接未释放的业务逻辑。
-   **连接失效 (Invalid Connection)**
    - **原因**: 连接长时间空闲，被数据库或防火墙单方面断开。
    - **解决**:
        - 配置 `validationQuery`，在借用连接前进行有效性检查。
        - 缩短 `idle-timeout`，让连接池主动回收空闲连接。
        - 开启 `testOnBorrow` 等选项。

### 6. Go语言 `database/sql` 连接池
Go的 `database/sql` 包内置了连接池，无需引入第三方库。
关键设置函数：
- `db.SetMaxOpenConns(n)`: 设置最大打开的连接数。
- `db.SetMaxIdleConns(n)`: 设置最大空闲连接数。
- `db.SetConnMaxLifetime(d)`: 设置连接可被复用的最大时间。

### 7. 面试核心问题
- **为什么用？** -> 复用、性能、并发控制。
- **怎么配？** -> 理解 `maxActive`, `minIdle` 等核心参数，知道如何估算大小。
- **出问题怎么办？** -> 掌握连接泄漏、超时、失效的原因和解决方法。
- **HikariCP为什么快？** -> 实现精简、字节码级别优化、使用 `FastList` 替代 `LinkedList` 等。
