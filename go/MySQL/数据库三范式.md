# 数据库三范式精简版

### 核心思想
数据库设计的三范式是用于规范化数据表，旨在**减少数据冗余**、**避免数据异常**和**提高数据一致性**。

### 1. 第一范式 (1NF)
- **核心要求**：**字段原子性**，即表中的每一列都是不可再分的最小数据单元。
- **通俗解释**：一个字段只能存一个值，不能把多个值塞在一个字段里（比如 `'蓝色,红色'`）。

### 2. 第二范式 (2NF)
- **核心要求**：在满足1NF的基础上，消除**部分依赖**。表中所有非主键字段必须**完全依赖**于整个主键，而不是主键的一部分。
- **适用场景**：通常针对**联合主键**。
- **通俗解释**：如果主键是 `(学号, 课程号)`，那么 `学生姓名` 就不应该在成绩表里，因为它只依赖于 `学号`。

### 3. 第三范式 (3NF)
- **核心要求**：在满足2NF的基础上，消除**传递依赖**。表中所有非主键字段必须**直接依赖**于主键，不能依赖于其他非主键字段。
- **通俗解释**：`班级名` 依赖于 `班级ID`，而 `班级ID` 依赖于 `学号`。`班级名` 就是通过 `班级ID` 传递依赖于 `学号`，这不符合3NF。应该把班级信息拆分成独立的表。

### 快速记忆
- **1NF**：字段不可分。
- **2NF**：消除部分依赖（针对联合主键）。
- **3NF**：消除传递依赖。