# MySQL存储引擎速查

> 为面试场景优化，突出核心差异和关键考点。

### 核心对比 (InnoDB vs MyISAM vs Memory)

| 特性 | InnoDB | MyISAM | Memory |
|---|---|---|---|
| **核心优势** | **事务安全、高并发** | **快速读取、简单** | **极速访问** |
| **事务** | ✅ (ACID) | ❌ | ❌ |
| **锁粒度** | **行级锁** | 表级锁 | 表级锁 |
| **外键** | ✅ | ❌ | ❌ |
| **MVCC** | ✅ | ❌ | ❌ |
| **崩溃恢复**| ✅ | ❌ (需手动修复) | ❌ (数据丢失) |
| **`COUNT(*)`**| 慢 (全表扫描) | **快 (内置计数器)** | 慢 (全表扫描) |
| **适用场景**| OLTP, 高并发业务 | 读密集, 数据仓库 | 缓存, 临时表 |

### 各引擎总结

- **InnoDB**: 默认引擎，功能全面，支持事务和行级锁，适合绝大多数需要高并发和数据一致性的场景 (OLTP)。
- **MyISAM**: 读性能优异，但表级锁限制并发写入。适合读多写少、对事务要求不高的场景，如报表和日志。`COUNT(*)` 速度极快。
- **Memory**: 数据存放在内存中，速度飞快，但服务重启后数据会丢失。适合用作临时表或缓存。
- **Archive**: 归档引擎，压缩比高，只支持插入和查询，适合存储历史日志数据。

### 核心面试问答

**Q: InnoDB 和 MyISAM 的核心区别？**
A:
- **事务与恢复**: InnoDB 支持 ACID 事务和崩溃恢复；MyISAM 不支持。
- **锁机制**: InnoDB 支持**行级锁**，并发性能高；MyISAM 是**表级锁**，并发性能差。
- **外键**: InnoDB 支持外键，保证数据完整性；MyISAM 不支持。
- **MVCC**: InnoDB 支持多版本并发控制，实现非阻塞读；MyISAM 不支持。

**Q: 为什么 MyISAM 的 `COUNT(*)` 比 InnoDB 快？**
A: MyISAM 将表的总行数缓存在一个计数器中，读取速度快。InnoDB 因为支持事务和 MVCC，需要实时扫描来计算当前事务可见的行数，确保数据一致性。

**Q: 什么场景下应该选择 InnoDB？**
A: 绝大多数业务场景都应首选 InnoDB。特别是涉及支付、交易等需要**事务**保证、或者有大量并发写入和更新的**高并发**场景。

### 实用SQL

```sql
-- 查看表状态（包括引擎），\G为了更佳的可读性
SHOW TABLE STATUS WHERE Name = 'your_table_name'\G

-- 修改表引擎
ALTER TABLE your_table_name ENGINE=InnoDB;
```
