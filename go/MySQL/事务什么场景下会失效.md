### 事务在哪些场景下会失效？

*   **隐式提交**: 执行数据定义语言 (DDL) 语句，如 `CREATE`, `ALTER`, `DROP` 表或索引时，会自动提交当前事务。
*   **死锁**: 多个事务循环等待对方持有的锁，数据库会选择一个事务回滚以解除死锁。
*   **并发冲突**: 隔离级别过低（如"读未提交"），导致脏读、不可重复读或幻读，破坏了事务的一致性。
*   **锁等待超时**: 事务等待锁的时间超过系统设定的 `innodb_lock_wait_timeout`，会被自动终止。
*   **存储引擎不支持**: 使用了不支持事务的存储引擎（例如 MySQL 的 MyISAM）。
*   **系统故障**: 数据库崩溃、服务器宕机或网络中断，导致事务未能完整执行。
*   **违反约束**: 操作违反了主键、外键、唯一性等数据库约束，触发事务回滚。
*   **手动回滚**: 代码中显式调用 `ROLLBACK` 来中断事务。

### 总结

事务失效的原因主要包括隐式提交、死锁、并发冲突、锁等待超时、存储引擎限制、系统故障、约束条件违反以及手动中断等。为了避免事务失效，开发者需要合理设计事务逻辑，选择合适的隔离级别，并处理好异常情况。