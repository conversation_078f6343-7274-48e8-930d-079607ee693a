# 分表核心知识点

### 1. 垂直分表 (按列拆分)

- **是什么**：将一个宽表拆分成多个窄表。例如，用户基础信息表和用户扩展信息表。
- **优点**：
    - 减少单次查询的I/O，提升性能。
    - 按业务功能拆分，便于维护。
- **缺点**：
    - 查询时可能需要 `JOIN` 多个表。
    - 事务管理变复杂。
- **场景**：
    - 表中存在冷热数据列。
    - 表字段过多。

---

### 2. 水平分表 (按行拆分)

- **是什么**：将一个大表的数据按规则（如 `id` 范围、`hash`）分散到多个结构相同的表中。
- **优点**：
    - 解决单表数据量过大和高并发的性能瓶颈。
    - 易于扩展。
- **缺点**：
    - 引入分片键和路由逻辑，系统复杂。
    - 跨分片查询和事务处理困难。
- **场景**：
    - 单表数据量巨大（亿级别以上）。
    - 高并发读写。

---

### 3. 跨分片查询解决方案

- **应用层聚合**：代码中查询各分片后合并结果。简单但对业务有侵入。
- **数据库中间件**：使用 Sharding-Sphere、MyCat 等。对业务透明，但增加运维复杂度。
- **数据冗余**：空间换时间，在表中冗余常用字段，避免 `JOIN`。
- **全局表**：在每个分片库中都存一份小表（如配置表），避免跨库查询。