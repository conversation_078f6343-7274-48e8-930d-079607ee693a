# MySQL 高性能索引核心策略

## 关键概念

- **B+Tree 特点**:
  - **内节点**: 只存键(Key)，不存数据。
  - **叶子节点**: 存数据（或数据地址），并通过链表连接，利于范围查询。
  - **平衡性**: 叶子节点深度相同，查询性能稳定在 O(log n)。

- **存储引擎差异 (InnoDB vs MyISAM)**:
  - **InnoDB (聚集索引)**: 主键索引的叶节点存**整行数据**。辅助索引的叶节点存**主键值**，查询时需要**回表**（再查一次主键索引）。
  - **MyISAM (非聚集索引)**: 索引叶节点存数据的**物理地址**。索引和数据文件分离。

## 核心优化策略

1.  **最左前缀原则**:
    - 联合索引 `(a, b, c)`，查询必须从 `a` 开始，且不能跳过中间列。
    - **生效**: `WHERE a=1`, `WHERE a=1 AND b=2`, `WHERE a=1 AND b=2 AND c=3`
    - **失效**: `WHERE b=2`, `WHERE a=1 AND c=3` (只有 a 生效)

2.  **覆盖索引**:
    - **定义**: 查询的字段 (`SELECT`部分) 全部在索引中，无需回表。
    - **优点**: 避免回表，极大提升查询性能。是索引优化的重要手段。

3.  **前缀索引**:
    - **场景**: 对长字符串字段 (如URL) 创建索引。
    - **方法**: `CREATE INDEX idx_name ON table(column(prefix_length));`
    - **权衡**: 需平衡前缀长度和选择性。

## 索引失效场景及优化

| 场景 | 失效示例 | 优化建议 |
| :--- | :--- | :--- |
| **对列函数/计算** | `WHERE YEAR(date) = 2024` | 对值进行计算: `WHERE date >= '2024-01-01'` |
| **隐式类型转换** | `WHERE phone = 123` (phone是varchar) | 保证类型一致: `WHERE phone = '123'` |
| **开头通配符** | `WHERE name LIKE '%John'` | 避免在开头用`%`: `WHERE name LIKE 'John%'` |
| **OR 条件** | `WHERE a=1 OR b=2` (b无索引) | 为 `OR` 两边的列都加上索引 |
| **`NOT` / `!=`** | `WHERE status != 'deleted'` | 改为 `IN` 或 `UNION` |

## 索引设计原则

- **高选择性优先**: 优先为区分度高的列建索引 (`count(distinct col) / count(*)` 越接近1越好)。
- **查询驱动**: 根据 `WHERE`, `ORDER BY`, `GROUP BY` 的列设计索引。
- **联合索引顺序**: 高频和高选择性列在前，范围查询 (`>`, `<`) 列在后。
- **适度索引**: 索引会增加写成本和存储开销，避免冗余。

## 面试速查

- **Q: 何时不建索引?**
  - **A:** 小表、写操作远多于读、选择性低的列 (如性别)。

- **Q: 如何判断索引有效?**
  - **A:** `EXPLAIN` 看执行计划。关注 `type` (避免`ALL`)、`key` (是否用了索引)、`Extra` (是否`Using index`)。

- **Q: 联合索引顺序?**
  - **A:** 遵循最左前缀，把选择性高的列放最左边，范围查询的列放最后。