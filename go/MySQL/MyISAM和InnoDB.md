# MyISAM vs InnoDB 快速对比

`MyISAM` 和 `InnoDB` 是 MySQL 中两种主要的存储引擎。以下是它们的核心区别，以便在面试中快速查阅。

### 核心区别对比表

| 特性 (Feature) | MyISAM | InnoDB |
| :--- | :--- | :--- |
| **锁机制** | **表锁**：写操作锁定整个表，并发性差。 | **行锁**：只锁定操作的行，并发性高。 |
| **事务 (ACID)** | **不支持** | **支持**：保证数据操作的原子性、一致性、隔离性和持久性。 |
| **外键约束** | **不支持** | **支持**：保证数据完整性。 |
| **索引结构** | **非聚集索引**：索引和数据分开存储。 | **聚集索引**：主键索引和数据存储在一起，查询效率高。 |
| **并发控制** | - | **支持MVCC** (多版本并发控制)，减少读写冲突。 |
| **崩溃恢复** | **不支持** | **支持**：通过日志实现崩溃后的自动恢复。 |
| **存储文件** | 数据（`.MYD`）和索引（`.MYI`）分离。 | 数据和索引存储在同一个表空间文件（`.ibd`）中。 |
| **适用场景** | 读密集、对事务要求不高的应用（如数据仓库）。 | 写密集、需要事务和高并发的应用（如在线交易系统）。 |

### 总结

-   **追求性能和低事务需求** -> **MyISAM**
-   **追求数据一致性、高并发和事务支持** -> **InnoDB** (MySQL 5.5+ 默认)