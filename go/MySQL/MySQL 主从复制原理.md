# MySQL 主从复制核心面试点

### 核心原理
主库将数据变更写入 `Binary Log`。从库的 `I/O 线程` 拉取主库的 `Binary Log` 并写入本地的 `Relay Log`，然后 `SQL 线程` 读取 `Relay Log` 并重放 SQL，实现数据同步。

**流程**: `主库 Binlog` -> `从库 I/O 线程` -> `从库 Relay Log` -> `从库 SQL 线程`

---

### 复制模式

| 模式 | 特点 | 优缺点 |
|---|---|---|
| **异步 (Async)** | 主库不等待从库确认 | **优点**: 性能高<br>**缺点**: 可能丢数据 |
| **半同步 (Semi-Sync)** | 主库等待至少一个从库确认 | **优点**: 数据安全性高<br>**缺点**: 性能有损耗 |
| **全同步 (Sync)** | 主库等待所有从库确认 | **优点**: 数据最安全<br>**缺点**: 性能差，不常用 |

*注：MySQL 5.7+ 增强了半同步复制，减少了数据丢失概率。*

---

### 日志格式 (Binlog Format)

- **Statement (SBR)**: 记录SQL原文。**缺点**: 可能导致不一致（如 `UUID()`, `NOW()`）。
- **Row (RBR)**: 记录行数据变更。**优点**: 最安全，**缺点**: 日志量大。
- **Mixed (MBR)**: 混合模式，自动选择。**MySQL 默认**。

---

### 核心问题与解决方案

- **复制延迟 (Replication Lag)**
  - **监控**: `SHOW SLAVE STATUS\G` 查看 `Seconds_Behind_Master`。
  - **原因**: 主库写入压力大、从库性能瓶颈、网络延迟。
  - **优化**:
    - 从库硬件升级 (SSD, CPU)。
    - 开启并行复制 (`slave_parallel_workers > 0`)。
    - 读写分离，降低主库负载。

- **主库故障转移 (Failover)**
  - **手动**: 提升一个从库为新主库，其他从库指向新主库。
  - **自动**: 使用高可用方案，如 MHA, Keepalived, 或 InnoDB Cluster。

- **数据一致性**
  - 使用半同步复制提高保障。
  - 定期使用 `pt-table-checksum` 等工具进行数据校验。

---

### 优势

- **读写分离**: 提高并发和查询性能。
- **数据备份与容灾**: 提供热备。
- **负载均衡**: 分担数据库负载。