## 联合索引与最左前缀原则 (面试精简版)

### 核心概念

- **联合索引**: 对多个字段同时建立的B+树索引。
- **最左前缀原则**: 查询必须从索引的**最左边列**开始，并且**不能跳过**中间的列。
- **存储原理**: 数据按 `(a, b, c)` 的顺序，先按`a`排序，`a`相同时再按`b`排序，以此类推。

### 索引匹配规则

假设索引为 `(a, b, c)`

#### ✓ 有效情况 (能用上索引)
- `WHERE a = ?`
- `WHERE a = ? AND b = ?`
- `WHERE a = ? AND b = ? AND c = ?`
- `WHERE a = ? AND c = ?`  **(注意: 只用到了 a 的索引)**

#### ✗ 失效情况 (完全用不上索引)
- `WHERE b = ?`
- `WHERE c = ?`
- `WHERE b = ? AND c = ?`

### 范围查询的影响

- 范围查询 (`>` , `<` , `BETWEEN` , `LIKE 'xx%'`) 会**终止**后续字段的索引匹配。
- **示例**: `WHERE a = 'A' AND b > 20 AND c = 'C'`
  - **结果**: 只有 `a` 和 `b` 的索引生效，`c` 的索引会失效。
- **原因**: `b` 字段范围查询后，结果集中的 `c` 字段是无序的，无法再使用索引。

### 索引设计黄金原则

1.  **区分度优先**: 将选择性高 (重复值少) 的字段放在最左边。
2.  **等值优先**: 将使用等值查询 (`=`, `IN`) 的字段放在范围查询字段的前面。
3.  **频率优先**: 将业务查询最频繁的字段组合放在前面。
4.  **覆盖索引**: 尽量设计索引让查询的字段都包含在内，避免回表，大幅提升性能。

### 重要优化技术

- **覆盖索引 (Covering Index)**
  - **定义**: `SELECT` 的字段恰好是索引的一部分，此时无需回表查询数据行。
  - **示例**: 索引 `(name, age)`，查询 `SELECT name, age FROM ... WHERE name = 'Tom'` 就是覆盖索引。

- **索引下推 (Index Condition Pushdown - ICP)**
  - **作用**: MySQL 5.6+ 新特性。在索引层面就对数据进行过滤，而不是拿到存储引擎层后才过滤，从而**减少回表次数**。
  - **示例**: 索引 `(name, age)`，查询 `WHERE name LIKE 'T%' AND age = 10`。
    - **无 ICP**: 引擎会找出所有`name`以`T`开头的记录，然后回表，再用`age=10`条件进行过滤。
    - **有 ICP**: 在索引扫描时，就直接排除掉`age != 10`的记录，减少了回表IO。

### 面试核心要点

1.  **最左前缀原理**: 能解释B+树中多列的排序方式。
2.  **范围查询截断**: 能说明白为什么范围查询后的字段索引会失效。
3.  **字段顺序设计**: 如何根据区分度、查询类型（等值/范围）、查询频率来设计索引顺序。
4.  **覆盖索引**: 知道它如何避免回表，是性能优化的利器。
5.  **索引下推**: 明白ICP是如何减少回表IO的。
