## MVCC原理

**一句话总结**: 通过数据多版本实现并发控制，让读写操作互不阻塞。

**核心原理**:
- **版本链**: `InnoDB`为每行数据添加`DB_TRX_ID`（事务ID）和`DB_ROLL_PTR`（回滚指针），`DB_ROLL_PTR`指向`Undo Log`中的历史版本，形成版本链。
- **ReadView**: 事务开启时，创建一个`ReadView`（读视图），记录当前活跃的事务ID。
- **可见性判断**: 读取数据时，根据`ReadView`和数据版本的`DB_TRX_ID`判断可见性，从版本链中找到第一个可见的版本。

**两种读**:
- **快照读 (Snapshot Read)**: 普通`SELECT`，读取历史快照，不加锁。
- **当前读 (Current Read)**: `SELECT...FOR UPDATE/LOCK IN SHARE MODE`, `UPDATE`, `DELETE`，读取最新版本，加锁。

**优缺点**:
- **优点**: 读写不阻塞，性能高。
- **缺点**: 增加存储开销，需要清理旧版本。