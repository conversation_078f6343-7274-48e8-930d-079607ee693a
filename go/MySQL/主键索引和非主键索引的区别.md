# 主键索引 vs. 非主键索引 (MySQL)

在MySQL中，主键索引和非主键索引（也称普通索引或二级索引）是提高查询性能的关键，但它们在实现和用途上有本质区别。

### 核心区别对比

| 特性 | 主键索引 (Primary Key) | 非主键索引 (Secondary/Normal Index) |
| :--- | :--- | :--- |
| **定义和数量** | 唯一标识表中每一行。每个表**只能有一个**。 | 用于加速查询。一个表**可以有多个**。 |
| **唯一性** | 列值**必须唯一**，且不能为 `NULL`。 | 列值**可以重复**，也可以为 `NULL`。 |
| **存储 (InnoDB)** | **聚集索引 (Clustered Index)**。表数据按主键顺序物理存储，索引和数据在一起。 | **非聚集索引 (Non-Clustered Index)**。索引的叶子节点存储的是**主键值**。 |
| **查询方式** | 定位到主键后，直接获取整行数据。 | 需要**回表**：先通过索引找到对应的主键，再用主键去聚集索引中查找数据。 |
| **创建方式** | `PRIMARY KEY` 约束，通常在建表时指定。 | `INDEX` 或 `KEY` 关键字，可随时添加。 |
| **适用场景** | 唯一标识符，如用户ID、订单号。 | 频繁出现在 `WHERE`, `JOIN`, `ORDER BY` 子句中的列。 |

### 总结

- **主键索引**是数据表的"目录"，决定了数据的物理排列方式。查找速度快，因为数据就在索引旁边。
- **非主键索引**是辅助"书签"，通过记录"页码"（主键）来帮助你快速定位。查找时多一步"回表"操作，但极大地提升了非主键列的查询效率。