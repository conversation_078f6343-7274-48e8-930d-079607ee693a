# 当前读 vs 快照读 (面试速查)

### 核心区别

| 特性     | 当前读 (Current Read)                        | 快照读 (Snapshot Read)                   |
|----------|----------------------------------------------|------------------------------------------|
| **数据版本** | 读取**最新已提交**的版本                     | 读取事务开始时的**数据快照** (通过 MVCC)      |
| **是否加锁**   | **是** (共享锁或排它锁)                      | **否**                                   |
| **典型场景** | `UPDATE`, `DELETE`, `INSERT`<br/>`SELECT ... FOR UPDATE`<br/>`SELECT ... LOCK IN SHARE MODE` | 普通的 `SELECT` 语句                     |
| **一致性** | 强一致性（读取最新数据）           | 最终一致性（可能读到旧数据，但无锁并发好）             |


### 核心概念

*   **当前读 (Current Read)**
    *   **定义**: 读取数据库**最新**的已提交数据，并对读取的记录**加锁**，以防止其他事务并发修改。
    *   **目的**: 保证数据修改时的一致性，避免在"读-改-写"过程中数据被意外篡改。

*   **快照读 (Snapshot Read)**
    *   **定义**: 基于**多版本并发控制 (MVCC)**，读取的是事务开始时生成的数据**快照**，**不加锁**。
    *   **目的**: 在不加锁的情况下，为事务提供一个一致性的数据视图，提高并发读取性能。在可重复读隔离级别下，可以避免幻读。

