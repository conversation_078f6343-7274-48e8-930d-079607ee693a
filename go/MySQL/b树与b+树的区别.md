## B树 vs B+树：核心区别与面试要点

### 核心对比

| 特性 | B树 | B+树 |
| :--- | :--- | :--- |
| **数据存储** | 内部节点和叶子节点都存数据 | 只有叶子节点存数据，内部节点存索引 |
| **查询性能** | 单点查询可能更快，但不稳定 | 所有查询都必须到达叶子节点，性能稳定 |
| **范围查询** | 效率低（需要复杂的中序遍历） | 效率高（叶子节点形成有序链表，支持顺序扫描） |
| **树的高度** | 更高（内部节点大，分支少） | 更矮（内部节点小，分支多），I/O次数更少 |
| **空间利用率**| 内部节点存储数据，空间利用率较低 | 内部节点不存数据，可以缓存更多索引，缓存命中率高 |
| **插入/删除**| 复杂，可能涉及内部节点分裂 | 相对简单，通常只影响叶子节点 |

### 为什么MySQL等数据库更青睐B+树？

1.  **I/O效率更高**：B+树更矮胖，意味着查询时磁盘I/O次数更少。
2.  **范围查询是核心优势**：叶子节点的有序链表结构，完美契合SQL中的`ORDER BY`、`BETWEEN`等范围查询场景。
3.  **缓存更友好**：内部节点不带数据，体积小，内存可以缓存更多的索引节点，大大提高了缓存命中率。
4.  **查询性能稳定**：任何查询都需要走到叶子节点，IO代价和查询复杂度稳定。

### 面试重点

- B+树为什么比B树更适合做数据库索引？
- 两种树结构在什么场景下各有优势？
- B+树的叶子节点链表有什么作用？