### 核心思想对比

- **悲观锁 (Pessimistic Locking)**: **假定冲突，先锁后操作**。认为数据在操作时总会被修改，所以在访问数据前先加锁，阻止其他事务的干扰。
- **乐观锁 (Optimistic Locking)**: **假定无冲突，操作时不加锁，提交时验证**。认为数据在操作时不会被修改，提交更新时才去验证数据是否被篡改。

### 悲观锁 (Pessimistic Locking)

- **实现方式**: 依赖数据库的锁机制。
  - **共享锁 (Share Lock)**: `SELECT ... LOCK IN SHARE MODE`
  - **排他锁 (Exclusive Lock)**: `SELECT ... FOR UPDATE`
- **优点**:
  - **数据强一致性**: 操作期间数据被锁定，非常安全。
- **缺点**:
  - **并发性能差**: 锁竞争导致阻塞，降低吞吐量。
  - **可能导致死锁**。
- **适用场景**:
  - **写多读少**，数据冲突概率高的场景（如：银行账户、商品秒杀）。

### 乐观锁 (Optimistic Locking)

- **实现方式**: 通常由应用层实现。
  - **版本号 (Versioning)**: 在表中增加 `version` 字段，更新时 `version+1` 并验证 `version` 值。
  - **时间戳 (Timestamp)**: 类似版本号，通过时间戳字段判断。
- **优点**:
  - **并发性能好**: 无锁操作，省去了锁的开销。
  - **不会产生死锁**。
- **缺点**:
  - **ABA问题**: 数据从 A->B->A，版本号/时间戳无法识别此变化。可通过CAS（比较并交换）解决。
  - **冲突成功率低**: 冲突严重时，反复重试会消耗CPU，降低性能。
- **适用场景**:
  - **读多写少**，数据冲突概率低的场景（如：更新用户信息、查看商品库存）。

### 总结与对比

| 特性 | 悲观锁 | 乐观锁 |
| :--- | :--- | :--- |
| **核心思想** | 保守，总假设最坏情况 | 乐观，总假设最好情况 |
| **加锁方式** | 数据库锁（物理锁） | 应用层实现（逻辑锁） |
| **数据一致性** | 强一致性 | 最终一致性（通过校验） |
| **并发性能** | 差 | 好 |
| **死锁** | 可能 | 不会 |
| **适用场景** | 写多、冲突严重 | 读多、冲突少 |