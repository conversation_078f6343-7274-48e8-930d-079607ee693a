# 分库分表后单表查询慢的优化策略

当分库分表后，单个表依然存在百万甚至千万级数据，导致查询缓慢时，可以从以下几个核心层面进行优化，使其更适合面试时快速回顾。

### 一、 索引与SQL优化 (I/O效率)

这是最直接、成本最低的优化方式。

- **创建高效索引**:
  - **覆盖索引**：最优情况。索引直接包含了所有查询字段，无需回表，I/O开销最小。
  - **复合索引**：遵循最左前缀原则。将过滤性最强的字段放在最前面。
  - **索引下推 (Index Condition Pushdown)**：MySQL 5.6+ 自动优化，在索引层面过滤数据，减少回表次数。

- **SQL查询重写**:
  - **避免 `SELECT *`**: 只查询必要的列，减少网络传输和I/O。
  - **优化分页查询**: 避免大偏移量 `LIMIT offset, N`。推荐使用 `WHERE id > last_id ORDER BY id LIMIT N` 的方式。
  - **避免索引失效**: 不在索引列上做函数运算、类型转换等。
  - **用 `UNION ALL` 代替 `OR`**: 在某些 `OR` 会导致索引失效的场景下。

### 二、 架构层优化 (减少单表数据量)

当SQL优化达到瓶颈时，需要从架构层面解决问题。

- **读写分离**: 主库负责写操作，从库集群负责读操作，分散查询压力。
- **数据分区 (Partitioning)**: 在数据库内部对单个大表进行分区。查询时数据库只需扫描特定分区，而不是整张表。
  - **Range分区**: 适合按时间或连续ID范围查询的场景。
  - **Hash分区**: 适合数据均匀分布，无明显热点key的场景。
- **数据归档**: 将历史数据、冷数据定期迁移到成本更低的归档库中，保持线上主表"瘦身"。
- **二次分片 (Re-sharding)**: 如果业务增长迅速，可以考虑对现有分片进行再次拆分，进一步细化粒度。

### 三、 应用与缓存层优化 (减少DB请求)

- **应用层缓存**:
  - 使用 **Redis** 或 **Memcached** 缓存热点数据（如用户信息、商品详情）或查询结果。
  - 这是提升性能、抗住高并发流量的利器。
- **并行查询**:
  - 对于需要跨多个分片查询的请求，应用层或中间件（如ShardingSphere）可以并行向各分片发起查询，然后聚合结果。
- **避免复杂JOIN**:
  - 将复杂的JOIN操作拆分为多次单表查询，在应用层进行数据组装。这样可以降低数据库的计算压力，也更容易利用到缓存。

### 总结
优化思路通常遵循以下路径：
1.  **先做SQL和索引优化**：成本最低，见效快。
2.  **引入缓存和读写分离**：应对高并发读的场景。
3.  **最后考虑分区、归档、二次分片等重度方案**：这些方案改造成本高，但能从根本上解决单表数据量过大的问题。