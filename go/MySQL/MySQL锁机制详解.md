# MySQL 锁机制核心速查

## 一、锁的分类

### 1. 按粒度划分
- **表级锁 (Table Lock)**: 锁住整张表，开销小，加锁快，但并发度低。MyISAM 主要使用。
- **行级锁 (Row Lock)**: 锁住目标行，开销大，加锁慢，但并发度高。InnoDB 主要使用。

### 2. 按模式划分
- **共享锁 (Shared Lock / S锁)**: 读锁。多个事务可同时读取同一资源，但不能修改。
  - 获取: `SELECT ... LOCK IN SHARE MODE;`
- **排他锁 (Exclusive Lock / X锁)**: 写锁。一个事务获取后，其他事务不能读写。
  - 获取: `SELECT ... FOR UPDATE;`, `UPDATE`, `DELETE`
- **意向锁 (Intention Lock)**: 表级锁，为了协调行锁和表锁。
  - **IS锁**: 事务想在某些行上加 S 锁。
  - **IX锁**: 事务想在某些行上加 X 锁。
  - 作用: 当事务需要获取表锁时，只需检查意向锁，无需遍历所有行，提高效率。

## 二、InnoDB 行锁详解

InnoDB 在可重复读 (Repeatable Read) 隔离级别下，为解决幻读问题，引入了以下锁：

- **记录锁 (Record Lock)**: 精确锁定单条索引记录。
- **间隙锁 (Gap Lock)**: 锁定索引记录之间的"间隙"，防止其他事务在此间隙插入新记录。
- **临键锁 (Next-Key Lock)**: **记录锁 + 间隙锁**的组合，锁定一个左开右闭的区间。这是 InnoDB 的默认行锁算法。

> **核心目的**: 临键锁和间隙锁主要用于防止**幻读**。

## 三、死锁 (Deadlock)

### 1. 产生条件
1.  **互斥**: 资源独占。
2.  **请求与保持**: 持有锁的同时请求新锁。
3.  **不可剥夺**: 已获的锁不能被强行抢占。
4.  **循环等待**: 事务间形成锁等待的环形链。

### 2. 解决方案
- **自动检测**: InnoDB 通过 `wait-for graph` 算法主动检测死锁，并回滚代价最小的事务。
- **超时机制**: `innodb_lock_wait_timeout` 参数设置锁等待超时时间。

### 3. 如何避免
- **统一加锁顺序**: 所有事务按相同顺序获取锁。
- **减少锁持有时间**: 尽快完成事务，尽早释放锁。
- **使用低隔离级别**: 在业务允许的情况下，使用 `Read Committed` 隔离级别，可禁用间隙锁，减少锁冲突。
- **优化索引和SQL**: 好的索引能让查询更快定位，减少锁定的范围。

## 四、锁优化策略

- **小事务**: 尽量缩短事务范围，减少锁的持有时间。
- **低隔离**: 在保证数据一致性的前提下，使用较低的隔离级别。
- **精确锁定**: 尽量使用索引来精确锁定数据行，而不是大范围扫描。
- **加锁顺序**: 约定好不同业务的加锁顺序，避免循环等待。

## 五、监控与诊断

- **查看锁等待**: `information_schema.INNODB_LOCK_WAITS`
- **查看当前锁**: `information_schema.INNODB_LOCKS`
- **查看事务**: `information_schema.INNODB_TRX`
- **综合分析**: `SHOW ENGINE INNODB STATUS;` (包含最近的死锁日志)
- **MySQL 8.0+**: 使用 `performance_schema` 下的 `data_locks` 和 `data_lock_waits` 表，信息更全面。

## 六、常见场景

- **秒杀/扣库存**:
  `SELECT ... FOR UPDATE` 悲观锁住商品库存行，检查库存后执行 `UPDATE`。
- **防重复处理**:
  使用唯一索引是最佳方案。若无，可对关键字段 `SELECT ... FOR UPDATE` 锁定，判断记录是否存在后再插入。
