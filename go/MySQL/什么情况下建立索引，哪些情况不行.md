### 适合建立索引

- **`WHERE` 子句**：查询条件中频繁使用的列。
- **`ORDER BY`**：排序操作中频繁使用的列。
- **`JOIN` 操作**：作为表连接条件的列（如外键）。
- **唯一性约束**：需要保证值唯一的列（`UNIQUE` 索引）。
- **高选择性**：列中包含大量不同值，重复值少（例如：身份证号、邮箱）。
- **复合查询**：多个列经常一起作为查询条件时，应建立复合索引。

### 不适合/慎重建立索引

- **低选择性**：列中不同值很少（例如：性别、状态）。索引效果差，且增加维护成本。
- **频繁更新**：经常被 `UPDATE` 的列。每次更新都需要维护索引，增加写操作开销。
- **小表**：数据量很少的表，全表扫描通常足够快，索引的维护成本可能超过其收益。
- **频繁批量增删**：大量 `INSERT` 或 `DELETE` 操作会带来巨大的索引维护开销。
- **前缀模糊查询**：`WHERE name LIKE '%keyword'` 这种以通配符开头的查询，无法利用B-Tree索引。
- **宽列**：`TEXT`、`BLOB` 或很长的 `VARCHAR` 列。索引会占用大量存储空间，且性能提升有限。
- **不常使用的列**：很少在查询中用到的列。

### 总结

建立索引的核心是在 **查询性能提升** 和 **写操作（增删改）与存储开销增加** 之间做权衡。