## WHERE vs. HAVING 快速查阅

### 核心区别对比

| 特性 | WHERE | HAVING |
| :--- | :--- | :--- |
| **作用对象** | 原始数据行 | `GROUP BY` 之后的分组 |
| **作用时机** | `GROUP BY` 和聚合函数前 | `GROUP BY` 和聚合函数后 |
| **聚合函数** | **不能**使用 | **可以**使用 |
| **性能** | 提前过滤数据，性能更高 | 对聚合后结果过滤 |
| **索引**| 可利用索引 | 无法直接利用索引 |


### SQL执行顺序

`FROM` -> `WHERE` -> `GROUP BY` -> `HAVING` -> `SELECT` -> `ORDER BY` -> `LIMIT`

### 总结要点

1.  **时机不同**：`WHERE` 在分组前过滤，`HAVING` 在分组后过滤。
2.  **对象不同**：`WHERE` 作用于行，`HAVING` 作用于分组。
3.  **聚合函数**：`WHERE` 子句中不能使用聚合函数，而 `HAVING` 子句可以。
4.  **性能优先**：优先使用 `WHERE` 提前过滤掉尽可能多的数据，以提升性能。