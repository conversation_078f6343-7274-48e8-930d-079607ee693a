在高并发环境下，InnoDB 使用行锁来保证数据的一致性和隔离性。主要有三种行锁：

#### 1. 记录锁 (Record Lock)

- **锁定什么**：单个**数据行**。
- **解决什么**：防止其他事务**修改**或**删除**当前行。
- **特点**：最基本的行级锁，精确锁定某一行。
- **示例**：`UPDATE employees SET salary = 60000 WHERE id = 10;` 会对 `id = 10` 的行加记录锁。

#### 2. 间隙锁 (Gap Lock)

- **锁定什么**：记录之间的**间隙**（不包含记录本身）。
- **解决什么**：防止其他事务在间隙中**插入**新记录，这是解决**幻读**的关键。
- **特点**：只锁间隙，不锁记录。
- **示例**：一个查询 `WHERE id > 10 AND id < 20`，如果这个范围内没有数据，会锁定 (10, 20) 这个间隙。

#### 3. 临键锁 (Next-Key Lock)

- **锁定什么**：**记录** + **记录前面的间隙**。
- **解决什么**：同时实现记录锁和间隙锁的功能，是 InnoDB 在 `REPEATABLE READ` 隔离级别下的默认锁。
- **特点**：记录锁和间隙锁的组合，锁定一个左开右闭的区间。
- **示例**：如果索引上有 10, 20, 30，一个 `WHERE id <= 20` 的更新会锁定 `(-∞, 10]`、`(10, 20]` 这两个区间。

### 快速小结

| 锁类型     | 锁定范围             | 主要作用                 |
| :--------- | :------------------- | :----------------------- |
| **记录锁** | 单个数据行           | 防止行被修改/删除        |
| **间隙锁** | 记录间的空隙         | 防止插入新记录（防幻读） |
| **临键锁** | 记录 + 前面的间隙    | RR级别默认，防幻读       |

- InnoDB 通过这些锁机制，实现了高效的并发控制，并通过临键锁在可重复读（RR）隔离级别下有效避免了幻读问题。