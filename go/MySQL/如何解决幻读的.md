# MySQL 如何解决幻读

### 1. 什么是幻读？

在一个事务内，相同的范围查询语句，后续的读取操作看到了前一次读取时不存在的行（被其他事务插入并提交）。

**例子**:
- 事务A: `SELECT * FROM users WHERE age > 20;` 得到10条记录。
- 事务B: `INSERT INTO users (name, age) VALUES ('new_user', 25);` 并提交。
- 事务A: 再次执行 `SELECT * FROM users WHERE age > 20;` 得到11条记录。这条新出现的记录就是"幻影"。

---

### 2. 核心解决方案

MySQL InnoDB 引擎在**可重复读（Repeatable Read）**（默认隔离级别）下，通过以下两种机制结合来解决幻读问题：

#### a. 多版本并发控制 (MVCC)
- **机制**: 对于`SELECT`查询，事务启动时会创建一个数据快照 (Read View)。后续的所有普通`SELECT`都从这个快照中读取数据，从而实现了与其他事务的隔离。
- **效果**: 看不到其他事务在本事务启动后插入的新数据，因此避免了幻读。

#### b. 临键锁 (Next-Key Lock)
- **机制**: 临键锁是 **记录锁 (Record Lock)** 和 **间隙锁 (Gap Lock)** 的结合。
    - **记录锁**: 锁定已存在的记录。
    - **间隙锁**: 锁定记录之间的"间隙"，防止其他事务在这个间隙中插入新数据。
- **效果**: 对于`SELECT ... FOR UPDATE` 或 `UPDATE`/`DELETE` 等当前读操作，通过临键锁锁定查询范围，防止其他事务插入新行，从而避免幻读。

---

### 3. 各隔离级别的表现

| 隔离级别 | 是否解决幻读 | 解决方法 |
| :--- | :--- | :--- |
| **读已提交 (Read Committed)** | ❌ **否** | - |
| **可重复读 (Repeatable Read)** | ✅ **是** (MySQL默认) | MVCC + 临键锁 (Next-Key Lock) |
| **串行化 (Serializable)** | ✅ **是** | 对所有读写操作加锁，强制事务串行执行。性能极低。|

### 面试核心回答

MySQL 在默认的**可重复读**隔离级别下就已经解决了幻读问题。它主要依靠 **MVCC**（多版本并发控制）来处理普通查询（快照读），避免读取到新插入的数据；同时，通过**临键锁**（间隙锁+记录锁）来处理当前读（如 `SELECT ... FOR UPDATE`），鎖定查詢範圍，防止其他事务插入数据。