## MySQL分区表核心速查

### 1. 核心定义

- **什么是分区表？**
  - **一句话概括：** 将一个大表的数据，按指定规则（如按时间、按地区）分散存储到多个独立的物理文件中，但**逻辑上仍是单个表**。
- **与分表的关键区别：**
  - **分区 (Partitioning)：** 对应用**透明**，由MySQL底层管理，逻辑上是一张表。
  - **分表 (Sharding)：** 对应用**不透明**，需要应用层或中间件来管理数据路由，逻辑上是多张独立的表。

### 2. 主要分区类型及适用场景

| 分区类型 | 核心语法 | 适用场景 |
| :--- | :--- | :--- |
| **RANGE** | `PARTITION BY RANGE (expr)` | **时间序列数据**，如订单、日志（按年、月、日分区）。 |
| **LIST** | `PARTITION BY LIST (col)` | **离散的、可枚举的值**，如地区、状态、类别。 |
| **HASH** | `PARTITION BY HASH (expr)` | **无明显业务逻辑**，希望数据均匀分布的场景。 |
| **KEY** | `PARTITION BY KEY (col)` | 类似HASH，由MySQL保证更均匀的哈希，通常用于PK或UK。 |

### 3. 优缺点与核心限制

#### 优点 (Pros)

1.  **性能提升 (最核心)**
    - **分区裁剪 (Partition Pruning)：** 查询时，优化器会自动过滤掉不相关的分区，只扫描必要数据，极大提升查询效率。可以通过 `EXPLAIN PARTITIONS` 查看。
2.  **管理便捷**
    - **快速数据清理：** 删除旧数据时，可直接 `DROP PARTITION`，远快于 `DELETE` 操作，且不产生大量binlog。
    - **独立维护：** 可对单个分区进行索引重建、备份、恢复等操作。
3.  **存储优化**
    - 可以将不同分区存储在不同的物理磁盘上，突破单盘I/O瓶颈。

#### 缺点 (Cons) / 核心限制

1.  **分区键强制约束 (最重要)**
    - 分区键**必须是主键或唯一索引的一部分**。这是使用分区表时最需要注意的设计约束。
2.  **查询性能陷阱**
    - 如果`WHERE`条件中**不包含分区键**，会导致全部分区扫描，性能可能比不分区更差。
3.  **功能限制**
    - 分区表不支持外键约束。
    - 分区数量不宜过多（官方建议<1024，但通常几十到一百个为佳），否则开销增大。

### 4. 关键原则与实践

- **分区键选择原则：**
  - **必须是查询最频繁的过滤条件**，以最大化分区裁剪的效果。
  - **尽量保证数据均匀分布**，避免热点分区。
- **运维管理：**
  - **自动化维护：** 对于按时间分区的表，必须编写脚本**定期自动创建新分区和删除过期分区**。
  - **监控：** 通过`information_schema.PARTITIONS`监控各分区的大小、行数，防止数据倾斜。

### 5. 典型应用场景

1.  **时间序列数据：** 网站访问日志、交易记录、监控数据等。按月或按周分区，便于按时间范围查询和归档。
2.  **地理位置数据：** 用户信息按省份或大区进行LIST分区。

### 6. 面试快问快答 Checklist

1.  **分区和分表的区别？**
    > 分区对应用透明，是MySQL内部机制；分表需要应用层处理，是架构层面的事。
2.  **分区表最大的好处是什么？**
    > 分区裁剪。当查询条件带上分区键时，能极大减少扫描的数据量。
3.  **使用分区表有什么代价或限制？**
    > 分区键必须是主键或唯一索引的一部分；不支持外键；查询不带分区键会扫描所有分区。
4.  **什么场景下分区表会比普通表更慢？**
    > 查询条件里不包含分区键，导致全部分区扫描。
5.  **按月分区的日志表，如何管理？**
    > 写定时任务，每月初自动创建下个月的新分区，并删除N个月前的旧分区。
