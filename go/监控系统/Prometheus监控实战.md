# Prometheus面试精简版

## 1. 面试核心问题

- **Prometheus的优势?**
  - **Pull模式**: 便于部署和维护，控制抓取节奏。
  - **多维数据模型**: `key-value`标签系统，灵活查询。
  - **强大的查询语言(PromQL)**: 丰富函数，支持聚合、预测。
  - **生态系统**: 与Grafana、Alertmanager等无缝集成。

- **如何设计监控指标？(RED/USE方法)**
  - **RED方法 (服务监控)**:
    - **Rate**: 请求速率 (QPS)
    - **Errors**: 错误率
    - **Duration**: 响应耗时 (P95, P99延迟)
  - **USE方法 (资源监控)**:
    - **Utilization**: 使用率 (CPU, 内存)
    - **Saturation**: 饱和度 (队列长度)
    - **Errors**: 错误数 (硬件错误)
  - **选择合适的指标类型**: Counter, Gauge, Histogram, Summary.

- **如何优化Prometheus性能？**
  - **控制标签基数(Cardinality)**: 避免使用`user_id`等高基数标签。
  - **合理设置抓取/评估间隔**: `scrape_interval`, `evaluation_interval`。
  - **使用远程存储(Remote Storage)**: 如Thanos, VictoriaMetrics，解决长期存储和扩展性问题。
  - **优化查询(PromQL)**: 尽量在聚合前过滤，避免大范围`rate`计算。
  - **联邦集群(Federation)**: 分层聚合，降低单实例负载。

- **告警规则如何设计？**
  - **基于SLI/SLO**: 告警应反映用户体验和业务影响。
  - **避免告警疲劳**:
    - 设置合理的`for`持续时间，过滤抖动。
    - 分级告警(`severity`: warning, critical)。
    - 使用抑制(inhibition)和分组(grouping)。
  - **清晰的告警信息**: `annotations`中提供告警原因、影响和处理建议。

## 2. 核心概念

- **数据模型**: `metric_name{label1="value1"} value timestamp`
- **Job & Instance**: 一个`Job`包含多个`Instance`(Target)。
- **四种指标类型**:
  - **Counter**: **只增不减**计数器 (如: http_requests_total)。
  - **Gauge**: **可增可减**瞬时值 (如: active_connections, memory_usage)。
  - **Histogram**: **服务端统计**分布，计算分位数 (如: request_duration_seconds)。适合聚合。
  - **Summary**: **客户端计算**分位数，消耗客户端资源。不适合聚合。

## 3. PromQL速查

- **基本查询**:
  - `http_requests_total{job="api", method="GET"}`
  - `http_requests_total{endpoint=~"/api/.*"}`
- **核心函数**:
  - `rate(v[d])`: 计算**每秒平均增长速率**。用于Counter。
  - `irate(v[d])`: 计算**瞬时增长速率**。更灵敏，适合图表。
  - `increase(v[d])`: 计算**时间范围内总增量**。
  - `sum(v) by (label)`: **聚合**。
  - `topk(k, v)`: 前k个最大值。
- **Histogram函数**:
  - `histogram_quantile(φ, v)`: 计算**φ分位数** (如: 0.95)。
- **预测与比较**:
  - `predict_linear(v[d], t)`: **线性预测**t秒后的值。
  - `avg_over_time(v[d])`: 计算**时间范围内平均值**。

## 4. 告警规则示例

```yaml
groups:
- name: service_alerts
  rules:
  - alert: HighErrorRate
    # 表达式: 5xx错误率超过5%
    expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
    # 持续时间: 持续2分钟才触发
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "服务 {{ $labels.job }} 错误率过高"
      description: "错误率已达 {{ $value | humanizePercentage }}."
```

## 5. 最佳实践

- **指标命名**: `应用_模块_指标名_单位` (e.g., `http_request_duration_seconds`)。清晰，无缩写。
- **标签设计**:
  - **低基数**: 核心原则。
  - **有意义**: 业务维度 (e.g., `env`, `service`)。
  - **一致性**: 相同概念用相同标签 (e.g., `pod` vs `kubernetes_pod`)。
- **监控覆盖度**:
  - **基础设施**: CPU, 内存, 磁盘, 网络 (Node Exporter)。
  - **应用层**: QPS, 延迟, 错误率 (RED)。
  - **业务层**: 用户注册数, 订单量。
  - **依赖服务**: 数据库, 缓存, MQ。
