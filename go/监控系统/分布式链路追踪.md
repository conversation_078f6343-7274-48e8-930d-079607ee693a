# 分布式链路追踪（面试速查版）

### 核心定义
- **一句话总结**: 分布式链路追踪通过Trace和Span构建请求调用链，帮助理解分布式系统的行为和性能特征。
- **Trace**: 代表一次完整的请求链路，由多个Span组成。
- **Span**: 代表链路中的一个操作/工作单元，包含操作名、起止时间、属性(Tags)、日志(Logs)等。
- **SpanContext**: 跨服务边界传递的追踪上下文，主要包含 `TraceID` 和 `SpanID`。

---

### 核心原理
1.  **上下文传播 (Context Propagation)**
    - **目标**: 在跨进程调用时（如RPC、HTTP、消息队列），将`SpanContext`从上游服务传递到下游服务。
    - **实现**:
        - **HTTP**: 通常通过HTTP Header传递（如W3C Trace Context标准的`traceparent`头）。
        - **消息队列 (Kafka/RocketMQ)**: 通过消息的Header/Properties传递。
        - **gRPC**: 通过`metadata`传递。

2.  **数据采集/埋点 (Instrumentation)**
    - **自动埋点**: 依赖框架和库的中间件/拦截器，对开发者透明。
        - **Web框架**: `otelhttp`中间件。
        - **gRPC**: `otelgrpc`拦截器。
        - **数据库/缓存**: `otelsql`, `otelredis`等包装库，自动记录DB查询、Redis命令。
    - **手动埋点**: 在业务代码中手动创建Span，添加自定义属性、事件，或记录错误。
        - `tracer.Start()`: 创建新Span。
        - `span.SetAttributes()`: 添加业务属性（如`user.id`）。
        - `span.RecordError()`: 记录错误信息。

3.  **采样 (Sampling)**
    - **目的**: 在高流量下，只采集部分请求的追踪数据，以降低性能开销和存储成本。
    - **常用策略**:
        - **固定比例采样 (TraceIDRatioBased)**: 如采集10%的请求。
        - **基于父级采样 (ParentBased)**: 如果父Span被采样，则子Span也一定被采样。
        - **始终/从不采样 (Always/Never)**
    - **高级策略**:
        - **动态采样**: 根据请求特征（如是否包含错误、是否为VIP用户、请求路径等）决定是否采样。

4.  **数据处理与导出 (Processing & Exporting)**
    - **Span Processor**:
        - **BatchSpanProcessor**: 异步批量导出Span，是生产环境推荐用法，可降低导出频率和网络开销。
        - **SimpleSpanProcessor**: 每当Span结束后立即同步导出，适合调试。
    - **Exporter**: 负责将格式化后的追踪数据发送到后端存储系统。
        - **常见后端**: Jaeger, Zipkin, SkyWalking, Prometheus。
        - **配置**: 指定后端地址（如`http://jaeger-collector:14268/api/traces`）。

---

### 面试核心问题

1.  **为什么需要分布式追踪？/ 它解决了什么问题？**
    - **全链路监控**: 可视化请求在分布式系统中的完整路径。
    - **快速故障定位**: 快速找到出错的服务和代码点。
    - **性能瓶颈分析**: 分析每个环节的耗时，识别性能瓶颈。
    - **服务依赖分析**: 自动发现和梳理服务间的调用关系。

2.  **一个追踪系统（如OpenTelemetry）的核心组件有哪些？**
    - **API**: 标准化的接口，用于在代码中进行埋点。
    - **SDK**: API的实现，包含采样、处理、导出等逻辑。
    - **Exporter**: 将数据发送到后端的插件。
    - **Collector (可选)**: 一个独立的中间服务，用于接收、处理、转发追踪数据，可以解耦应用和后端。

3.  **如何控制追踪系统的性能开销？**
    - **采样**: 最重要的方式，特别是动态采样，只保留高价值的追踪数据。
    - **批量异步导出**: 使用`BatchSpanProcessor`，减少对应用主线程的影响。
    - **控制数据量**: 限制Span的Attribute和Event数量及大小。
    - **部署Collector**: 将数据处理的压力从业务应用转移到独立的Collector服务。

4.  **技术选型对比 (Jaeger vs. Zipkin vs. SkyWalking)**
    - **Jaeger**: CNCF毕业项目，Go语言实现，与OpenTelemetry生态集成最好，功能强大。
    - **Zipkin**: Twitter开源，Java实现，概念简单，易于上手。
    - **SkyWalking**: Apache顶级项目，Java实现，提供了追踪、指标、日志一体化的APM解决方案，对Java应用支持尤其好，侵入性较低。

5.  **最佳实践有哪些？**
    - **标准化Span命名**: 格式一般为 `package.class.method` 或 `protocol.operation`，方便聚合分析。
    - **添加关键业务属性 (Attribute)**: 如用户ID、订单号、租户ID等，方便按业务维度查询。
    - **错误记录**: 务必在捕获到异常时使用`span.RecordError()`并设置`span.SetStatus(codes.Error, ...)`。
    - **避免记录敏感信息**: 如密码、身份证号等。
