# Trie树(前缀树)实现

## 基本概念

Trie树，也称前缀树，是一种用于快速检索字符串数据集中的键的树形数据结构。

## 基本特性

1. **根节点不包含字符**，除根节点外每个节点都只包含一个字符
2. **从根节点到某一节点的路径**，连接起来就是该节点对应的字符串
3. **每个节点的所有子节点**包含的字符都不相同
4. **节点标记**：标识从根到该节点是否构成一个完整单词

## Go语言实现

### 基本结构
```go
type TrieNode struct {
    children map[rune]*TrieNode
    isEnd    bool
}

type Trie struct {
    root *TrieNode
}

func NewTrie() *Trie {
    return &Trie{
        root: &TrieNode{
            children: make(map[rune]*TrieNode),
        },
    }
}
```

### 核心操作
```go
// 插入单词
func (t *Trie) Insert(word string) {
    node := t.root
    for _, char := range word {
        if node.children[char] == nil {
            node.children[char] = &TrieNode{
                children: make(map[rune]*TrieNode),
            }
        }
        node = node.children[char]
    }
    node.isEnd = true
}

// 查找单词
func (t *Trie) Search(word string) bool {
    node := t.findNode(word)
    return node != nil && node.isEnd
}

// 前缀查找
func (t *Trie) StartsWith(prefix string) bool {
    return t.findNode(prefix) != nil
}

func (t *Trie) findNode(word string) *TrieNode {
    node := t.root
    for _, char := range word {
        if node.children[char] == nil {
            return nil
        }
        node = node.children[char]
    }
    return node
}
```

## 高级功能

### 自动补全
```go
func (t *Trie) AutoComplete(prefix string, limit int) []string {
    var result []string
    node := t.findNode(prefix)
    if node == nil {
        return result
    }

    t.dfs(node, prefix, &result, limit)
    return result
}

func (t *Trie) dfs(node *TrieNode, current string, result *[]string, limit int) {
    if len(*result) >= limit {
        return
    }
    if node.isEnd {
        *result = append(*result, current)
    }
    for char, child := range node.children {
        if len(*result) >= limit {
            break
        }
        t.dfs(child, current+string(char), result, limit)
    }
}
```

## 性能分析

### 时间复杂度
- **插入**: O(m)，m为字符串长度
- **查找**: O(m)
- **删除**: O(m)
- **前缀查找**: O(p + n)，p为前缀长度，n为结果数量

### 空间复杂度
- **最坏情况**: O(ALPHABET_SIZE * N * M)
- **平均情况**: 取决于字符串的公共前缀

## 应用场景

### 1. 搜索引擎
- 自动补全功能
- 拼写检查
- 关键词提示

### 2. IP路由表
```go
type IPTrie struct {
    root *IPNode
}

type IPNode struct {
    children [2]*IPNode  // 0和1两个子节点
    isEnd    bool
    gateway  string
}

func (ipt *IPTrie) InsertRoute(ip string, gateway string) {
    node := ipt.root
    
    for _, bit := range ip {
        index := int(bit - '0')
        if node.children[index] == nil {
            node.children[index] = &IPNode{}
        }
        node = node.children[index]
    }
    
    node.isEnd = true
    node.gateway = gateway
}
```

### 3. 文本处理
- 敏感词过滤
- 文本匹配
- 词频统计

## 优化技巧

### 1. 内存优化
- 使用数组代替map（固定字符集）
- 压缩Trie减少节点数量
- 延迟创建子节点

### 2. 查询优化
- 缓存热点查询结果
- 预计算常用前缀
- 并行查询多个前缀

## 面试要点
1. **基本原理**: 前缀共享，树形结构存储字符串
2. **时间复杂度**: 查找、插入、删除都是O(m)
3. **空间权衡**: 用空间换时间，适合前缀查询
4. **应用场景**: 自动补全、IP路由、敏感词过滤
5. **优化方向**: 压缩Trie、内存优化、查询缓存
