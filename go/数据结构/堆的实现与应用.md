# 堆的实现与应用

## 基本概念

堆是一种特殊的完全二叉树，满足堆性质：
- **最大堆**: 父节点值 ≥ 子节点值
- **最小堆**: 父节点值 ≤ 子节点值

## 核心特性

1. **完全二叉树**: 除最后一层外都填满，最后一层从左到右填充
2. **堆序性质**: 满足父子节点的大小关系
3. **数组表示**: 可以用数组高效存储
4. **索引关系**: 
   - 父节点: `(i-1)/2`
   - 左子节点: `2*i+1`
   - 右子节点: `2*i+2`

## Go语言实现

### 最小堆实现
```go
type MinHeap struct {
    data []int
}

func NewMinHeap() *MinHeap {
    return &MinHeap{data: make([]int, 0)}
}

// 插入元素
func (h *MinHeap) Push(val int) {
    h.data = append(h.data, val)
    h.heapifyUp(len(h.data) - 1)
}

// 删除最小元素
func (h *MinHeap) Pop() int {
    if len(h.data) == 0 {
        return -1
    }
    
    min := h.data[0]
    last := len(h.data) - 1
    h.data[0] = h.data[last]
    h.data = h.data[:last]
    
    if len(h.data) > 0 {
        h.heapifyDown(0)
    }
    
    return min
}

// 向上调整
func (h *MinHeap) heapifyUp(index int) {
    for index > 0 {
        parent := (index - 1) / 2
        if h.data[parent] <= h.data[index] {
            break
        }
        h.data[parent], h.data[index] = h.data[index], h.data[parent]
        index = parent
    }
}

// 向下调整
func (h *MinHeap) heapifyDown(index int) {
    for {
        left := 2*index + 1
        right := 2*index + 2
        smallest := index
        
        if left < len(h.data) && h.data[left] < h.data[smallest] {
            smallest = left
        }
        
        if right < len(h.data) && h.data[right] < h.data[smallest] {
            smallest = right
        }
        
        if smallest == index {
            break
        }
        
        h.data[index], h.data[smallest] = h.data[smallest], h.data[index]
        index = smallest
    }
}

// 获取最小值
func (h *MinHeap) Peek() int {
    if len(h.data) == 0 {
        return -1
    }
    return h.data[0]
}

// 堆大小
func (h *MinHeap) Size() int {
    return len(h.data)
}
```

### 使用标准库
```go
import "container/heap"

type IntHeap []int

func (h IntHeap) Len() int           { return len(h) }
func (h IntHeap) Less(i, j int) bool { return h[i] < h[j] }
func (h IntHeap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *IntHeap) Push(x interface{}) {
    *h = append(*h, x.(int))
}

func (h *IntHeap) Pop() interface{} {
    old := *h
    n := len(old)
    x := old[n-1]
    *h = old[0 : n-1]
    return x
}

func useStandardHeap() {
    h := &IntHeap{2, 1, 5}
    heap.Init(h)
    heap.Push(h, 3)
    
    for h.Len() > 0 {
        fmt.Printf("%d ", heap.Pop(h))
    }
}
```

## 时间复杂度

| 操作 | 时间复杂度 | 说明 |
|------|------------|------|
| 插入 | O(log n) | 向上调整 |
| 删除最值 | O(log n) | 向下调整 |
| 查看最值 | O(1) | 直接访问根节点 |
| 建堆 | O(n) | 自底向上调整 |

## 经典应用

### 1. 优先队列
```go
type Task struct {
    priority int
    name     string
}

type PriorityQueue []*Task

func (pq PriorityQueue) Len() int { return len(pq) }
func (pq PriorityQueue) Less(i, j int) bool {
    return pq[i].priority > pq[j].priority  // 高优先级在前
}
func (pq PriorityQueue) Swap(i, j int) { pq[i], pq[j] = pq[j], pq[i] }

func (pq *PriorityQueue) Push(x interface{}) {
    *pq = append(*pq, x.(*Task))
}

func (pq *PriorityQueue) Pop() interface{} {
    old := *pq
    n := len(old)
    task := old[n-1]
    *pq = old[0 : n-1]
    return task
}
```

### 2. 堆排序
```go
func heapSort(arr []int) {
    n := len(arr)
    
    // 建堆
    for i := n/2 - 1; i >= 0; i-- {
        heapify(arr, n, i)
    }
    
    // 排序
    for i := n - 1; i > 0; i-- {
        arr[0], arr[i] = arr[i], arr[0]
        heapify(arr, i, 0)
    }
}

func heapify(arr []int, n, i int) {
    largest := i
    left := 2*i + 1
    right := 2*i + 2
    
    if left < n && arr[left] > arr[largest] {
        largest = left
    }
    
    if right < n && arr[right] > arr[largest] {
        largest = right
    }
    
    if largest != i {
        arr[i], arr[largest] = arr[largest], arr[i]
        heapify(arr, n, largest)
    }
}
```

### 3. Top K 问题
```go
func findKLargest(nums []int, k int) []int {
    h := &IntHeap{}
    heap.Init(h)
    
    for _, num := range nums {
        heap.Push(h, num)
        if h.Len() > k {
            heap.Pop(h)
        }
    }
    
    result := make([]int, k)
    for i := k - 1; i >= 0; i-- {
        result[i] = heap.Pop(h).(int)
    }
    
    return result
}
```

### 4. 合并K个有序链表
```go
type ListNode struct {
    Val  int
    Next *ListNode
}

type NodeHeap []*ListNode

func (h NodeHeap) Len() int           { return len(h) }
func (h NodeHeap) Less(i, j int) bool { return h[i].Val < h[j].Val }
func (h NodeHeap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *NodeHeap) Push(x interface{}) {
    *h = append(*h, x.(*ListNode))
}

func (h *NodeHeap) Pop() interface{} {
    old := *h
    n := len(old)
    x := old[n-1]
    *h = old[0 : n-1]
    return x
}

func mergeKLists(lists []*ListNode) *ListNode {
    h := &NodeHeap{}
    heap.Init(h)
    
    // 将所有链表的头节点加入堆
    for _, list := range lists {
        if list != nil {
            heap.Push(h, list)
        }
    }
    
    dummy := &ListNode{}
    current := dummy
    
    for h.Len() > 0 {
        node := heap.Pop(h).(*ListNode)
        current.Next = node
        current = current.Next
        
        if node.Next != nil {
            heap.Push(h, node.Next)
        }
    }
    
    return dummy.Next
}
```

## 面试要点

1. **基本概念**: 完全二叉树，满足堆序性质
2. **实现方式**: 数组表示，索引关系计算
3. **时间复杂度**: 插入删除O(log n)，查看最值O(1)
4. **应用场景**: 优先队列、堆排序、Top K问题
5. **标准库**: container/heap包的使用方法
