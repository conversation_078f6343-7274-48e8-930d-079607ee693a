# 红黑树详解

## 为什么需要红黑树？

普通二叉搜索树在极端情况下会退化为链表，时间复杂度从O(log n)退化为O(n)。红黑树通过颜色约束保持平衡，确保操作效率。

## 红黑树的五个性质

1. **节点颜色**：每个节点要么红色，要么黑色
2. **根节点**：根节点必须是黑色
3. **红色约束**：红色节点的子节点必须是黑色（不能有连续红色节点）
4. **叶子节点**：所有叶子节点(NIL)都是黑色
5. **黑色平衡**：从任意节点到叶子节点的路径上，黑色节点数量相同

## Go语言实现

### 节点结构
```go
type Color int

const (
    RED Color = iota
    BLACK
)

type RBNode struct {
    key    int
    color  Color
    left   *RBNode
    right  *RBNode
    parent *RBNode
}

type RBTree struct {
    root *RBNode
    nil  *RBNode  // 哨兵节点
}

func NewRBTree() *RBTree {
    nil := &RBNode{color: BLACK}
    return &RBTree{
        root: nil,
        nil:  nil,
    }
}
```

### 旋转操作
```go
// 左旋
func (t *RBTree) leftRotate(x *RBNode) {
    y := x.right
    x.right = y.left

    if y.left != t.nil {
        y.left.parent = x
    }

    y.parent = x.parent

    if x.parent == t.nil {
        t.root = y
    } else if x == x.parent.left {
        x.parent.left = y
    } else {
        x.parent.right = y
    }

    y.left = x
    x.parent = y
}

// 右旋
func (t *RBTree) rightRotate(y *RBNode) {
    x := y.left
    y.left = x.right

    if x.right != t.nil {
        x.right.parent = y
    }

    x.parent = y.parent

    if y.parent == t.nil {
        t.root = x
    } else if y == y.parent.right {
        y.parent.right = x
    } else {
        y.parent.left = x
    }

    x.right = y
    y.parent = x
}
```

### 插入操作
```go
func (t *RBTree) Insert(key int) {
    z := &RBNode{
        key:    key,
        color:  RED,
        left:   t.nil,
        right:  t.nil,
        parent: t.nil,
    }

    t.insertNode(z)
    t.insertFixup(z)
}

func (t *RBTree) insertFixup(z *RBNode) {
    for z.parent.color == RED {
        if z.parent == z.parent.parent.left {
            y := z.parent.parent.right  // 叔叔节点

            if y.color == RED {
                // 情况1：叔叔是红色
                z.parent.color = BLACK
                y.color = BLACK
                z.parent.parent.color = RED
                z = z.parent.parent
            } else {
                if z == z.parent.right {
                    // 情况2：叔叔是黑色，z是右孩子
                    z = z.parent
                    t.leftRotate(z)
                }
                // 情况3：叔叔是黑色，z是左孩子
                z.parent.color = BLACK
                z.parent.parent.color = RED
                t.rightRotate(z.parent.parent)
            }
        } else {
            // 对称情况
            // ... 类似处理
        }
    }
    t.root.color = BLACK
}
```

## 性能特点

| 操作 | 时间复杂度 | 说明 |
|------|------------|------|
| 查找 | O(log n) | 树高度约为2log(n) |
| 插入 | O(log n) | 包含修复操作 |
| 删除 | O(log n) | 包含修复操作 |

## 与其他树的对比

| 特性 | 红黑树 | AVL树 | B树 |
|------|--------|-------|-----|
| 平衡性 | 近似平衡 | 严格平衡 | 多路平衡 |
| 插入/删除 | 较快 | 较慢 | 适中 |
| 查找 | 快 | 最快 | 适中 |
| 内存占用 | 低 | 低 | 高 |

## 实际应用

- **Java**: TreeMap、TreeSet
- **C++**: std::map、std::set
- **Linux内核**: 进程调度器(CFS)
- **数据库**: 内存索引结构

## 面试要点
1. **五个性质**：能准确描述红黑树的约束条件
2. **平衡原理**：理解如何通过颜色约束保持平衡
3. **操作复杂度**：所有基本操作都是O(log n)
4. **应用场景**：适合频繁插入删除的场景