# Hash函数详解

## 基本概念

Hash函数（散列函数）是一种将任意长度的输入数据映射为固定长度值的函数，广泛应用于数据存储、密码学、数据检索等领域。

## 核心特性

1. **确定性**：相同输入总是产生相同输出
2. **高效性**：计算速度快，适合大量数据处理
3. **均匀分布**：输出值在值域内均匀分布
4. **雪崩效应**：输入微小变化导致输出大幅变化
5. **单向性**：从输出难以推导输入（密码学hash）
6. **抗碰撞性**：难以找到产生相同输出的不同输入

## 常见Hash算法

### 1. 非密码学Hash
- **FNV Hash**: 快速、简单，适合哈希表
- **MurmurHash**: 高性能，分布均匀
- **CityHash**: Google开发，速度极快
- **xxHash**: 极高性能，广泛使用

### 2. 密码学Hash
- **MD5**: 128位输出，已不安全
- **SHA-1**: 160位输出，已被破解
- **SHA-256**: 256位输出，目前安全
- **SHA-3**: 最新标准，高安全性

## Go语言实现

### 简单Hash函数
```go
// FNV-1a hash算法
func fnvHash(data []byte) uint32 {
    hash := uint32(2166136261)
    for _, b := range data {
        hash ^= uint32(b)
        hash *= 16777619
    }
    return hash
}

// 字符串hash
func stringHash(s string) uint32 {
    hash := uint32(0)
    for _, c := range s {
        hash = hash*31 + uint32(c)
    }
    return hash
}
```

### 使用标准库
```go
import (
    "crypto/md5"
    "crypto/sha256"
    "hash/fnv"
)

func hashExamples(data []byte) {
    // FNV hash
    h1 := fnv.New32a()
    h1.Write(data)
    fnvResult := h1.Sum32()
    
    // MD5 hash
    md5Result := md5.Sum(data)
    
    // SHA-256 hash
    sha256Result := sha256.Sum256(data)
}
```

## 应用场景

### 1. 哈希表
- **键值映射**: 将键映射到数组索引
- **冲突处理**: 链地址法、开放寻址法
- **负载因子**: 控制性能和空间平衡

### 2. 数据完整性
- **校验和**: 检测数据传输错误
- **数字指纹**: 文件去重、版本控制
- **数据验证**: 确保数据未被篡改

### 3. 密码存储
- **密码哈希**: 存储密码的哈希值而非明文
- **加盐处理**: 防止彩虹表攻击
- **慢哈希**: bcrypt、scrypt、Argon2

### 4. 分布式系统
- **一致性哈希**: 负载均衡、数据分片
- **布隆过滤器**: 快速判断元素是否存在
- **分布式缓存**: 数据分片和路由

## 哈希冲突处理

### 1. 链地址法
```go
type HashTable struct {
    buckets [][]KeyValue
    size    int
}

type KeyValue struct {
    key   string
    value interface{}
}

func (ht *HashTable) Put(key string, value interface{}) {
    index := hash(key) % len(ht.buckets)
    bucket := &ht.buckets[index]
    
    // 查找是否已存在
    for i, kv := range *bucket {
        if kv.key == key {
            (*bucket)[i].value = value
            return
        }
    }
    
    // 添加新元素
    *bucket = append(*bucket, KeyValue{key, value})
    ht.size++
}
```

### 2. 开放寻址法
```go
func (ht *HashTable) linearProbe(key string) int {
    index := hash(key) % len(ht.table)
    
    for ht.table[index] != nil {
        if ht.table[index].key == key {
            return index
        }
        index = (index + 1) % len(ht.table)
    }
    
    return index
}
```

## 性能考虑

### 1. 选择合适的Hash函数
- **速度要求**: 非密码学hash更快
- **安全要求**: 密码学hash更安全
- **分布要求**: 避免聚集现象

### 2. 负载因子控制
- **过低**: 浪费空间
- **过高**: 冲突增加，性能下降
- **最佳**: 通常0.75左右

### 3. 动态调整
- **扩容**: 负载因子过高时扩大表
- **缩容**: 负载因子过低时缩小表
- **重新哈希**: 调整大小时重新计算位置

## 面试要点

1. **基本原理**: 将任意输入映射为固定长度输出
2. **重要特性**: 确定性、高效性、均匀分布
3. **冲突处理**: 链地址法vs开放寻址法
4. **应用场景**: 哈希表、数据完整性、密码存储
5. **性能优化**: 选择合适算法、控制负载因子
