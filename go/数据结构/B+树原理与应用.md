# B+树原理与应用

## 基本概念

B+树是B树的变种，专门为磁盘存储和数据库索引设计的多路搜索树。

## 核心特点

### 与B树的区别
1. **数据存储**: 只有叶子节点存储数据，内部节点只存储键值
2. **叶子链表**: 所有叶子节点通过指针连接成有序链表
3. **键值冗余**: 内部节点的键值在叶子节点中会重复出现

### 结构特性
- 所有叶子节点在同一层
- 内部节点只存储索引，不存储数据
- 叶子节点存储所有数据记录
- 叶子节点间有指针连接

## 主要优势

### 1. 磁盘IO优化
- 内部节点不存储数据，单个节点可存储更多键值
- 减少磁盘IO次数，提高查询效率

### 2. 范围查询优化
- 叶子节点链表支持高效范围扫描
- 无需回溯到根节点

### 3. 缓存友好
- 内部节点可以缓存更多键值
- 提高缓存命中率

## Go语言实现要点

### 节点结构
```go
type BPlusTree struct {
    root   *Node
    degree int  // 树的度数
}

type Node struct {
    isLeaf   bool
    keys     []int
    children []*Node      // 内部节点的子指针
    data     []interface{} // 叶子节点的数据
    next     *Node        // 叶子节点的下一个节点指针
}
```

### 核心操作
- **查找**: 从根节点向下查找到叶子节点，时间复杂度O(log n)
- **插入**: 找到叶子节点插入，必要时分裂节点
- **范围查询**: 利用叶子节点链表进行高效扫描

## 性能分析

### 时间复杂度
- **查找**: O(log_m n)，m为树的度数
- **插入**: O(log_m n)
- **删除**: O(log_m n)
- **范围查询**: O(log_m n + k)，k为结果数量

### 空间复杂度
- **存储**: O(n)
- **内部节点**: 只存储键值，空间利用率高

## 数据库应用

### MySQL InnoDB
- **聚簇索引**: B+树叶子节点存储完整行数据
- **非聚簇索引**: B+树叶子节点存储主键值

### 索引优化策略
1. **覆盖索引**: 查询字段都在索引中
2. **最左前缀**: 复合索引的使用原则
3. **索引下推**: 在索引层面进行过滤

## 对比分析

| 特性 | B+树 | B树 | 红黑树 | 哈希表 |
|------|------|-----|--------|--------|
| 磁盘IO | 优秀 | 良好 | 差 | 差 |
| 范围查询 | 优秀 | 良好 | 良好 | 不支持 |
| 内存使用 | 高效 | 一般 | 低 | 一般 |
| 实现复杂度 | 高 | 高 | 中 | 低 |

## 面试要点
1. **基本特性**: 叶子节点存数据，内部节点存索引
2. **磁盘友好**: 减少磁盘IO，适合数据库索引
3. **范围查询**: 叶子节点链表支持高效范围扫描
4. **应用场景**: 数据库索引、文件系统
5. **性能特点**: O(log n)查找，优秀的范围查询性能
