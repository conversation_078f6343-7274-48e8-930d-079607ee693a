# 线段树与树状数组

## 线段树（Segment Tree）

### 基本概念
线段树是一种二叉树数据结构，用于高效处理区间查询和区间更新问题。

### 特点
- 每个节点代表一个区间
- 叶子节点代表单个元素
- 内部节点代表子区间的合并结果
- 支持O(log n)的查询和更新

### Go语言实现

#### 基础线段树
```go
type SegmentTree struct {
    tree []int
    n    int
}

func NewSegmentTree(arr []int) *SegmentTree {
    n := len(arr)
    tree := make([]int, 4*n)
    st := &SegmentTree{tree: tree, n: n}
    st.build(arr, 0, 0, n-1)
    return st
}

// 构建线段树
func (st *SegmentTree) build(arr []int, node, start, end int) {
    if start == end {
        st.tree[node] = arr[start]
    } else {
        mid := (start + end) / 2
        st.build(arr, 2*node+1, start, mid)
        st.build(arr, 2*node+2, mid+1, end)
        st.tree[node] = st.tree[2*node+1] + st.tree[2*node+2]
    }
}

// 区间查询
func (st *SegmentTree) Query(l, r int) int {
    return st.query(0, 0, st.n-1, l, r)
}

func (st *SegmentTree) query(node, start, end, l, r int) int {
    if r < start || end < l {
        return 0  // 无交集
    }
    if l <= start && end <= r {
        return st.tree[node]  // 完全包含
    }
    
    mid := (start + end) / 2
    leftSum := st.query(2*node+1, start, mid, l, r)
    rightSum := st.query(2*node+2, mid+1, end, l, r)
    return leftSum + rightSum
}

// 单点更新
func (st *SegmentTree) Update(idx, val int) {
    st.update(0, 0, st.n-1, idx, val)
}

func (st *SegmentTree) update(node, start, end, idx, val int) {
    if start == end {
        st.tree[node] = val
    } else {
        mid := (start + end) / 2
        if idx <= mid {
            st.update(2*node+1, start, mid, idx, val)
        } else {
            st.update(2*node+2, mid+1, end, idx, val)
        }
        st.tree[node] = st.tree[2*node+1] + st.tree[2*node+2]
    }
}
```

#### 懒惰传播线段树
```go
type LazySegmentTree struct {
    tree []int
    lazy []int
    n    int
}

func NewLazySegmentTree(arr []int) *LazySegmentTree {
    n := len(arr)
    tree := make([]int, 4*n)
    lazy := make([]int, 4*n)
    st := &LazySegmentTree{tree: tree, lazy: lazy, n: n}
    st.build(arr, 0, 0, n-1)
    return st
}

func (st *LazySegmentTree) build(arr []int, node, start, end int) {
    if start == end {
        st.tree[node] = arr[start]
    } else {
        mid := (start + end) / 2
        st.build(arr, 2*node+1, start, mid)
        st.build(arr, 2*node+2, mid+1, end)
        st.tree[node] = st.tree[2*node+1] + st.tree[2*node+2]
    }
}

// 推送懒惰标记
func (st *LazySegmentTree) pushDown(node, start, end int) {
    if st.lazy[node] != 0 {
        st.tree[node] += st.lazy[node] * (end - start + 1)
        
        if start != end {
            st.lazy[2*node+1] += st.lazy[node]
            st.lazy[2*node+2] += st.lazy[node]
        }
        
        st.lazy[node] = 0
    }
}

// 区间更新
func (st *LazySegmentTree) UpdateRange(l, r, val int) {
    st.updateRange(0, 0, st.n-1, l, r, val)
}

func (st *LazySegmentTree) updateRange(node, start, end, l, r, val int) {
    st.pushDown(node, start, end)
    
    if start > r || end < l {
        return
    }
    
    if start >= l && end <= r {
        st.lazy[node] += val
        st.pushDown(node, start, end)
        return
    }
    
    mid := (start + end) / 2
    st.updateRange(2*node+1, start, mid, l, r, val)
    st.updateRange(2*node+2, mid+1, end, l, r, val)
    
    st.pushDown(2*node+1, start, mid)
    st.pushDown(2*node+2, mid+1, end)
    st.tree[node] = st.tree[2*node+1] + st.tree[2*node+2]
}

// 区间查询
func (st *LazySegmentTree) QueryRange(l, r int) int {
    return st.queryRange(0, 0, st.n-1, l, r)
}

func (st *LazySegmentTree) queryRange(node, start, end, l, r int) int {
    if start > r || end < l {
        return 0
    }
    
    st.pushDown(node, start, end)
    
    if start >= l && end <= r {
        return st.tree[node]
    }
    
    mid := (start + end) / 2
    leftSum := st.queryRange(2*node+1, start, mid, l, r)
    rightSum := st.queryRange(2*node+2, mid+1, end, l, r)
    return leftSum + rightSum
}
```

## 树状数组（Binary Indexed Tree/Fenwick Tree）

### 基本概念
树状数组是一种支持单点更新和前缀和查询的数据结构，空间复杂度更低。

### Go语言实现
```go
type BIT struct {
    tree []int
    n    int
}

func NewBIT(n int) *BIT {
    return &BIT{
        tree: make([]int, n+1),
        n:    n,
    }
}

// 单点更新
func (bit *BIT) Update(i, delta int) {
    for i <= bit.n {
        bit.tree[i] += delta
        i += i & (-i)  // 加上lowbit
    }
}

// 前缀和查询
func (bit *BIT) Query(i int) int {
    sum := 0
    for i > 0 {
        sum += bit.tree[i]
        i -= i & (-i)  // 减去lowbit
    }
    return sum
}

// 区间和查询
func (bit *BIT) RangeQuery(l, r int) int {
    return bit.Query(r) - bit.Query(l-1)
}

// 从数组构建
func NewBITFromArray(arr []int) *BIT {
    n := len(arr)
    bit := NewBIT(n)
    for i, val := range arr {
        bit.Update(i+1, val)
    }
    return bit
}
```

### 二维树状数组
```go
type BIT2D struct {
    tree [][]int
    m, n int
}

func NewBIT2D(m, n int) *BIT2D {
    tree := make([][]int, m+1)
    for i := range tree {
        tree[i] = make([]int, n+1)
    }
    return &BIT2D{tree: tree, m: m, n: n}
}

func (bit *BIT2D) Update(x, y, delta int) {
    for i := x; i <= bit.m; i += i & (-i) {
        for j := y; j <= bit.n; j += j & (-j) {
            bit.tree[i][j] += delta
        }
    }
}

func (bit *BIT2D) Query(x, y int) int {
    sum := 0
    for i := x; i > 0; i -= i & (-i) {
        for j := y; j > 0; j -= j & (-j) {
            sum += bit.tree[i][j]
        }
    }
    return sum
}

func (bit *BIT2D) RangeQuery(x1, y1, x2, y2 int) int {
    return bit.Query(x2, y2) - bit.Query(x1-1, y2) - 
           bit.Query(x2, y1-1) + bit.Query(x1-1, y1-1)
}
```

## 经典应用

### 1. 区域和检索
```go
type NumArray struct {
    bit *BIT
}

func Constructor(nums []int) NumArray {
    return NumArray{bit: NewBITFromArray(nums)}
}

func (na *NumArray) Update(index int, val int) {
    oldVal := na.bit.RangeQuery(index+1, index+1)
    na.bit.Update(index+1, val-oldVal)
}

func (na *NumArray) SumRange(left int, right int) int {
    return na.bit.RangeQuery(left+1, right+1)
}
```

### 2. 逆序对计算
```go
func reversePairs(nums []int) int {
    // 离散化
    sorted := make([]int, len(nums))
    copy(sorted, nums)
    sort.Ints(sorted)
    
    // 去重
    unique := make([]int, 0)
    for i, num := range sorted {
        if i == 0 || num != sorted[i-1] {
            unique = append(unique, num)
        }
    }
    
    bit := NewBIT(len(unique))
    count := 0
    
    for i := len(nums) - 1; i >= 0; i-- {
        // 查找当前数字的位置
        pos := sort.SearchInts(unique, nums[i]) + 1
        
        // 查询比当前数字小的数量
        count += bit.Query(pos - 1)
        
        // 更新树状数组
        bit.Update(pos, 1)
    }
    
    return count
}
```

### 3. 动态排名
```go
type DynamicRank struct {
    bit    *BIT
    values []int
}

func NewDynamicRank() *DynamicRank {
    return &DynamicRank{
        bit:    NewBIT(100000),
        values: make([]int, 0),
    }
}

func (dr *DynamicRank) Insert(x int) {
    dr.values = append(dr.values, x)
    sort.Ints(dr.values)
    
    pos := sort.SearchInts(dr.values, x) + 1
    dr.bit.Update(pos, 1)
}

func (dr *DynamicRank) GetRank(x int) int {
    pos := sort.SearchInts(dr.values, x)
    if pos == len(dr.values) || dr.values[pos] != x {
        return -1
    }
    return dr.bit.Query(pos + 1)
}
```

## 时间复杂度对比

| 操作 | 线段树 | 树状数组 | 数组 |
|------|--------|----------|------|
| 构建 | O(n) | O(n log n) | O(1) |
| 单点更新 | O(log n) | O(log n) | O(1) |
| 区间更新 | O(log n) | O(log n) | O(n) |
| 区间查询 | O(log n) | O(log n) | O(n) |
| 空间复杂度 | O(4n) | O(n) | O(n) |

## 选择建议

### 使用线段树的场景
- 需要区间更新和区间查询
- 需要支持多种操作（最大值、最小值、和等）
- 需要懒惰传播优化

### 使用树状数组的场景
- 只需要前缀和查询
- 空间要求较严格
- 实现简单，代码量少

## 面试要点

1. **基本原理**: 线段树的区间分治思想，树状数组的lowbit操作
2. **时间复杂度**: 都是O(log n)的查询和更新
3. **空间复杂度**: 线段树4n，树状数组n
4. **应用场景**: 区间查询、动态排名、逆序对
5. **优化技巧**: 懒惰传播、离散化、二维扩展
