# 跳表原理与实现

## 什么是跳表？

跳表(Skip List)是一种随机化数据结构，可以看作是有序链表的扩展，通过维护多级索引来实现快速查找。

## 基本原理

### 多级索引结构
```
Level 3: 1 ---------> 7 ---------> 19
Level 2: 1 -----> 4 -> 7 -----> 13 -> 19
Level 1: 1 -> 2 -> 4 -> 7 -> 8 -> 13 -> 19 -> 21
Level 0: 1 -> 2 -> 4 -> 7 -> 8 -> 13 -> 19 -> 21 -> 25 -> 28
```

### 随机层数生成
每个节点的层数通过抛硬币决定，概率为0.5

## 核心操作

### 1. 查找操作
- 从最高层开始
- 每层水平移动直到下一个节点大于目标
- 下降到下一层继续查找
- 时间复杂度：O(log n)

### 2. 插入操作
- 先查找插入位置
- 随机生成新节点层数
- 更新各层指针连接
- 时间复杂度：O(log n)

### 3. 删除操作
- 查找目标节点
- 更新各层指针跳过目标节点
- 调整跳表层数
- 时间复杂度：O(log n)

## Go语言实现

### 基本结构
```go
const (
    MaxLevel = 16
    P        = 0.5
)

type SkipListNode struct {
    key     int
    value   interface{}
    forward []*SkipListNode
}

type SkipList struct {
    header *SkipListNode
    level  int
}

func NewSkipList() *SkipList {
    header := &SkipListNode{
        forward: make([]*SkipListNode, MaxLevel),
    }
    return &SkipList{
        header: header,
        level:  0,
    }
}
```

### 随机层数生成
```go
func randomLevel() int {
    level := 1
    for rand.Float64() < P && level < MaxLevel {
        level++
    }
    return level
}
```

### 查找操作
```go
func (sl *SkipList) Search(key int) interface{} {
    current := sl.header

    // 从最高层开始查找
    for i := sl.level - 1; i >= 0; i-- {
        for current.forward[i] != nil && current.forward[i].key < key {
            current = current.forward[i]
        }
    }

    current = current.forward[0]
    if current != nil && current.key == key {
        return current.value
    }

    return nil
}
```

### 插入操作
```go
func (sl *SkipList) Insert(key int, value interface{}) {
    update := make([]*SkipListNode, MaxLevel)
    current := sl.header

    // 查找插入位置
    for i := sl.level - 1; i >= 0; i-- {
        for current.forward[i] != nil && current.forward[i].key < key {
            current = current.forward[i]
        }
        update[i] = current
    }

    current = current.forward[0]

    // 如果key已存在，更新值
    if current != nil && current.key == key {
        current.value = value
        return
    }

    // 生成新节点层数
    newLevel := randomLevel()

    // 如果新层数大于当前层数，更新header
    if newLevel > sl.level {
        for i := sl.level; i < newLevel; i++ {
            update[i] = sl.header
        }
        sl.level = newLevel
    }

    // 创建新节点
    newNode := &SkipListNode{
        key:     key,
        value:   value,
        forward: make([]*SkipListNode, newLevel),
    }

    // 更新指针
    for i := 0; i < newLevel; i++ {
        newNode.forward[i] = update[i].forward[i]
        update[i].forward[i] = newNode
    }
}
```

### 删除操作
```go
func (sl *SkipList) Delete(key int) bool {
    update := make([]*SkipListNode, MaxLevel)
    current := sl.header

    // 查找删除位置
    for i := sl.level - 1; i >= 0; i-- {
        for current.forward[i] != nil && current.forward[i].key < key {
            current = current.forward[i]
        }
        update[i] = current
    }

    current = current.forward[0]

    if current == nil || current.key != key {
        return false
    }

    // 更新指针
    for i := 0; i < sl.level; i++ {
        if update[i].forward[i] != current {
            break
        }
        update[i].forward[i] = current.forward[i]
    }

    // 调整层数
    for sl.level > 1 && sl.header.forward[sl.level-1] == nil {
        sl.level--
    }

    return true
}
```



## 性能分析

### 时间复杂度
- **查找**: O(log n) 期望时间
- **插入**: O(log n) 期望时间  
- **删除**: O(log n) 期望时间

### 空间复杂度
- **空间**: O(n) 期望空间，每个节点平均2个指针

### 概率分析
- 第k层节点数期望为 n/2^k
- 总层数期望为 log₂(n)
- 每次查找期望比较次数为 2log₂(n)

## 跳表的优势

### 相比红黑树
1. **实现简单**: 无需复杂的旋转操作
2. **并发友好**: 更容易实现无锁并发
3. **内存局部性**: 顺序访问性能更好

### 相比哈希表
1. **有序性**: 支持范围查询
2. **稳定性**: 性能不依赖哈希函数质量
3. **动态性**: 无需rehash操作

## 实际应用

### Redis中的应用
```go
// Redis ZSet的跳表实现
type ZSkipListNode struct {
    member string
    score  float64
    backward *ZSkipListNode
    level    []ZSkipListLevel
}

type ZSkipListLevel struct {
    forward *ZSkipListNode
    span    int  // 跨度，用于计算rank
}
```

### LevelDB中的应用
- MemTable使用跳表存储键值对
- 支持高效的插入和有序遍历

## 面试要点
1. **基本原理**: 多级索引，随机化层数
2. **时间复杂度**: 期望O(log n)的查找、插入、删除
3. **空间复杂度**: 期望O(n)，每个节点平均2个指针
4. **应用场景**: Redis ZSet、LevelDB MemTable
5. **优势**: 实现简单，并发友好，支持范围查询
