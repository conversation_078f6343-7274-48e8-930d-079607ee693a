# 栈和队列实现

## 栈（Stack）

### 基本概念
栈是一种后进先出（LIFO）的数据结构，只能在一端进行插入和删除操作。

### Go语言实现
```go
type Stack struct {
    items []int
}

func NewStack() *Stack {
    return &Stack{items: make([]int, 0)}
}

// 入栈
func (s *Stack) Push(item int) {
    s.items = append(s.items, item)
}

// 出栈
func (s *Stack) Pop() (int, bool) {
    if len(s.items) == 0 {
        return 0, false
    }
    
    index := len(s.items) - 1
    item := s.items[index]
    s.items = s.items[:index]
    return item, true
}

// 查看栈顶
func (s *Stack) Peek() (int, bool) {
    if len(s.items) == 0 {
        return 0, false
    }
    return s.items[len(s.items)-1], true
}

// 判断是否为空
func (s *Stack) IsEmpty() bool {
    return len(s.items) == 0
}

// 栈大小
func (s *Stack) Size() int {
    return len(s.items)
}
```

### 栈的应用

#### 1. 括号匹配
```go
func isValid(s string) bool {
    stack := NewStack()
    pairs := map[rune]rune{
        ')': '(',
        ']': '[',
        '}': '{',
    }
    
    for _, char := range s {
        if char == '(' || char == '[' || char == '{' {
            stack.Push(int(char))
        } else if char == ')' || char == ']' || char == '}' {
            if stack.IsEmpty() {
                return false
            }
            
            top, _ := stack.Pop()
            if rune(top) != pairs[char] {
                return false
            }
        }
    }
    
    return stack.IsEmpty()
}
```

#### 2. 表达式求值
```go
func evalRPN(tokens []string) int {
    stack := NewStack()
    
    for _, token := range tokens {
        if token == "+" || token == "-" || token == "*" || token == "/" {
            b, _ := stack.Pop()
            a, _ := stack.Pop()
            
            var result int
            switch token {
            case "+":
                result = a + b
            case "-":
                result = a - b
            case "*":
                result = a * b
            case "/":
                result = a / b
            }
            
            stack.Push(result)
        } else {
            num, _ := strconv.Atoi(token)
            stack.Push(num)
        }
    }
    
    result, _ := stack.Pop()
    return result
}
```

#### 3. 单调栈
```go
// 下一个更大元素
func nextGreaterElement(nums []int) []int {
    result := make([]int, len(nums))
    stack := NewStack()
    
    for i := len(nums) - 1; i >= 0; i-- {
        // 维护单调递减栈
        for !stack.IsEmpty() {
            top, _ := stack.Peek()
            if top <= nums[i] {
                stack.Pop()
            } else {
                break
            }
        }
        
        if stack.IsEmpty() {
            result[i] = -1
        } else {
            result[i], _ = stack.Peek()
        }
        
        stack.Push(nums[i])
    }
    
    return result
}
```

## 队列（Queue）

### 基本概念
队列是一种先进先出（FIFO）的数据结构，在一端插入，另一端删除。

### Go语言实现
```go
type Queue struct {
    items []int
}

func NewQueue() *Queue {
    return &Queue{items: make([]int, 0)}
}

// 入队
func (q *Queue) Enqueue(item int) {
    q.items = append(q.items, item)
}

// 出队
func (q *Queue) Dequeue() (int, bool) {
    if len(q.items) == 0 {
        return 0, false
    }
    
    item := q.items[0]
    q.items = q.items[1:]
    return item, true
}

// 查看队首
func (q *Queue) Front() (int, bool) {
    if len(q.items) == 0 {
        return 0, false
    }
    return q.items[0], true
}

// 判断是否为空
func (q *Queue) IsEmpty() bool {
    return len(q.items) == 0
}

// 队列大小
func (q *Queue) Size() int {
    return len(q.items)
}
```

### 循环队列
```go
type CircularQueue struct {
    data  []int
    front int
    rear  int
    size  int
    cap   int
}

func NewCircularQueue(k int) *CircularQueue {
    return &CircularQueue{
        data: make([]int, k),
        cap:  k,
    }
}

func (q *CircularQueue) EnQueue(value int) bool {
    if q.IsFull() {
        return false
    }
    
    q.data[q.rear] = value
    q.rear = (q.rear + 1) % q.cap
    q.size++
    return true
}

func (q *CircularQueue) DeQueue() bool {
    if q.IsEmpty() {
        return false
    }
    
    q.front = (q.front + 1) % q.cap
    q.size--
    return true
}

func (q *CircularQueue) Front() int {
    if q.IsEmpty() {
        return -1
    }
    return q.data[q.front]
}

func (q *CircularQueue) Rear() int {
    if q.IsEmpty() {
        return -1
    }
    return q.data[(q.rear-1+q.cap)%q.cap]
}

func (q *CircularQueue) IsEmpty() bool {
    return q.size == 0
}

func (q *CircularQueue) IsFull() bool {
    return q.size == q.cap
}
```

### 双端队列
```go
type Deque struct {
    items []int
}

func NewDeque() *Deque {
    return &Deque{items: make([]int, 0)}
}

// 前端插入
func (d *Deque) PushFront(item int) {
    d.items = append([]int{item}, d.items...)
}

// 后端插入
func (d *Deque) PushBack(item int) {
    d.items = append(d.items, item)
}

// 前端删除
func (d *Deque) PopFront() (int, bool) {
    if len(d.items) == 0 {
        return 0, false
    }
    
    item := d.items[0]
    d.items = d.items[1:]
    return item, true
}

// 后端删除
func (d *Deque) PopBack() (int, bool) {
    if len(d.items) == 0 {
        return 0, false
    }
    
    index := len(d.items) - 1
    item := d.items[index]
    d.items = d.items[:index]
    return item, true
}
```

### 队列的应用

#### 1. BFS遍历
```go
func bfs(graph [][]int, start int) []int {
    visited := make([]bool, len(graph))
    result := make([]int, 0)
    queue := NewQueue()
    
    queue.Enqueue(start)
    visited[start] = true
    
    for !queue.IsEmpty() {
        node, _ := queue.Dequeue()
        result = append(result, node)
        
        for _, neighbor := range graph[node] {
            if !visited[neighbor] {
                visited[neighbor] = true
                queue.Enqueue(neighbor)
            }
        }
    }
    
    return result
}
```

#### 2. 滑动窗口最大值
```go
func maxSlidingWindow(nums []int, k int) []int {
    deque := NewDeque()
    result := make([]int, 0)
    
    for i, num := range nums {
        // 移除窗口外的元素
        for !deque.IsEmpty() {
            front, _ := deque.Front()
            if front <= i-k {
                deque.PopFront()
            } else {
                break
            }
        }
        
        // 维护单调递减队列
        for !deque.IsEmpty() {
            back, _ := deque.Back()
            if nums[back] <= num {
                deque.PopBack()
            } else {
                break
            }
        }
        
        deque.PushBack(i)
        
        // 窗口形成后记录最大值
        if i >= k-1 {
            front, _ := deque.Front()
            result = append(result, nums[front])
        }
    }
    
    return result
}
```

## 时间复杂度对比

| 操作 | 栈 | 队列 | 双端队列 |
|------|----|----- |----------|
| 插入 | O(1) | O(1) | O(1) |
| 删除 | O(1) | O(1) | O(1) |
| 查看 | O(1) | O(1) | O(1) |
| 搜索 | O(n) | O(n) | O(n) |

## 面试要点

1. **基本概念**: 栈LIFO，队列FIFO
2. **实现方式**: 数组、链表、切片
3. **应用场景**: 
   - 栈：括号匹配、表达式求值、函数调用、单调栈
   - 队列：BFS、任务调度、缓冲区、滑动窗口
4. **变种**: 循环队列、双端队列、优先队列
5. **性能特点**: 基本操作都是O(1)时间复杂度
