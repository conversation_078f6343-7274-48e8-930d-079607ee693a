# 并查集详解

## 基本概念

并查集（Union-Find）是一种树型数据结构，用于处理一些不相交集合的合并及查询问题。

### 核心操作
1. **Find**: 查找元素所属的集合（根节点）
2. **Union**: 合并两个集合
3. **Connected**: 判断两个元素是否在同一集合

## Go语言实现

### 基础版本
```go
type UnionFind struct {
    parent []int
    count  int  // 连通分量数量
}

func NewUnionFind(n int) *UnionFind {
    parent := make([]int, n)
    for i := range parent {
        parent[i] = i
    }
    return &UnionFind{
        parent: parent,
        count:  n,
    }
}

// 查找根节点
func (uf *UnionFind) Find(x int) int {
    if uf.parent[x] != x {
        return uf.Find(uf.parent[x])
    }
    return x
}

// 合并两个集合
func (uf *UnionFind) Union(x, y int) {
    rootX := uf.Find(x)
    rootY := uf.Find(y)
    
    if rootX != rootY {
        uf.parent[rootX] = rootY
        uf.count--
    }
}

// 判断是否连通
func (uf *UnionFind) Connected(x, y int) bool {
    return uf.Find(x) == uf.Find(y)
}

// 获取连通分量数量
func (uf *UnionFind) Count() int {
    return uf.count
}
```

### 路径压缩优化
```go
func (uf *UnionFind) Find(x int) int {
    if uf.parent[x] != x {
        uf.parent[x] = uf.Find(uf.parent[x])  // 路径压缩
    }
    return uf.parent[x]
}
```

### 按秩合并优化
```go
type UnionFindOptimized struct {
    parent []int
    rank   []int  // 树的高度
    count  int
}

func NewUnionFindOptimized(n int) *UnionFindOptimized {
    parent := make([]int, n)
    rank := make([]int, n)
    for i := range parent {
        parent[i] = i
        rank[i] = 1
    }
    return &UnionFindOptimized{
        parent: parent,
        rank:   rank,
        count:  n,
    }
}

func (uf *UnionFindOptimized) Find(x int) int {
    if uf.parent[x] != x {
        uf.parent[x] = uf.Find(uf.parent[x])
    }
    return uf.parent[x]
}

func (uf *UnionFindOptimized) Union(x, y int) {
    rootX := uf.Find(x)
    rootY := uf.Find(y)
    
    if rootX == rootY {
        return
    }
    
    // 按秩合并：将较小的树合并到较大的树
    if uf.rank[rootX] < uf.rank[rootY] {
        uf.parent[rootX] = rootY
    } else if uf.rank[rootX] > uf.rank[rootY] {
        uf.parent[rootY] = rootX
    } else {
        uf.parent[rootY] = rootX
        uf.rank[rootX]++
    }
    
    uf.count--
}
```

### 带权重的并查集
```go
type WeightedUnionFind struct {
    parent []int
    weight []int  // 到父节点的权重
}

func NewWeightedUnionFind(n int) *WeightedUnionFind {
    parent := make([]int, n)
    weight := make([]int, n)
    for i := range parent {
        parent[i] = i
        weight[i] = 0
    }
    return &WeightedUnionFind{
        parent: parent,
        weight: weight,
    }
}

func (wuf *WeightedUnionFind) Find(x int) int {
    if wuf.parent[x] != x {
        root := wuf.Find(wuf.parent[x])
        wuf.weight[x] += wuf.weight[wuf.parent[x]]
        wuf.parent[x] = root
    }
    return wuf.parent[x]
}

func (wuf *WeightedUnionFind) Union(x, y, w int) {
    rootX := wuf.Find(x)
    rootY := wuf.Find(y)
    
    if rootX != rootY {
        wuf.parent[rootX] = rootY
        wuf.weight[rootX] = wuf.weight[y] - wuf.weight[x] + w
    }
}

func (wuf *WeightedUnionFind) Diff(x, y int) int {
    if wuf.Find(x) != wuf.Find(y) {
        return 0  // 不在同一集合
    }
    return wuf.weight[x] - wuf.weight[y]
}
```

## 经典应用

### 1. 岛屿数量
```go
func numIslands(grid [][]byte) int {
    if len(grid) == 0 || len(grid[0]) == 0 {
        return 0
    }
    
    m, n := len(grid), len(grid[0])
    uf := NewUnionFind(m * n)
    count := 0
    
    // 统计陆地数量
    for i := 0; i < m; i++ {
        for j := 0; j < n; j++ {
            if grid[i][j] == '1' {
                count++
            }
        }
    }
    
    uf.count = count
    directions := [][]int{{0, 1}, {1, 0}, {0, -1}, {-1, 0}}
    
    for i := 0; i < m; i++ {
        for j := 0; j < n; j++ {
            if grid[i][j] == '1' {
                for _, dir := range directions {
                    ni, nj := i+dir[0], j+dir[1]
                    if ni >= 0 && ni < m && nj >= 0 && nj < n && grid[ni][nj] == '1' {
                        uf.Union(i*n+j, ni*n+nj)
                    }
                }
            }
        }
    }
    
    return uf.Count()
}
```

### 2. 朋友圈
```go
func findCircleNum(isConnected [][]int) int {
    n := len(isConnected)
    uf := NewUnionFind(n)
    
    for i := 0; i < n; i++ {
        for j := i + 1; j < n; j++ {
            if isConnected[i][j] == 1 {
                uf.Union(i, j)
            }
        }
    }
    
    return uf.Count()
}
```

### 3. 冗余连接
```go
func findRedundantConnection(edges [][]int) []int {
    n := len(edges)
    uf := NewUnionFind(n + 1)
    
    for _, edge := range edges {
        u, v := edge[0], edge[1]
        if uf.Connected(u, v) {
            return edge  // 找到冗余边
        }
        uf.Union(u, v)
    }
    
    return nil
}
```

### 4. 账户合并
```go
func accountsMerge(accounts [][]string) [][]string {
    emailToIndex := make(map[string]int)
    emailToName := make(map[string]string)
    index := 0
    
    // 为每个邮箱分配索引
    for _, account := range accounts {
        name := account[0]
        for i := 1; i < len(account); i++ {
            email := account[i]
            if _, exists := emailToIndex[email]; !exists {
                emailToIndex[email] = index
                emailToName[email] = name
                index++
            }
        }
    }
    
    uf := NewUnionFind(index)
    
    // 合并同一账户下的邮箱
    for _, account := range accounts {
        firstEmailIndex := emailToIndex[account[1]]
        for i := 2; i < len(account); i++ {
            uf.Union(firstEmailIndex, emailToIndex[account[i]])
        }
    }
    
    // 按根节点分组
    groups := make(map[int][]string)
    for email, idx := range emailToIndex {
        root := uf.Find(idx)
        groups[root] = append(groups[root], email)
    }
    
    // 构建结果
    result := make([][]string, 0)
    for _, emails := range groups {
        sort.Strings(emails)
        name := emailToName[emails[0]]
        account := append([]string{name}, emails...)
        result = append(result, account)
    }
    
    return result
}
```

### 5. 等式方程的可满足性
```go
func equationsPossible(equations []string) bool {
    uf := NewUnionFind(26)
    
    // 处理相等关系
    for _, eq := range equations {
        if eq[1] == '=' {
            x := int(eq[0] - 'a')
            y := int(eq[3] - 'a')
            uf.Union(x, y)
        }
    }
    
    // 检查不等关系
    for _, eq := range equations {
        if eq[1] == '!' {
            x := int(eq[0] - 'a')
            y := int(eq[3] - 'a')
            if uf.Connected(x, y) {
                return false
            }
        }
    }
    
    return true
}
```

## 时间复杂度分析

### 优化前
- **Find**: O(n) 最坏情况
- **Union**: O(n) 最坏情况

### 优化后（路径压缩 + 按秩合并）
- **Find**: O(α(n)) 平摊时间
- **Union**: O(α(n)) 平摊时间

其中 α(n) 是阿克曼函数的反函数，在实际应用中可以认为是常数。

## 应用场景

1. **连通性问题**: 判断图中两点是否连通
2. **动态连通性**: 动态添加边并查询连通性
3. **最小生成树**: Kruskal算法中检测环
4. **社交网络**: 朋友关系、社区发现
5. **图像处理**: 连通区域标记
6. **网络连接**: 网络拓扑分析

## 面试要点

1. **基本操作**: Find和Union的实现
2. **优化技巧**: 路径压缩和按秩合并
3. **时间复杂度**: 优化后接近O(1)
4. **应用场景**: 连通性问题、动态图问题
5. **变种**: 带权重并查集、可撤销并查集
