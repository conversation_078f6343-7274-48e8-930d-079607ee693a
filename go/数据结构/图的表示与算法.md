# 图的表示与算法

## 基本概念

图是由顶点（Vertex）和边（Edge）组成的数据结构，用于表示对象之间的关系。

### 图的分类
- **有向图 vs 无向图**: 边是否有方向
- **加权图 vs 无权图**: 边是否有权重
- **连通图 vs 非连通图**: 是否所有顶点都可达
- **简单图 vs 多重图**: 是否允许重边和自环

## 图的表示方法

### 1. 邻接矩阵
```go
type AdjMatrix struct {
    vertices int
    matrix   [][]int
}

func NewAdjMatrix(v int) *AdjMatrix {
    matrix := make([][]int, v)
    for i := range matrix {
        matrix[i] = make([]int, v)
    }
    return &AdjMatrix{
        vertices: v,
        matrix:   matrix,
    }
}

// 添加边
func (g *AdjMatrix) AddEdge(u, v int) {
    g.matrix[u][v] = 1
    g.matrix[v][u] = 1  // 无向图
}

// 添加加权边
func (g *AdjMatrix) AddWeightedEdge(u, v, weight int) {
    g.matrix[u][v] = weight
    g.matrix[v][u] = weight  // 无向图
}

// 检查边是否存在
func (g *AdjMatrix) HasEdge(u, v int) bool {
    return g.matrix[u][v] != 0
}
```

### 2. 邻接表
```go
type AdjList struct {
    vertices int
    adjList  [][]int
}

func NewAdjList(v int) *AdjList {
    adjList := make([][]int, v)
    for i := range adjList {
        adjList[i] = make([]int, 0)
    }
    return &AdjList{
        vertices: v,
        adjList:  adjList,
    }
}

// 添加边
func (g *AdjList) AddEdge(u, v int) {
    g.adjList[u] = append(g.adjList[u], v)
    g.adjList[v] = append(g.adjList[v], u)  // 无向图
}

// 获取邻居
func (g *AdjList) GetNeighbors(u int) []int {
    return g.adjList[u]
}
```

### 3. 边列表
```go
type Edge struct {
    from   int
    to     int
    weight int
}

type EdgeList struct {
    vertices int
    edges    []Edge
}

func NewEdgeList(v int) *EdgeList {
    return &EdgeList{
        vertices: v,
        edges:    make([]Edge, 0),
    }
}

func (g *EdgeList) AddEdge(u, v, weight int) {
    g.edges = append(g.edges, Edge{u, v, weight})
}
```

## 图的遍历算法

### 1. 深度优先搜索（DFS）
```go
func (g *AdjList) DFS(start int) []int {
    visited := make([]bool, g.vertices)
    result := make([]int, 0)
    
    g.dfsHelper(start, visited, &result)
    return result
}

func (g *AdjList) dfsHelper(v int, visited []bool, result *[]int) {
    visited[v] = true
    *result = append(*result, v)
    
    for _, neighbor := range g.adjList[v] {
        if !visited[neighbor] {
            g.dfsHelper(neighbor, visited, result)
        }
    }
}

// 迭代版本DFS
func (g *AdjList) DFSIterative(start int) []int {
    visited := make([]bool, g.vertices)
    result := make([]int, 0)
    stack := []int{start}
    
    for len(stack) > 0 {
        v := stack[len(stack)-1]
        stack = stack[:len(stack)-1]
        
        if !visited[v] {
            visited[v] = true
            result = append(result, v)
            
            // 逆序添加邻居以保持顺序
            neighbors := g.adjList[v]
            for i := len(neighbors) - 1; i >= 0; i-- {
                if !visited[neighbors[i]] {
                    stack = append(stack, neighbors[i])
                }
            }
        }
    }
    
    return result
}
```

### 2. 广度优先搜索（BFS）
```go
func (g *AdjList) BFS(start int) []int {
    visited := make([]bool, g.vertices)
    result := make([]int, 0)
    queue := []int{start}
    visited[start] = true
    
    for len(queue) > 0 {
        v := queue[0]
        queue = queue[1:]
        result = append(result, v)
        
        for _, neighbor := range g.adjList[v] {
            if !visited[neighbor] {
                visited[neighbor] = true
                queue = append(queue, neighbor)
            }
        }
    }
    
    return result
}
```

## 最短路径算法

### 1. Dijkstra算法
```go
import "container/heap"

type Item struct {
    vertex   int
    distance int
    index    int
}

type PriorityQueue []*Item

func (pq PriorityQueue) Len() int { return len(pq) }
func (pq PriorityQueue) Less(i, j int) bool {
    return pq[i].distance < pq[j].distance
}
func (pq PriorityQueue) Swap(i, j int) {
    pq[i], pq[j] = pq[j], pq[i]
    pq[i].index = i
    pq[j].index = j
}

func (pq *PriorityQueue) Push(x interface{}) {
    n := len(*pq)
    item := x.(*Item)
    item.index = n
    *pq = append(*pq, item)
}

func (pq *PriorityQueue) Pop() interface{} {
    old := *pq
    n := len(old)
    item := old[n-1]
    old[n-1] = nil
    item.index = -1
    *pq = old[0 : n-1]
    return item
}

func dijkstra(graph [][]int, start int) []int {
    n := len(graph)
    dist := make([]int, n)
    visited := make([]bool, n)
    
    // 初始化距离
    for i := range dist {
        dist[i] = math.MaxInt32
    }
    dist[start] = 0
    
    pq := &PriorityQueue{}
    heap.Init(pq)
    heap.Push(pq, &Item{vertex: start, distance: 0})
    
    for pq.Len() > 0 {
        item := heap.Pop(pq).(*Item)
        u := item.vertex
        
        if visited[u] {
            continue
        }
        visited[u] = true
        
        for v := 0; v < n; v++ {
            if graph[u][v] > 0 && !visited[v] {
                newDist := dist[u] + graph[u][v]
                if newDist < dist[v] {
                    dist[v] = newDist
                    heap.Push(pq, &Item{vertex: v, distance: newDist})
                }
            }
        }
    }
    
    return dist
}
```

### 2. Floyd-Warshall算法
```go
func floydWarshall(graph [][]int) [][]int {
    n := len(graph)
    dist := make([][]int, n)
    
    // 初始化距离矩阵
    for i := 0; i < n; i++ {
        dist[i] = make([]int, n)
        for j := 0; j < n; j++ {
            if i == j {
                dist[i][j] = 0
            } else if graph[i][j] != 0 {
                dist[i][j] = graph[i][j]
            } else {
                dist[i][j] = math.MaxInt32
            }
        }
    }
    
    // Floyd-Warshall核心算法
    for k := 0; k < n; k++ {
        for i := 0; i < n; i++ {
            for j := 0; j < n; j++ {
                if dist[i][k] != math.MaxInt32 && 
                   dist[k][j] != math.MaxInt32 &&
                   dist[i][k]+dist[k][j] < dist[i][j] {
                    dist[i][j] = dist[i][k] + dist[k][j]
                }
            }
        }
    }
    
    return dist
}
```

## 最小生成树算法

### Kruskal算法
```go
type UnionFind struct {
    parent []int
    rank   []int
}

func NewUnionFind(n int) *UnionFind {
    parent := make([]int, n)
    rank := make([]int, n)
    for i := range parent {
        parent[i] = i
    }
    return &UnionFind{parent, rank}
}

func (uf *UnionFind) Find(x int) int {
    if uf.parent[x] != x {
        uf.parent[x] = uf.Find(uf.parent[x])
    }
    return uf.parent[x]
}

func (uf *UnionFind) Union(x, y int) bool {
    rootX := uf.Find(x)
    rootY := uf.Find(y)
    
    if rootX == rootY {
        return false
    }
    
    if uf.rank[rootX] < uf.rank[rootY] {
        uf.parent[rootX] = rootY
    } else if uf.rank[rootX] > uf.rank[rootY] {
        uf.parent[rootY] = rootX
    } else {
        uf.parent[rootY] = rootX
        uf.rank[rootX]++
    }
    
    return true
}

func kruskal(edges []Edge, vertices int) []Edge {
    // 按权重排序
    sort.Slice(edges, func(i, j int) bool {
        return edges[i].weight < edges[j].weight
    })
    
    uf := NewUnionFind(vertices)
    mst := make([]Edge, 0)
    
    for _, edge := range edges {
        if uf.Union(edge.from, edge.to) {
            mst = append(mst, edge)
            if len(mst) == vertices-1 {
                break
            }
        }
    }
    
    return mst
}
```

## 拓扑排序
```go
func topologicalSort(graph [][]int) []int {
    n := len(graph)
    inDegree := make([]int, n)
    
    // 计算入度
    for i := 0; i < n; i++ {
        for j := 0; j < n; j++ {
            if graph[i][j] == 1 {
                inDegree[j]++
            }
        }
    }
    
    queue := make([]int, 0)
    result := make([]int, 0)
    
    // 将入度为0的节点加入队列
    for i := 0; i < n; i++ {
        if inDegree[i] == 0 {
            queue = append(queue, i)
        }
    }
    
    for len(queue) > 0 {
        u := queue[0]
        queue = queue[1:]
        result = append(result, u)
        
        // 更新邻居的入度
        for v := 0; v < n; v++ {
            if graph[u][v] == 1 {
                inDegree[v]--
                if inDegree[v] == 0 {
                    queue = append(queue, v)
                }
            }
        }
    }
    
    if len(result) != n {
        return nil  // 存在环
    }
    
    return result
}
```

## 时间复杂度对比

| 算法 | 时间复杂度 | 空间复杂度 | 适用场景 |
|------|------------|------------|----------|
| DFS | O(V+E) | O(V) | 路径查找、连通性 |
| BFS | O(V+E) | O(V) | 最短路径、层次遍历 |
| Dijkstra | O((V+E)logV) | O(V) | 单源最短路径 |
| Floyd-Warshall | O(V³) | O(V²) | 全源最短路径 |
| Kruskal | O(ElogE) | O(V) | 最小生成树 |
| 拓扑排序 | O(V+E) | O(V) | 依赖关系排序 |

## 面试要点

1. **图的表示**: 邻接矩阵vs邻接表的优缺点
2. **遍历算法**: DFS和BFS的实现和应用
3. **最短路径**: Dijkstra和Floyd-Warshall的区别
4. **最小生成树**: Kruskal和Prim算法
5. **拓扑排序**: 检测环和依赖关系处理
