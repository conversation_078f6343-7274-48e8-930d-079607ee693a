# 二叉树遍历与应用

## 二叉树基础

### 1. 基本概念
- **节点**：包含数据和指向子节点的指针
- **根节点**：树的顶部节点
- **叶子节点**：没有子节点的节点
- **深度**：从根到节点的路径长度
- **高度**：从节点到叶子的最长路径

### 2. 二叉树类型
- **满二叉树**：每层都完全填满
- **完全二叉树**：除最后一层外都填满，最后一层从左到右填充
- **平衡二叉树**：左右子树高度差不超过1
- **二叉搜索树**：左子树 < 根 < 右子树

## 遍历算法

### Go语言实现

```go
type TreeNode struct {
    Val   int
    Left  *TreeNode
    Right *TreeNode
}
```

### 1. 深度优先遍历（DFS）

#### 前序遍历（根-左-右）
```go
// 递归实现
func preorderTraversal(root *TreeNode) []int {
    var result []int
    var preorder func(*TreeNode)
    preorder = func(node *TreeNode) {
        if node == nil {
            return
        }
        result = append(result, node.Val)
        preorder(node.Left)
        preorder(node.Right)
    }
    preorder(root)
    return result
}

// 迭代实现
func preorderIterative(root *TreeNode) []int {
    if root == nil {
        return nil
    }

    var result []int
    stack := []*TreeNode{root}

    for len(stack) > 0 {
        node := stack[len(stack)-1]
        stack = stack[:len(stack)-1]

        result = append(result, node.Val)

        if node.Right != nil {
            stack = append(stack, node.Right)
        }
        if node.Left != nil {
            stack = append(stack, node.Left)
        }
    }

    return result
}
```

#### 中序遍历（左-根-右）
```go
func inorderTraversal(root *TreeNode) []int {
    var result []int
    var inorder func(*TreeNode)
    inorder = func(node *TreeNode) {
        if node == nil {
            return
        }
        inorder(node.Left)
        result = append(result, node.Val)
        inorder(node.Right)
    }
    inorder(root)
    return result
}
```

#### 后序遍历（左-右-根）
```go
func postorderTraversal(root *TreeNode) []int {
    var result []int
    var postorder func(*TreeNode)
    postorder = func(node *TreeNode) {
        if node == nil {
            return
        }
        postorder(node.Left)
        postorder(node.Right)
        result = append(result, node.Val)
    }
    postorder(root)
    return result
}
```

### 2. 广度优先遍历（BFS）

#### 层序遍历
```go
func levelOrder(root *TreeNode) [][]int {
    if root == nil {
        return nil
    }

    var result [][]int
    queue := []*TreeNode{root}

    for len(queue) > 0 {
        levelSize := len(queue)
        var level []int

        for i := 0; i < levelSize; i++ {
            node := queue[0]
            queue = queue[1:]

            level = append(level, node.Val)

            if node.Left != nil {
                queue = append(queue, node.Left)
            }
            if node.Right != nil {
                queue = append(queue, node.Right)
            }
        }

        result = append(result, level)
    }

    return result
}
```

## 常见算法问题

### 1. 树的属性
- **最大深度**：递归计算左右子树深度
- **最小深度**：到最近叶子节点的距离
- **是否平衡**：检查左右子树高度差
- **是否对称**：镜像对称检查

### 2. 路径问题
- **路径总和**：是否存在根到叶子路径和等于目标值
- **最大路径和**：任意节点到任意节点的最大路径和
- **路径数量**：满足条件的路径数量

### 3. 构造问题
- **从遍历序列构造树**：前序+中序、后序+中序
- **最大二叉树**：从数组构造最大值为根的树
- **平衡BST**：从有序数组构造平衡BST

## 二叉搜索树（BST）

### 1. 基本操作
- **查找**：O(log n)平均，O(n)最坏
- **插入**：保持BST性质
- **删除**：三种情况处理

### 2. 删除节点策略
- **叶子节点**：直接删除
- **单子节点**：子节点替代
- **双子节点**：前驱或后继替代

### 3. BST应用
- **范围查询**：查找指定范围内的值
- **第k小元素**：中序遍历第k个
- **验证BST**：检查是否满足BST性质

## 平衡二叉树

### 1. AVL树
- **平衡因子**：左右子树高度差
- **旋转操作**：LL、RR、LR、RL
- **时间复杂度**：所有操作O(log n)

### 2. 红黑树
- **性质**：节点颜色、路径黑节点数相等
- **插入删除**：通过旋转和重新着色维护性质
- **应用**：Java TreeMap、C++ map

### 3. B树/B+树
- **多路搜索树**：每个节点多个键值
- **应用**：数据库索引、文件系统
- **优势**：减少磁盘I/O次数

## 实际应用

### 1. 表达式树
- **构造**：从后缀表达式构造
- **求值**：后序遍历计算
- **优化**：常量折叠、公共子表达式

### 2. 决策树
- **分类**：根据特征进行分类
- **剪枝**：避免过拟合
- **随机森林**：多个决策树集成

### 3. 语法分析树
- **编译器**：语法分析阶段
- **AST**：抽象语法树
- **代码生成**：从AST生成目标代码

## 优化技巧

### 1. 空间优化
- **线索二叉树**：利用空指针存储遍历信息
- **数组表示**：完全二叉树用数组存储
- **压缩存储**：位图表示

### 2. 时间优化
- **缓存**：缓存计算结果
- **剪枝**：提前终止不必要的搜索
- **并行**：子树独立处理

### 3. 内存管理
- **对象池**：复用节点对象
- **延迟删除**：标记删除，批量回收
- **内存对齐**：提高缓存效率

## 面试要点

### 1. 基础概念
- **遍历方式？** 前中后序、层序遍历
- **BST性质？** 左小右大，中序遍历有序
- **平衡树作用？** 保证操作时间复杂度

### 2. 算法实现
- **递归vs迭代？** 递归简洁，迭代节省栈空间
- **如何判断平衡？** 左右子树高度差不超过1
- **BST删除节点？** 三种情况的处理方式

### 3. 复杂度分析
- **时间复杂度？** 平衡树O(log n)，退化O(n)
- **空间复杂度？** 递归O(h)，迭代O(w)
- **如何优化？** 平衡、缓存、剪枝

### 4. 实际应用
- **数据库索引？** B+树结构
- **文件系统？** 目录树结构
- **编译器？** 语法分析树
