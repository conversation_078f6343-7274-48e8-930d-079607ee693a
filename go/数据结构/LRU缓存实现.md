# LRU缓存实现

## LRU算法原理

### 1. 基本概念
LRU（Least Recently Used）最近最少使用算法，当缓存满时淘汰最久未使用的数据。

### 2. 核心思想
- **时间局部性**：最近访问的数据很可能再次被访问
- **淘汰策略**：优先淘汰最久未使用的数据
- **访问更新**：每次访问都要更新使用时间

## 实现方案

### 1. 数据结构选择
- **哈希表**：O(1)时间查找
- **双向链表**：O(1)时间插入删除
- **组合使用**：哈希表存储键值，链表维护访问顺序

### 2. 设计要点
- **头节点**：最近使用的节点
- **尾节点**：最久未使用的节点
- **容量限制**：超出容量时删除尾节点

## Go语言实现

### 基本结构
```go
type Node struct {
    key, value int
    prev, next *Node
}

type LRUCache struct {
    capacity int
    cache    map[int]*Node
    head     *Node
    tail     *Node
}

func NewLRUCache(capacity int) *LRUCache {
    head := &Node{}
    tail := &Node{}
    head.next = tail
    tail.prev = head

    return &LRUCache{
        capacity: capacity,
        cache:    make(map[int]*Node),
        head:     head,
        tail:     tail,
    }
}
```

### 核心操作
```go
func (lru *LRUCache) Get(key int) int {
    if node, exists := lru.cache[key]; exists {
        lru.moveToHead(node)
        return node.value
    }
    return -1
}

func (lru *LRUCache) Put(key, value int) {
    if node, exists := lru.cache[key]; exists {
        node.value = value
        lru.moveToHead(node)
    } else {
        newNode := &Node{key: key, value: value}
        lru.cache[key] = newNode
        lru.addToHead(newNode)

        if len(lru.cache) > lru.capacity {
            tail := lru.removeTail()
            delete(lru.cache, tail.key)
        }
    }
}

func (lru *LRUCache) addToHead(node *Node) {
    node.prev = lru.head
    node.next = lru.head.next
    lru.head.next.prev = node
    lru.head.next = node
}

func (lru *LRUCache) removeNode(node *Node) {
    node.prev.next = node.next
    node.next.prev = node.prev
}

func (lru *LRUCache) moveToHead(node *Node) {
    lru.removeNode(node)
    lru.addToHead(node)
}

func (lru *LRUCache) removeTail() *Node {
    lastNode := lru.tail.prev
    lru.removeNode(lastNode)
    return lastNode
}
```

## 时间复杂度分析

### 1. 操作复杂度
- **查找**：O(1) - 哈希表查找
- **插入**：O(1) - 链表头部插入
- **删除**：O(1) - 链表节点删除
- **更新**：O(1) - 组合操作

### 2. 空间复杂度
- **存储空间**：O(capacity)
- **哈希表**：O(capacity)
- **链表**：O(capacity)

## 变种实现

### 1. LFU缓存
- **策略**：淘汰访问频率最低的数据
- **实现**：频率计数 + 最小堆
- **复杂度**：Get/Put均为O(log n)

### 2. 时间窗口LRU
- **策略**：只考虑时间窗口内的访问
- **实现**：时间戳 + 定期清理
- **应用**：热点数据识别

### 3. 分段LRU
- **策略**：将缓存分为多个段，减少锁竞争
- **实现**：多个LRU实例 + 哈希分片
- **优势**：提高并发性能

## 实际应用

### 1. 操作系统
- **页面置换**：虚拟内存管理
- **缓冲区管理**：磁盘缓存
- **文件系统**：目录项缓存

### 2. 数据库系统
- **缓冲池**：数据页缓存
- **查询缓存**：SQL结果缓存
- **索引缓存**：B+树节点缓存

### 3. 应用层缓存
- **Web缓存**：页面内容缓存
- **API缓存**：接口响应缓存
- **对象缓存**：业务对象缓存

## 优化策略

### 1. 内存优化
- **对象池**：复用Node对象
- **内存预分配**：减少GC压力
- **紧凑存储**：减少指针开销

### 2. 并发优化
- **读写锁**：读多写少场景
- **分段锁**：减少锁竞争
- **无锁实现**：CAS操作

### 3. 性能优化
- **批量操作**：减少锁获取次数
- **异步更新**：后台更新访问时间
- **预取策略**：预测性加载

## 面试要点

### 1. 基础问题
- **LRU原理？** 最近最少使用淘汰策略
- **数据结构选择？** 哈希表+双向链表
- **时间复杂度？** Get/Put均为O(1)

### 2. 实现细节
- **为什么用双向链表？** 支持O(1)删除任意节点
- **如何处理容量满？** 删除尾节点，插入新节点到头部
- **并发安全如何保证？** 加锁或使用无锁数据结构

### 3. 扩展问题
- **LRU vs LFU？** 时间局部性 vs 频率局部性
- **如何优化内存使用？** 对象池、紧凑存储
- **大规模场景优化？** 分段、异步、预取

### 4. 实际应用
- **Redis过期策略？** 多种策略组合，包括LRU
- **操作系统页面置换？** Clock算法（LRU近似）
- **数据库缓冲池？** 改进的LRU算法
