# MongoDB 复制集与分片核心概念

## 一、复制集 (Replica Set) - 高可用

### 1. 核心概念
- **作用**: 数据冗余、自动故障转移，实现高可用。
- **节点角色**:
  - **Primary**: 主节点，接收所有写操作。
  - **Secondary**: 从节点，同步主节点数据，可用于读扩展。
  - **Arbiter**: 仲裁节点，只参与选举，不存数据，节省资源。
- **选举机制**: 当主节点故障，从节点会发起选举，获得大多数票的节点成为新主节点。

### 2. 读写策略
- **写关注 (Write Concern)**: 定义写入成功的标准 (e.g., `w: majority` 表示大多数节点确认)。
- **读偏好 (Read Preference)**:
  - `primary`: 只读主节点 (默认，强一致性)。
  - `primaryPreferred`: 优先读主，不可用时读从。
  - `secondary`: 只读从节点 (最终一致性)。
  - `secondaryPreferred`: 优先读从，无可用从节点时读主。
  - `nearest`: 读网络延迟最小的节点。

## 二、分片 (Sharding) - 水平扩展

### 1. 核心概念
- **作用**: 将数据分散到多个服务器（分片），解决单机存储和性能瓶颈。
- **架构组件**:
  - **mongos**: 路由进程，客户端入口，将请求转发到正确的分片。
  - **Config Servers**: 配置服务器（以复制集形式存在），存储集群元数据和分片键的映射关系。
  - **Shard**: 分片，实际存储数据的单元，通常是一个复制集以保证自身高可用。

### 2. 分片键 (Shard Key)
- **作用**: 决定数据如何分布在各个分片上。**一旦设定，不可更改。**
- **选择原则**:
  1. **高基数 (High Cardinality)**: 键值多样，避免数据集中在少数chunk。
  2. **均匀分布 (Even Distribution)**: 读写负载能均匀分散到所有分片，避免热点。
  3. **查询友好 (Query-friendly)**: 常用查询尽量带上分片键，mongos可以直接路由，避免广播查询（scatter-gather）。
- **典型分片键**:
  - **好的**: 用户ID、设备ID、地理位置（通常与高基数ID组合成复合分片键）。
  - **差的**: 时间戳、自增ID（导致写操作集中在最后一个分片）、状态字段（低基数）。

## 三、核心运维操作

### 1. 常用命令
- `rs.initiate()`: 初始化复制集。
- `rs.status()`: 查看复制集状态。
- `sh.addShard("host")`: 添加分片。
- `sh.enableSharding("db_name")`: 对数据库启用分片。
- `sh.shardCollection("db.collection", {shard_key})`: 对集合进行分片。
- `sh.status()`: 查看分片状态。

### 2. 数据均衡器 (Balancer)
- **作用**: 在后台迁移数据块 (chunks)，确保数据在分片间均匀分布。
- **管理**:
  - `sh.getBalancerState()`: 查看状态。
  - `sh.startBalancer()` / `sh.stopBalancer()`: 启停均衡器。
  - 可以设置均衡窗口，在业务低峰期进行数据迁移。

## 四、面试高频问题

1.  **复制集和分片的区别与联系？**
    -   **复制集**: 解决**高可用**，通过数据冗余实现故障自动转移。
    -   **分片**: 解决**水平扩展**，通过数据分发突破单机性能和容量限制。
    -   **联系**: 生产环境中，每个分片（Shard）通常都是一个复制集，以同时保证分片的高可用。

2.  **Primary 节点选举过程？**
    -   节点间通过心跳检测健康状况。
    -   Primary 宕机后，其余节点在延迟后发起选举。
    -   节点会投票给数据最新（oplog最新）且优先级最高的节点。
    -   获得**多数派**选票的节点成为新的 Primary。

3.  **分片键如何选择？选错了怎么办？**
    -   参考前面的三大原则（高基数、均匀分布、查询友好）。
    -   分片键一旦设定就**不可更改**。如果选错，唯一的办法是数据迁移：创建一个新的集合，使用正确的分片键，然后将旧集合的数据导入新集合。

4.  **如何避免热点问题？**
    -   **写热点**: 避免使用单调递增的分片键（如时间、自增ID）。可以使用哈希分片来打散。
    -   **读热点**: 确保分片键能将高频查询的数据分散到不同分片。
    -   **解决方案**: 选择合适的复合分片键，或对某些键使用哈希分片。

5.  **一个查询不带分片键会发生什么？**
    -   `mongos` 无法确定数据在哪一个分片，会将查询**广播**到所有分片（Scatter-Gather）。
    -   所有分片执行查询后，结果汇集到 `mongos` 进行合并。
    -   这种查询效率低下，应尽量避免。

6.  **一句话总结**
    > 复制集提供高可用性和读扩展，分片提供水平扩展能力，两者结合构建高性能、高可用的分布式MongoDB集群。
