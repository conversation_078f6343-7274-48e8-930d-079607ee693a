# MongoDB 事务与一致性 (面试速查版)

## 核心概念

- **ACID 支持**: MongoDB 4.0+ 支持多文档 ACID 事务。
    - **原子性 (Atomicity)**: 操作全成功或全失败。
    - **一致性 (Consistency)**: 事务前后数据状态一致。
    - **隔离性 (Isolation)**: 并发事务互不干扰 (通过快照隔离)。
    - **持久性 (Durability)**: 提交后数据持久化 (通过 Write Concern 控制)。

- **事务版本历史**:
    - **4.0**: 支持**副本集**内多文档事务。
    - **4.2**: 支持**分片集群**事务。

- **事务类型**:
    - **单文档原子操作**: 默认。单个文档内的更新是原子的，应优先使用。通过内嵌文档等方式进行数据建模，可避免多文档事务。
    - **多文档事务**: 用于需要原子更新多个文档的场景。

## 一致性控制 (关键)

### 读关注 (Read Concern) - 控制读什么数据

决定了读取操作能看到哪些数据。在性能和一致性间权衡。

- **`local`**: 默认。从当前节点直接读，延迟低，但可能读到脏数据或在发生回滚时读到旧数据。
- **`available`**: (仅分片集群) 从可用的副本节点读，延迟最低，不保证数据最新。
- **`majority`**: **推荐**。读取**已被大多数节点确认**的数据，避免脏读和数据回滚问题。
- **`snapshot`**: **事务专用**。在事务中提供一个数据快照，保证事务内的可重复读。
- **`linearizable`**: **最高级别**。保证读取到所有已成功提交的最新数据，性能开销最大。

### 写关注 (Write Concern) - 控制写到什么程度算成功

决定了写入操作需要得到多少个节点确认才算成功。在性能和数据可靠性间权衡。

- **`w: <number>`**: 需要多少个节点确认。`w: 1` (默认) 表示仅 Primary 确认即可。
- **`w: majority`**: **推荐**。需要副本集中**大多数**节点确认。是保证数据持久性的常用选项。
- **`j: <boolean>`**: 是否需要写入 on-disk journal 才返回。`j: true` 提供了更强的持久性保证。
- **`wtimeout: <ms>`**: 写操作的超时时间。

## 事务实践与优化

### 使用事务
- **会话 (Session)**: 事务必须在会话中执行。`client.StartSession()`
- **事务操作**: `session.WithTransaction()` 封装了事务的开始、提交、中止和重试逻辑，是推荐的使用方式。

### 性能优化
- **保持事务简短**: 只将真正需要原子性的操作放入事务，避免在事务中执行长时间运行的计算。
- **缩小范围**: 事务应只涉及必要的文档，避免全表扫描。
- **使用批量操作**: 在事务内，使用 `BulkWrite` 对多个文档进行操作，减少网络往返。
- **分片集群提示**: 如果可能，让事务操作在单个分片上进行，以获得最佳性能。

### 错误处理
- **瞬时错误 (Transient Errors)**: 比如网络问题、节点短暂不可用、写冲突 (`WriteConflict`)。
- **重试机制**: 官方驱动的 `WithTransaction` API 会**自动重试**标记为 `TransientTransactionError` 的错误。对于 `UnknownTransactionCommitResult` 错误，需要应用自行决定是否重试。

## 面试高频点

1.  **MongoDB 何时支持事务？**
    - 4.0 支持副本集事务，4.2 支持分片集群事务。

2.  **事务对性能有什么影响？**
    - **增加延迟**: 涉及协调器和多节点通信。
    - **降低吞吐量**: 事务会持有锁，可能阻塞其他操作。
    - **资源消耗**: 缓存（WiredTiger cache）消耗增加。

3.  **如何优化事务性能？**
    - 优先单文档原子操作（通过数据建模）。
    - 保持事务小而快。
    - 在事务内使用批量操作。

4.  **Read Concern 和 Write Concern 分别是什么？如何选择？**
    - **Read Concern**: 控制读取数据的一致性。金融等强一致场景用 `majority` 或 `snapshot`；对一致性要求不高的用 `local`。
    - **Write Concern**: 控制写入数据的持久性。关键业务用 `w: majority` 保证数据不丢失；普通日志类写入用 `w: 1` 追求高性能。

5.  **MongoDB 的隔离级别是什么？**
    - **快照隔离 (Snapshot Isolation)**。事务开始时，会对数据创建一个快照。事务中的所有读操作都会从这个快照中读取，与外部的修改隔离。

6.  **事务使用最佳实践？**
    - 优先数据建模，避免事务。
    - 事务要小而快。
    - 充分利用 `Read/Write Concern` 来平衡一致性和性能。
    - 监控事务相关的指标（如锁等待、冲突率）。
