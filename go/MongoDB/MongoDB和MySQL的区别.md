# MongoDB vs. MySQL (面试速查版)

## 1. 核心对比

| 特性 | MongoDB | MySQL |
|---|---|---|
| **数据模型** | 文档型 (BSON) | 关系型 (表格) |
| **Schema** | 动态、灵活 | 严格、预定义 |
| **查询语言** | MQL (JSON-like) | SQL |
| **事务** | 4.0+ 支持 ACID | 完整 ACID 支持 |
| **扩展性** | 水平扩展 (分片) | 垂直扩展为主 |
| **JOIN** | 有限 (`$lookup`) | 强大 JOIN |

## 2. 选型考量

### 何时选择 MongoDB?
*   **业务场景**: 需求快速变化、快速迭代的项目 (敏捷开发)。
*   **数据类型**: 非结构化或半结构化数据 (如：日志、内容管理、用户画像)。
*   **扩展需求**: 需要处理海量数据，要求高扩展性。
*   **开发模型**: 倾向于在应用层处理数据关系，而非数据库层。

### 何时选择 MySQL?
*   **业务场景**: 需要高度事务一致性的场景 (如：金融、电商、ERP)。
*   **数据类型**: 高度结构化的关系型数据。
*   **查询需求**: 业务需要复杂的 SQL 查询和多表关联 (JOIN)。
*   **技术生态**: 团队对 SQL 和关系型数据库有深厚经验，需要成熟稳定的解决方案。

## 3. 核心差异点

*   **数据结构**: MongoDB 的文档模型更符合面向对象编程，可以直接映射对象。MySQL 需要 ORM。
*   **性能**:
    *   MongoDB 通过内嵌文档和避免 JOIN，在高并发读写和简单查询下通常更快。
    *   MySQL 在复杂查询和多表联合查询上经过长期优化，表现更稳定。
*   **扩展性**: MongoDB 的分片机制是其核心优势，更容易实现水平扩展。MySQL 的分库分表方案复杂且维护成本高。
*   **事务**: MySQL 的 ACID 事务经过了长期考验，非常可靠。MongoDB 的事务支持较新，适用于特定场景，但复杂性不如 MySQL。

## 4. 一句话总结
> **MongoDB** 强在 `灵活` 和 `水平扩展`，适合业务多变和大数据场景。
> **MySQL** 强在 `稳定` 和 `事务一致性`，适合关系复杂和数据一致性要求高的场景。

## MongoDB 核心优势

*   **灵活的文档模型 (BSON)**
    *   **动态 Schema**: 无需预定义表结构，方便快速迭代和演进。
    *   **丰富的数据结构**: 可内嵌文档和数组，轻松表示复杂层级关系。

*   **高可扩展性**
    *   **水平扩展 (Sharding)**: 通过分片将数据分布到多台服务器，突破单机瓶颈，支持海量数据。

*   **高性能**
    *   **高吞吐读写**: 内存计算，将工作集数据缓存在RAM中。
    *   **强大的索引支持**: 支持单键、复合、地理空间、全文等多种索引类型。

*   **高可用性**
    *   **复制集 (Replica Set)**: 数据冗余备份，实现自动故障转移和恢复。

*   **丰富的查询与分析能力**
    *   **强大的查询语言**: 支持 CRUD 操作、数据聚合 (Aggregation Pipeline)、MapReduce。
    *   **多功能支持**: 地理空间查询 (Geospatial), 全文搜索 (Full-text Search)。