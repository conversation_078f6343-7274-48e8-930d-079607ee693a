# 消息队列核心价值与应用

## 1. 核心功能

- **解耦 (Decoupling):** 服务间通过消息交互，避免直接调用，实现独立演进。
- **异步 (Asynchronous):** 将耗时操作（如邮件、短信通知）放入队列后台处理，提升主流程响应速度。
- **削峰 (Peak Shaving):** 缓冲突发流量（如秒杀、大促），防止系统过载，平滑处理请求。

## 2. 核心业务场景

- **电商订单系统:** 订单创建后，通过消息队列异步完成库存、支付、物流、积分等多个下游操作。
- **秒杀系统:** 承接瞬时海量请求，后端服务按实际处理能力消费，防止超卖和系统崩溃。
- **日志收集:** 解耦业务与日志处理，将日志写入消息队列，由ELK等系统消费分析。
- **数据同步:** 当主数据源变更时，通过消息队列通知缓存、搜索索引、数据仓库等进行同步。

## 3. 技术选型关注点 (面试)

- **为什么用MQ?**
  - **核心:** 解耦、异步、削峰。
- **MQ带来什么问题?**
  - **系统复杂性:** 引入了新的中间件，需要保证其高可用。
  - **数据一致性:** 消息传递的上下游状态需要保证最终一致性。
  - **消息积压:** 需监控并处理消费延迟、消息堆积问题。
- **如何保证消息可靠性?**
  - **生产者:** 确认机制 (ACK)。
  - **Broker:** 持久化、多副本。
  - **消费者:** 消费后确认 (ACK)。
- **如何选型?**
  - **Kafka:** 高吞吐、分布式、流处理平台，适用于日志、大数据场景。
  - **RocketMQ:** 低延迟、高可靠、功能丰富（事务、定时消息），适用于金融、电商业务。
  - **RabbitMQ:** 成熟稳定、协议支持广泛 (AMQP)、社区活跃，适用于企业级应用。