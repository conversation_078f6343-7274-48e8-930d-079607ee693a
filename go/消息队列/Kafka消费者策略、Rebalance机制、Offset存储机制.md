## Kafka 消费者核心机制

### 1. 消费者策略 (Consumer Strategy)

- **消费者组 (Consumer Group)**: 多个消费者共同消费一个 Topic，实现并行处理和高可用。
  - **关键特性**: 一个分区 (Partition) 在同一时间只能被组内的 **一个** 消费者消费。
  - **优点**: 易于水平扩展消费能力。

- **分区分配策略 (`partition.assignment.strategy`)**:
  - **Range**: 按分区序号范围分配，容易导致部分消费者负载过高。
  - **RoundRobin**: 轮询分配，负载最均衡。
  - **Sticky**: 粘性分配。尽可能保持上一次的分配结果，减少不必要的 Rebalance。**（推荐）**

---

### 2. Rebalance 机制

- **定义**: 消费者组内部分区所有权的动态再分配过程。

- **触发场景**:
  - 消费者数量变化 (新成员加入或旧成员离开/崩溃)。
  - Topic 分区数量变化。

- **主要影响 (缺点)**:
  - Rebalance 期间，整个消费者组会 **暂停消费 (Stop-the-world)**，直到分配完成。
  - 频繁的 Rebalance 会严重影响消费性能和稳定性。

---

### 3. Offset 管理 (Offset Management)

- **作用**: Offset 是一个递增的整数，用于追踪消费者在每个分区上的消费进度。

- **存储位置**:
  - **默认**: Kafka 内部的一个特殊 Topic `__consumer_offsets`。

- **Offset 提交 (Commit)**:
  - **自动提交 (`enable.auto.commit=true`)**: 由消费者客户端按固定频率自动提交。
    - **优点**: 简单。
    - **缺点**: 可能导致消息重复消费或丢失。
  - **手动提交**:
    - **同步提交 (`commitSync`)**: 阻塞式，提交成功或失败后才继续。可靠性高，但影响吞吐。
    - **异步提交 (`commitAsync`)**: 非阻塞，性能好，但提交失败时不会重试，可能导致后续提交覆盖失败的提交。

- **Offset 重置策略 (`auto.offset.reset`)**: 当消费者启动时没有找到有效的 Offset 时生效。
  - **`latest` (默认)**: 从分区 **最新** 的消息开始消费 (忽略老数据)。
  - **`earliest`**: 从分区 **最早** 的消息开始消费。