# 消息队列设计模式（面试速查版）

## 1. 基础模式

### 点对点模式 (Point-to-Point)
- **特点**: 一个生产者，一个消费者。
- **场景**: 任务分发、命令处理。
- **关键**: 队列保证消息只被消费一次。

### 发布订阅模式 (Pub/Sub)
- **特点**: 一个生产者，多个消费者。
- **场景**: 事件通知、数据广播。
- **关键**: Topic机制，消息被所有订阅者接收。

## 2. 高级模式

### 请求响应模式 (Request-Reply)
- **特点**: 异步RPC。
- **场景**: 服务间解耦通信。
- **关键**: 使用临时回复队列 (Reply-To Queue) 和关联ID (Correlation ID)。

### 工作队列模式 (Work Queue)
- **特点**: 多个消费者竞争消费一个队列。
- **场景**: 后台任务处理、负载均衡。
- **关键**: 轮询或公平分发。

### 路由模式 (Routing)
- **特点**: 根据路由键 (Routing Key) 将消息发送到指定队列。
- **场景**: 日志分级、消息分类。
- **关键**: Exchange + Routing Key 绑定。

## 3. 可靠性模式

### 消息确认 (Acknowledgement)
- **生产者确认 (Producer Confirm)**: 确保消息成功到达Broker。
- **消费者确认 (Consumer Ack)**: 确保消息被成功处理。
- **关键**: ACK/NACK机制，失败可重发。

### 死信队列 (Dead-Letter Queue)
- **目的**: 处理无法消费的 "死" 消息。
- **场景**: 消息重试超限、格式错误、TTL过期。
- **关键**: `TTL` + `Dead-Letter-Exchange (DLX)`。

### 幂等性 (Idempotency)
- **目的**: 防止同一消息被重复处理。
- **关键**:
    - **消息去重**: 为每条消息生成唯一ID，处理前检查。
    - **业务幂等**: 操作本身天然幂等（如 `SET a=100`），或通过状态机、版本号等实现。

## 4. 性能模式

### 批量处理 (Batching)
- **目的**: 减少网络IO，提高吞吐量。
- **实现**: 批量发送、批量消费。

### 预取 (Prefetch)
- **目的**: 减少消费者拉取消息的网络延迟。
- **实现**: 客户端预拉取并缓存一定数量（`prefetch count`）的消息。

### 分区 (Partitioning)
- **目的**: 水平扩展，并行处理，提高吞吐。
- **实现**: 按Key将消息哈希到不同分区，每个分区可独立消费。

## 5. 监控模式

### 健康检查 (Health Check)
- **目的**: 监控MQ服务和客户端连接状态。
- **关键指标**: 连接状态、队列深度、消费者数量。

### 指标收集 (Metrics)
- **目的**: 度量系统性能和状态。
- **关键指标**:
    - **流量**: QPS (每秒消息数)、TPS (每秒事务数)。
    - **延迟**: 端到端延迟、处理耗时。
    - **积压**: 队列中未处理的消息数 (Lag)。
    - **错误率**: 发送/消费失败率。

---

## 面试核心要点

### 设计选择
- **P2P vs Pub/Sub**: 取决于业务是一对一处理还是需要广播。
- **保证消息不丢失**: `生产者确认` + `消息持久化` + `消费者确认`。
- **处理消息重复**: `幂等性`设计（唯一ID、业务状态判断）。
- **提高性能**: `批量处理` + `并行消费（分区/多Worker）` + `预取`。
- **保证顺序消费**:
    - **全局有序**: 单一队列，单一消费者。性能瓶颈。
    - **分区有序**: 将需要保证顺序的消息（如同一用户的订单）通过相同Key发送到同一分区。

### 最佳实践
- **参数设置**: 合理配置TTL、队列长度、死信策略。
- **监控告警**: 监控核心指标（积压、延迟、错误率），设置告警。
- **优雅启停**: 程序关闭前确保已处理完的消息被确认，未处理的能安全回滚或重入队。
- **错误处理**: 设计好重试机制（次数、间隔）和最终失败处理（死信队列、人工介入）。

### 常见陷阱
- **忘记ACK**: 导致消息在消费者断开后被重复投递。
- **无限重试**: 毒药消息（Poison Pill）可能导致消费者死循环，拖垮系统。必须有重试上限。
- **消息积压**: 生产速度远超消费速度，导致队列溢出、内存耗尽。需要监控和扩容。
- **顺序错乱**: 并行消费天然会破坏顺序，需要特殊设计（如分区）来保证局部有序。
