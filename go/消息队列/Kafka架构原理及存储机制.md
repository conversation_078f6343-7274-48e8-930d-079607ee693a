# Kafka架构原理及存储机制 (面试速查版)

---

## Kafka 核心架构

Kafka 的架构旨在实现高吞吐、高可用和高扩展性。

- **Broker**: Kafka 服务器实例，多个 Broker 组成集群。
- **Producer**: 消息生产者，将消息发布到 Topic。
- **Consumer**: 消息消费者，从 Topic 订阅消息。**Consumer Group** 实现并行消费。
- **Topic**: 消息的逻辑分类，类似数据库的表。
- **Partition**: Topic 的物理分区，是 **并行处理** 的基本单位。每个 Partition 是一个有序、不可变的日志，保证了 **分区内消息的顺序性**。
- **Leader & Follower**: 每个 Partition 有一个 Leader (处理所有读写) 和多个 Follower (数据同步/备份)。Leader 宕机时，自动从 Follower 中选举新 Leader，实现 **高可用**。
- **Zookeeper/Raft**: 负责集群元数据管理、Broker 协调和 Leader 选举。新版 Kafka 逐渐用内置的 Raft 替代 Zookeeper。

---

## Kafka 高效存储机制

Kafka 的核心是其高效的日志存储系统。

- **顺序写磁盘 (Sequential Write)**: 消息以追加方式写入日志文件，利用操作系统的顺序 I/O 优化，实现高吞吐量。这是 Kafka 高性能的关键之一。
- **Segment 分段存储**: Partition 日志被切分为多个 **Segment** 文件。这种设计便于按策略（时间或大小）进行数据清理，只需删除旧的 Segment 文件即可。
- **消息保留策略 (Retention Policy)**:
    - **按时间**: 如保留最近 7 天的数据。
    - **按大小**: Partition 超过指定大小时删除旧数据。
- **日志压缩 (Log Compaction)**: 对于有 Key 的消息，只保留每个 Key 的最新值。适用于需要保存最新状态的场景（如用户配置）。
- **零拷贝 (Zero-Copy)**: 在数据传输时（Broker 间复制、Consumer 拉取），利用零拷贝技术直接在内核空间进行数据传输，避免了 CPU 和内存的多次拷贝，极大提升了数据传输效率。
- **Offset (偏移量)**: 每条消息在 Partition 中都有一个唯一的数字标识 (Offset)。Consumer 通过 Offset 控制消费位置，可以实现重复消费和从任意位置开始消费。