# 消息队列对比与选型

## 主流消息队列对比

### 1. Kafka
- **特点**: 高吞吐量、分布式、持久化、分区有序。
- **适用场景**: 大数据、日志收集、实时流处理。
- **优势**: 百万级TPS、水平扩展能力强、消息持久化、支持多种消费模式。
- **劣势**: 运维复杂度高、消息延迟相对较高、不支持消息优先级。

### 2. RabbitMQ
- **特点**: 功能丰富、高可靠性、支持多种协议(AMQP)。
- **适用场景**: 企业级应用、复杂路由、事务处理。
- **优势**: 功能丰富（路由、交换机）、高可靠性、支持事务和确认机制、管理界面友好。
- **劣势**: 吞吐量相对较低、集群配置复杂、内存消耗较大。

### 3. Redis Stream
- **特点**: 轻量级、高性能、基于Redis、支持持久化。
- **适用场景**: 轻量级消息队列、需要消费者组的缓存场景。
- **优势**: 性能优秀、部署简单、支持消费者组、内存占用小。
- **劣势**: 功能相对简单、集群方案复杂、持久化依赖配置。

### 4. NSQ
- **特点**: 去中心化、简单易用、高可用。
- **适用场景**: 微服务通信、实时消息推送。
- **优势**: 去中心化架构、部署运维简单、自动负载均衡、内置监控。
- **劣势**: 功能相对简单、社区相对较小、消息顺序性保证有限。

## 选型对比表

| 特性 | Kafka | RabbitMQ | Redis Stream | NSQ |
|------|-------|----------|--------------|-----|
| 吞吐量 | 极高 | 中等 | 高 | 高 |
| 延迟 | 中等 | 低 | 低 | 低 |
| 可靠性 | 高 | 极高 | 中等 | 高 |
| 扩展性 | 极好 | 好 | 中等 | 好 |
| 运维复杂度 | 高 | 中等 | 低 | 低 |
| 消息顺序 | 分区有序 | 支持 | 支持 | 有限 |
| 持久化 | 支持 | 支持 | 支持 | 支持 |

## 核心概念

- **消息可靠性**:
  - **At Most Once**: 最多一次，可能丢失。
  - **At Least Once**: 至少一次，可能重复。
  - **Exactly Once**: 精确一次，理想状态，通常通过幂等或事务实现。
- **消息顺序性**:
  - **全局有序**: 所有消息严格按序。
  - **分区有序**: 同一分区内消息有序 (如Kafka)。
  - **无序**: 不保证消息顺序。
- **消费模式**:
  - **Push模式**: 服务端主动推送 (RabbitMQ)。
  - **Pull模式**: 客户端主动拉取 (Kafka)。

## 高可用设计

- **集群部署**: 多节点部署，避免单点故障。
- **数据复制**:
  - **同步复制**: 强一致性，性能低。
  - **异步复制**: 高性能，可能丢数据。
  - **半同步复制**: 平衡性能和一致性。
- **故障转移**: 主从切换、负载均衡、数据恢复。

## 性能优化

- **生产者**: 批量发送、请求压缩、异步发送。
- **消费者**: 批量消费、并发消费。
- **Broker/存储**: 合理设置分区、选择压缩算法、设置数据保留策略。

## 面试要点
1. **选型依据**：根据业务场景（吞吐量、可靠性、延迟、功能）选择。
2. **可靠性保证**：如何实现At-least-once, Exactly-once（ACK机制、幂等处理、事务）。
3. **性能优化**：生产者、消费者、Broker端的优化手段。
4. **高可用设计**：集群、数据复制、故障转移方案。
5. **监控运维**：关键指标（消息积压/Lag, TPS, Latency）和故障排查。
