# Kafka 故障排查核心指南

## 1. 面试核心思路

### 故障排查四步法
1.  **定位问题**：判断问题发生在 **生产者**、**消费者** 还是 **Broker**。
2.  **收集信息**：查看 **日志**、**监控指标**（Lag、QPS、错误率等）、检查相关 **配置**。
3.  **分析原因**：结合信息，分析是 **网络**、**硬件**、**代码逻辑** 还是 **负载压力** 问题。
4.  **解决问题**：先用 **临时方案** 恢复服务（如重启、扩容），再制定 **长久之策**（如修复代码、优化配置）。

### 预防措施
-   **完善监控告警**：建立关键指标（如 Lag、磁盘使用率）的监控和告警。
-   **容量规划**：定期评估业务流量，做好机器和集群扩容规划。
-   **故障演练**：定期模拟节点宕机、网络分区等场景，检验高可用性。
-   **文档维护**：保持架构图、运维手册、应急预案等文档的更新。

---

## 2. 常见故障与解决方案

### 消息丢失
-   **可能原因**：
    -   **生产者**：`acks` 配置不当（如 `acks=0` 或 `acks=1`）、网络抖动导致发送失败。
    -   **Broker**：`min.insync.replicas` 不足、Leader 选举时 `unclean.leader.election.enable=true` 导致未同步数据丢失。
    -   **消费者**：先提交位移再处理消息、自动提交位移。
-   **解决方案**：
    -   **生产者**：
        -   `acks` 设置为 `all` 或 `-1`。
        -   开启幂等性：`enable.idempotence = true`。
        -   配置合理的重试机制 `retries` 和 `retry.backoff.ms`。
    -   **Broker**：
        -   `min.insync.replicas` 至少设置为 2。
        -   关闭 Unclean Leader 选举：`unclean.leader.election.enable = false`。
    -   **消费者**：
        -   关闭自动提交：`enable.auto.commit = false`。
        -   采用手动提交，在消息处理成功后再提交位移。

### 消息重复
-   **可能原因**：
    -   **生产者**：发送超时或返回错误，触发重试机制。
    -   **消费者**：
        -   消息处理完，但位移提交失败（如网络问题或消费者崩溃）。
        -   消费组发生 Rebalance，未提交位移的消息被重新消费。
-   **解决方案**：
    -   **生产者**：开启幂等性（`enable.idempotence=true`），`producer` 会为每个消息分配序列号，避免重试导致重复。
    -   **消费者**：
        -   **保证消费逻辑幂等**：利用数据库唯一键、分布式锁（如 Redis `setnx`）或版本号等机制，确保同一消息多次处理结果一致。
        -   减小 `max.poll.interval.ms`，避免处理超时导致 Rebalance。

### 消息积压
-   **可能原因**：
    -   消费速度远低于生产速度。
    -   消费者实例崩溃或全部离线。
    -   消费逻辑存在阻塞或 Bug，导致处理缓慢。
-   **解决方案**：
    -   **排查**：
        -   使用 `kafka-consumer-groups.sh` 脚本监控消费延迟（Lag）。
        -   检查消费者日志，定位处理瓶颈（如慢查询、外部服务调用耗时）。
    -   **处理**：
        -   **紧急**：横向扩容消费者实例数量。如果分区数不足，可增加 Topic 分区数再扩容消费者。
        -   **长期**：优化消费逻辑，提升单实例处理性能。
        -   **数据修复**：如果积压过多，可以考虑编写临时脚本，将积压消息转存到临时 Topic，分批处理。

### 消息乱序
-   **可能原因**：
    -   **生产者**：`max.in.flight.requests.per.connection > 1` 时，重试可能导致乱序。
    -   **消费者**：多线程或协程并发处理一个分区内的消息。
-   **解决方案**：
    -   **保证有序性**：
        -   **分区内有序**：Kafka 只保证单个分区内的消息有序。将需要保证顺序的消息（如同一订单的操作）通过相同的 `key` 发送到同一个分区。
        -   **生产者**：设置 `max.in.flight.requests.per.connection = 1`（会影响吞吐量）。
        -   **消费者**：单线程消费，或将同一 `key` 的消息分发到同一个内存队列，由单个后台线程处理。

---

## 3. 性能优化关键参数

### 生产者优化
-   `acks`: 吞吐量和可靠性的权衡。`all` > `1` > `0`。
-   `compression.type`: 压缩消息体（`snappy`, `lz4`, `gzip`），减少网络 IO，但增加 CPU 消耗。
-   `batch.size`: 批量发送的消息大小，越大吞吐越高，但延迟也越高。
-   `linger.ms`: 发送一个批次的等待时间，与 `batch.size` 配合使用。

### 消费者优化
-   `fetch.min.bytes`: 一次 fetch 请求最少获取的数据大小。
-   `fetch.max.wait.ms`: 如果数据量未达到 `fetch.min.bytes`，最长等待时间。
-   `max.partition.fetch.bytes`: 一次 fetch 请求从单个分区获取的最大数据大小。

### Broker 优化
-   **JVM**：优化 `-Xmx`, `-Xms` 参数，选择合适的垃圾回收器（如 G1）。
-   **IO线程**：调整 `num.network.threads` (网络IO) 和 `num.io.threads` (磁盘IO)。
-   **日志保留**：合理配置 `log.retention.hours` 或 `log.retention.bytes`，避免磁盘写满。

---

## 4. 核心监控指标

-   **Broker 指标**：
    -   `UnderReplicatedPartitions`: 副本不足的分区数，应为 0。
    -   `IsrShrinksPerSec/IsrExpandsPerSec`: ISR 列表伸缩频率，过高表示集群不稳定。
    -   `LogFlushRateAndTimeMs`: 日志刷盘速率和耗时。
-   **生产者指标**：
    -   `request-rate`: 发送速率。
    -   `record-error-rate`: 错误率。
-   **消费者指标**：
    -   `records-lag-max`: **最核心指标**，消费延迟。
    -   `bytes-consumed-rate`: 消费速率。

**告警**：为 `UnderReplicatedPartitions` > 0、`records-lag-max` 超过阈值、错误率过高等关键指标配置告警。
