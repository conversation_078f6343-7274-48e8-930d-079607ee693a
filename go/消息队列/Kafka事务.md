# Kafka 事务核心概念速查

Kafka 事务机制主要用于保证端到端（生产者 -> Kafka -> 消费者）的 **Exactly Once Semantics (EOS，恰好一次语义)**，确保消息在生产和消费过程中原子性地处理，不丢不重。适用于金融、订单等对数据一致性要求高的场景。

### 1. 核心思想
通过将一组消息的生产和消费标记为一个原子操作，要么全部成功，要么全部失败。

### 2. 使用场景
- **跨分区/主题原子写入**: 保证一组消息原子地写入多个分区或主题。
- **"消费-处理-生产"模式**: (Consume-Transform-Produce) 将"从 Kafka 读取数据 -> 业务处理 -> 写回 Kafka"的整个流程作为一个原子事务，是流处理中常见的模式。

### 3. 核心组件与工作流程

#### 组件
- **事务生产者 (Transactional Producer)**: 唯一标识一个生产者实例 (`transactional.id`)，负责开启、提交或中止事务。
- **事务协调器 (Transaction Coordinator)**: 服务端组件，每个生产者对应一个协调器实例。负责管理事务日志（`__transaction_state` topic），记录事务状态并驱动事务的提交或回滚。
- **事务消费者 (Transactional Consumer)**: 通过设置 `isolation.level=read_committed`，只消费已提交的事务消息，未提交或回滚的事务消息对其不可见。

#### 工作流程
1.  **`FindCoordinator`**: 生产者向 Broker 发送请求，找到自己的事务协调器。
2.  **`InitProducerId`**: 生产者从事务协调器获取唯一的 `Producer ID` (PID)。即使应用重启，只要 `transactional.id` 不变，就能恢复之前的状态（如中止未完成的事务）。
3.  **开启事务 (`BeginTransaction`)**: 生产者在本地记录事务开始。
4.  **生产消息 (`Produce`)**: 生产者向目标 Topic-Partition 发送消息。这些消息在被提交前，对 `read_committed` 的消费者不可见。
5.  **提交/中止事务 (`Commit/AbortTransaction`)**:
    - **提交**: 生产者向协调器发送 `Commit` 请求。协调器通过两阶段提交（2PC）协议，确保所有涉及的分区都标记消息为"已提交"。
    - **中止**: 生产者发送 `Abort` 请求。协调器标记所有消息为"已中止"，这些消息永远不会被消费者读到。

### 4. 优缺点

#### 优点
- **原子性保证**: 实现跨多个分区和主题的原子写入。
- **恰好一次语义 (EOS)**: 结合幂等性生产者，实现端到端的 Exactly Once。
- **简化业务逻辑**: 无需在应用层实现复杂的状态管理和补偿逻辑。

#### 缺点
- **性能开销**: 事务协调和两阶段提交会引入额外延迟，略微降低吞吐量。
- **实现复杂性**: 增加了 Broker 和客户端的复杂度。
- **Broker 资源消耗**: `__transaction_state` Topic 会占用额外的存储和网络资源。

### 5. 关键配置

#### 生产者 (Producer)
- `transactional.id`: **必须设置**。唯一的事务 ID，用于故障恢复和识别生产者实例。
- `enable.idempotence`: **必须设为 `true`**。事务要求幂等性必须开启。
- `acks`: **必须设为 `all`**。保证消息写入所有同步副本，避免数据丢失。

#### 消费者 (Consumer)
- `isolation.level`: 设为 `read_committed`。默认为 `read_uncommitted`，会读取到未提交的事务消息（包括已中止的）。