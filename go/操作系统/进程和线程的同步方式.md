# 进程与线程同步核心速查

## 1. 核心同步机制对比

| 同步方式 | 核心作用 | 适用范围 | 关键特点 |
|---|---|---|---|
| **互斥锁 (Mutex)** | 保证资源独占访问 | 线程/进程 | 简单高效，阻塞等待，易死锁 |
| **读写锁 (RWLock)** | 读共享，写独占 | 线程/进程 | 读多写少场景性能高，可能写者饥饿 |
| **信号量 (Semaphore)**| 控制并发资源数量 | 线程/进程 | 资源计数，可用于限流、生产者消费者 |
| **条件变量 (CondVar)**| 复杂等待/通知 | 线程/进程 | 需配合互斥锁，避免轮询 |
| **自旋锁 (Spinlock)**| 忙等待获取锁 | 线程/进程 | CPU空转，无切换开销，适用极短锁 |
| **原子操作** | 无锁数据操作 | 线程/进程 | 硬件支持，效率最高，操作受限 |
| **文件锁** | 通过文件系统同步 | 进程 | 简单可靠，性能较低 |
| **屏障 (Barrier)** | 同步多个执行点 | 线程 | 并行计算阶段同步 |

*注：进程间使用互斥锁、读写锁、条件变量通常需要共享内存支持。*

## 2. 如何选择？

- **按性能要求 (高到低):**
  1. **原子操作**
  2. **自旋锁** (多核 & 持有时间极短)
  3. **互斥锁**
  4. **读写锁** (读多写少)

- **按业务场景:**
  - **独占访问**：互斥锁
  - **读多写少**：读写锁
  - **控制资源数/限流**：信号量
  - **等待特定条件/生产者消费者**：条件变量 + 互斥锁
  - **并行计算各阶段同步**：屏障

## 3. 面试核心问题

### Q1: 为什么条件变量必须配合互斥锁？
- **保护条件状态**：防止在检查条件和进入等待状态之间，条件被其他线程修改，引发竞态条件。
- **防止虚假唤醒**：被唤醒后，立即重新加锁，再次检查条件是否真正满足。
- **保证原子性**：保证"解锁并等待"这一操作是原子性的，不会被中断。

### Q2: 自旋锁 vs 互斥锁？
- **自旋锁**: **不释放CPU**，循环忙等。适用于**多核CPU**且**锁持有时间极短**（纳秒级）的场景，避免了上下文切换的开销。
- **互斥锁**: **释放CPU**，线程休眠。适用于**锁持有时间较长**或**单核CPU**的场景，避免CPU资源浪费。

### Q3: 读写锁的"写者饥饿"问题？
- **原因**: 在高并发读的情况下，读锁不断被获取，导致写锁一直等待，无法被获取。
- **解决**:
  1. **写者优先**: 当有写者等待时，后续的读请求排队。
  2. **公平策略**: 按先来后到的顺序授予锁。

### Q4: 进程间同步为何比线程间复杂？
- **内存空间独立**: 无法直接访问对方内存，数据共享需要通过共享内存、管道等IPC（进程间通信）机制。
- **开销更大**: 同步操作通常需要陷入内核态，上下文切换成本高于线程。
- **鲁棒性**: 进程崩溃可能导致锁等同步资源无法释放，需要系统级的清理机制。