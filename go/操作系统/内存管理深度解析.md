# 内存管理速查

### 1. 核心概念
- **虚拟内存**: 为进程提供独立、连续的地址空间，实现进程隔离、内存保护和扩展。
- **物理内存**: 实际的RAM。
- **地址转换**: `虚拟地址 -> MMU(页表) -> 物理地址`。`TLB`是页表的高速缓存。

### 2. 内存分配
- **连续分配**:
    - **首次适应**: 第一个够大的块 (快, 外部碎片多)
    - **最佳适应**: 最小的够大的块 (利用率高, 小碎片多)
    - **最坏适应**: 最大的块 (避免小碎片)
- **非连续/高级**:
    - **伙伴系统**: 按2的幂分块，减少外部碎片。
    - **内存池**: 预分配同尺寸块，快，无外部碎片。

### 3. 内存碎片
- **内部碎片**: 分配块太大，内部有浪费。
- **外部碎片**: 空闲块太碎，无法分配。
- **解决**: 伙伴系统、内存池、GC压缩。

### 4. 页面置换算法 (缺页中断时)
- **FIFO**: 先进先出 (简单, Belady异常)
- **LRU**: 最近最少使用 (效果好, 实现复杂)
- **Clock**: LRU的近似 (性能/开销均衡)

### 5. 垃圾回收 (GC)
- **标记-清除**: 标记存活，清除垃圾 (有碎片)
- **复制算法**: 复制存活对象到新区域 (无碎片, 浪费空间)
- **标记-压缩**: 标记后移动存活对象 (无碎片, 移动开销)

### 6. 常见问题
- **内存泄漏检测?**
    - **静态分析**: 代码审查、工具扫描。
    - **动态检测**: 运行时监控 (e.g., Valgrind, AddressSanitizer)。
    - **性能监控**: 观察内存使用趋势。
