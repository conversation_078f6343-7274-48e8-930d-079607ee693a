# 进程、线程与协程的深度对比

## 1. 基本定义

| 类型 | 定义 | 核心角色 |
|------|------|----------|
| **进程 (Process)** | 操作系统进行资源分配和调度的基本单位，是应用程序的执行实例。 | 资源分配的基本单位 |
| **线程 (Thread)** | 进程内的一个执行单元，是操作系统能够进行运算调度的最小单位。 | CPU调度的基本单位 |
| **协程 (Coroutine)** | 一种用户态的轻量级线程，由程序员在代码中进行调度。 | 用户态的轻量级线程 |

---

## 2. 核心区别对比

| 维度 | 进程 | 线程 | 协程 |
|:---|:---|:---|:---|
| **内存空间** | 独立的虚拟地址空间，隔离性强 | 共享进程的地址空间 | 共享线程的地址空间 |
| **栈空间** | 独立，通常为8MB | 独立，通常为2MB | 独立，初始仅2-8KB，按需增长 |
| **创建开销** | 最大（分配独立内存和内核资源） | 中等（需分配栈和内核资源） | 最小（仅分配少量用户态资源） |
| **切换开销** | 最大（涉及内核态切换、页表切换、TLB刷新） | 中等（涉及内核态切换，但不切换页表） | 最小（纯用户态切换，只保存/恢复寄存器上下文） |
| **并发数量** | 受内存和内核限制，通常为百级 | 受内存和内核限制，通常为千级 | 可达百万级 |
| **调度方式** | 由操作系统内核调度（抢占式） | 由操作系统内核调度（抢占式） | 由用户态调度器调度（协作式+部分抢占） |
| **通信方式** | IPC（管道、消息队列、共享内存、信号量） | 共享内存、同步原语（锁、条件变量） | Channel（推荐）、共享内存 |
| **故障隔离** | 强隔离，一个进程崩溃不影响其他进程 | 弱隔离，一个线程崩溃导致整个进程崩溃 | 几乎无隔离，一个协程panic可能导致整个进程崩溃 |
| **数据共享** | 复杂，需要IPC | 简单，直接读写共享内存（需加锁） | 简单，但推荐通过Channel通信来共享 |

---

## 3. 内存布局差异

**进程**拥有完全独立的内存布局，包括代码段、数据段、堆和栈。

**线程**则共享大部分进程资源，但拥有独立的私有部分：
- **共享资源**：代码段、数据段、堆、文件描述符、全局变量。
- **私有资源**：栈空间、程序计数器、寄存器集合。

这种设计使得线程间通信高效，但也带来了数据同步的挑战。

---

## 4. 调度机制

- **进程/线程调度**：由操作系统内核的调度器负责，遵循抢占式策略。当一个线程的时间片用完或被更高优先级的线程抢占时，内核会强制切换。这涉及到从用户态到内核态的转换，开销较大。

- **协程调度**：由用户空间的调度器（例如Go的GPM模型）管理。调度时机通常是协程主动让出（yield），如等待I/O、Channel操作等。这种协作式调度避免了系统调用，切换开销极低。Go 1.14后引入了基于信号的异步抢占，以解决某些计算密集型协程长时间占用CPU的问题。

---

## 5. 使用场景

| 类型 | 适用场景 | 示例 |
|:---|:---|:---|
| **进程** | - 需要强隔离性，防止互相干扰<br>- 容错性要求高的场景<br>- 多核并行，利用多机资源（微服务） | - 浏览器为每个标签页创建一个进程<br>- Office套件中的Word和Excel |
| **线程** | - 需要大量数据共享和频繁通信<br>- CPU密集型任务，利用多核并行计算<br>- 提高程序响应速度，如GUI应用 | - Web服务器用线程池处理并发请求<br>- 并行计算、图像处理 |
| **协程** | - 高并发I/O密集型场景，如网络服务器、爬虫<br>- 需要管理大量并发连接<br>- 异步编程模型，简化代码逻辑 | - Go语言构建的后端服务<br>- Python中的asyncio/await |

---

## 6. 面试高频问题

### Q1: 协程为什么比线程轻量？
1.  **用户态调度**：切换和创建完全在用户态完成，无需昂贵的系统调用和内核态/用户态切换。
2.  **小栈空间**：初始栈非常小（如Go中为2KB），并可以按需动态扩展，而线程栈通常是固定的MB级别。
3.  **简单的上下文**：切换时只需保存和恢复少量寄存器，而线程切换需要保存更多内核级状态。

### Q2: Go的GPM调度模型是什么？
GPM是Go语言协程调度器的核心：
- **G (Goroutine)**：即Go协程，是基本的执行单元。
- **P (Processor)**：逻辑处理器，是G和M之间的中间层。P维护一个本地的G运行队列，为G的执行提供上下文。
- **M (Machine)**：内核线程，是真正执行代码的实体。
**调度流程**：M需要绑定一个P才能执行P本地队列中的G。这种M:N的映射关系（M个线程执行N个G）和work-stealing机制，使得Go能高效地利用多核资源，实现高并发。

### Q3: 协程有哪些缺点？
1.  **无法利用多核（对于纯协作式调度）**：单个线程内的协程无法实现真正的并行。但Go等语言通过M:N模型解决了这个问题。
2.  **CPU密集型任务效率不高**：如果一个协程长时间占用CPU进行计算，会阻塞同一线程上的其他协程。需要手动或由抢占机制来处理。
3.  **调试困难**：由于协程调用栈在用户态，传统的调试工具可能难以跟踪。
4.  **需要语言和框架支持**：协程的实现依赖于语言的运行时环境。
