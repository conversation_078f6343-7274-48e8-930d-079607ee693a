# I/O系统与设备管理 (精简版)

## 核心概念

### I/O 系统层次结构
用户程序 -> 系统调用 -> 文件系统 -> 通用块层 -> I/O调度器 -> 设备驱动 -> 中断处理 -> 硬件

### I/O 设备分类
- **按数据传输方式**:
  - **字符设备**: 键盘、鼠标 (以字符为单位)
  - **块设备**: 硬盘、SSD (以块为单位)
  - **网络设备**: 网卡 (网络数据包)
- **按访问方式**:
  - **顺序访问**: 磁带
  - **随机访问**: 硬盘、内存

---

## I/O 控制方式

| 控制方式 | 核心思想 | 优点 | 缺点 |
|---|---|---|---|
| **程序直接控制 (轮询)** | CPU主动、持续查询设备状态。 | 实现简单。 | CPU利用率低，忙等待。 |
| **中断驱动** | 设备完成操作后，通过中断信号通知CPU。 | CPU利用率高，可执行其他任务。 | 中断有开销，高频I/O会有效率问题。 |
| **DMA (直接内存访问)** | 由DMA控制器负责内存和设备间的数据传输，完成后中断CPU。 | CPU开销极小，适合大批量数据传输。 | 硬件要求高，设置有开销。 |

---

## I/O 调度算法 (磁盘)

- **FCFS (先来先服务)**:
  - **策略**: 按请求到达顺序处理。
  - **优劣**: 公平，但未优化寻道时间，效率低。

- **SSTF (最短寻道时间优先)**:
  - **策略**: 优先处理离当前磁头最近的请求。
  - **优劣**: 性能好，平均寻道时间短。但可能导致"饥饿"。

- **SCAN (电梯算法)**:
  - **策略**: 磁头沿一个方向移动，处理所有同向请求，到达磁盘末端后反向。
  - **优劣**: 克服了SSTF的饥饿问题，但对两端请求不公平。

- **C-SCAN (循环扫描)**:
  - **策略**: 类似SCAN，但到达一端后立即返回另一端重新开始，只在一个方向上处理请求。
  - **优劣**: 比SCAN更公平。

- **LOOK / C-LOOK**:
  - **策略**: SCAN/C-SCAN的优化版，磁头移动到最远的请求处即返回，而不是磁盘末端。

- **CFQ (完全公平队列)**:
  - **策略**: Linux 2.6内核默认算法。为每个进程维护一个I/O队列，以时间片轮转方式调度，保证公平性。

---

## 核心组件与技术

### 设备驱动程序
- **作用**: 硬件和操作系统之间的软件接口，封装硬件细节。
- **结构 (Linux)**: 通过 `file_operations` 结构体向VFS注册 `open`, `read`, `write` 等操作。
- **中断处理**: 驱动程序注册中断处理函数，用于响应硬件发出的中断信号，进行数据处理或状态更新。

### 缓冲与缓存
- **I/O缓冲 (Buffering)**:
  - **目的**: 缓解CPU与I/O设备速度不匹配的矛盾；减少I/O中断次数；提高数据传输效率。
  - **实现**: 如内核空间的环形缓冲区。
- **页缓存 (Page Cache)**:
  - **目的**: 加速文件I/O。将磁盘文件的内容缓存到物理内存中。读操作先查缓存，写操作先写入缓存（Write-Back / Write-Through）。
  - **相关技术**: 预读 (Read-ahead) 技术，提前将可能要访问的数据读入缓存。

### 性能优化
- **I/O合并 (Merging/Coalescing)**: 将多个相邻的、小的I/O请求合并成一个大的请求，减少寻道次数和请求开销。
- **异步I/O (AIO)**: 应用发起I/O操作后无需等待即可返回，当操作完成时通过回调、信号等方式通知应用。提高程序并发能力。

---

## 面试速查

- **不同I/O控制方式对比?**
  - **轮询**: 简单，CPU浪费大。
  - **中断**: CPU利用率高，但中断本身有开销。
  - **DMA**: CPU解放最彻底，适合海量数据，但硬件复杂。

- **如何选择I/O调度算法?**
  - **SSTF**: 追求最大吞吐量，能容忍饥饿。
  - **SCAN/C-SCAN**: 吞吐量和公平性的折中。
  - **CFQ**: 多用户、多进程环境，公平性是首要考虑。

- **设计驱动程序考虑什么?**
  - **硬件抽象**: 统一接口。
  - **中断处理**: 及时响应。
  - **并发控制**: 锁机制保证数据安全。
  - **缓冲管理**: 平衡性能与内存。
  - **错误处理**: 健壮性。
