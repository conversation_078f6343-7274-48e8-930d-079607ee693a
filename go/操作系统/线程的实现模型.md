# 线程实现模型（面试速记版）

面试核心是理解三种模型的**映射关系**、**优缺点**和**关键差异**。

## 模型速览

| 特性 | 用户级线程 (N:1) | 内核级线程 (1:1) | 混合模型 (M:N) |
|:--- |:---|:---|:---|
| **映射** | 多个用户线程 -> 1个内核线程 | 1个用户线程 -> 1个内核线程 | M个用户线程 -> N个内核线程 |
| **优点** | 创建/切换**快**，资源占用**少** | **真并行**，充分利用多核，独立调度 | 结合前两者优点，高性能，可并行 |
| **缺点** | **一损俱损** (一阻塞全阻塞)，无法利用多核 | 创建/切换**慢**，开销**大** | 实现**极度复杂** |
| **调度** | 用户态线程库 | 内核 | 用户态库 + 内核 |
| **代表** | 一些早期的线程库 | Java, C++, Linux Pthreads | Go (GPM) |

## 面试核心要点

1.  **关键区别**：**线程调度是在用户态还是内核态**。这是根本区别。
2.  **性能权衡**：用户级线程快但不并行，内核级线程并行但开销大。混合模型是二者的折中。
3.  **阻塞影响**：用户级线程的阻塞是**进程级别**的，而内核级线程的阻塞是**线程级别**的。
4.  **Go语言模型**：务必记住Go语言的GPM是典型的**M:N混合模型**，这是高频考点。