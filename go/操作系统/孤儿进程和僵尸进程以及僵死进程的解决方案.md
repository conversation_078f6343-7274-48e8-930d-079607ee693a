### 孤儿进程 (Orphan Process)
- **定义**: 父进程已终止，但子进程仍在运行。
- **回收**: 由 `init` 进程 (PID=1) 自动接管并回收资源。
- **结论**: 无需手动干预。

### 僵尸进程 (Zombie Process)
- **定义**: 子进程已终止，但父进程未通过 `wait()` 或 `waitpid()` 获取其退出状态，导致其进程描述符仍保留在系统中。
- **危害**: 占用进程号（PID），过多会导致无法创建新进程。
- **解决方案**:
    - 父进程中调用 `wait()` / `waitpid()` 回收子进程。
    - 父进程捕获 `SIGCHLD` 信号，在信号处理函数中调用 `wait()`。
    - `kill` 父进程，使僵尸进程变为孤儿进程，交由 `init` 进程回收。

### 僵死进程 (Defunct/Stuck Process)
- **定义**: (非正式术语) 指进程因死循环、等待无法获得的资源等原因，无法正常终止或响应信号。
- **解决方案**:
    - 使用 `kill -9` (`SIGKILL`) 强制终止。
    - 通过 `top`, `ps`, `strace` 等工具监控和定位问题。
    - 通过 `ulimit` 提前限制进程可用的资源。

### 核心区别
- **孤儿进程**: 有进程实体，但无父进程，由 `init` 领养，会被正常回收。
- **僵尸进程**: 无进程实体，但有进程表条目，等待父进程回收。
- **僵死进程**: 有进程实体，但卡死不动，需手动干预。