# Go 内存泄漏排查（面试速查）

## 核心定义
- **内存泄漏**：无用内存因被错误引用，导致GC无法回收。
- **根本原因**：GC只回收**不可达**对象。只要有任何可达引用指向对象，它就不会被回收。

## 常见泄漏场景 & 对策

| 场景 | 描述 | 对策 |
| --- | --- | --- |
| **Goroutine泄漏** | 因`channel`阻塞/无限循环无法退出 | 使用`context`或`done channel`管理生命周期 |
| **资源未关闭** | 文件、网络连接、DB连接等未`Close()` | `defer`确保资源释放 |
| **长生命周期引用** | 全局`map/slice`等持续添加元素 | 定期清理、限制容量、使用指针或弱引用 |
| **无界缓存** | 缓存无淘汰策略(如LRU) | 设置容量上限，实现淘汰策略 |
| **CGO调用** | `C`代码分配的内存需手动`free` | 严格遵守`C`内存管理规则 |

## 排查工具与流程

### 核心工具：pprof
- **开启服务**：`import _ "net/http/pprof"`
- **分析堆内存**：`go tool pprof http://<host:port>/debug/pprof/heap`
- **关键命令**:
  - `top`: 查看内存占用最高的函数
  - `list <func_regex>`: 定位到具体代码行
  - `web`: 生成可视化火焰图 (需安装 `graphviz`)
- **对比快照**：`go tool pprof -http=:8081 --base v1.heap v2.heap`，分析内存增量。

### 排查四步走
1. **监控发现**：通过监控（如`runtime.MemStats`）发现内存持续增长。
2. **pprof定位**：使用`pprof`工具，通过`top`和`list`命令找到可疑的内存分配点。
3. **代码分析**：对照上述常见场景，审查代码逻辑，找出泄漏原因。
4. **修复验证**：修复后进行压力测试，观察内存使用是否平稳。 