# CPU调度与上下文切换

## 调度基础概念

### 调度时机
- **进程阻塞**：等待I/O操作
- **进程终止**：执行完毕
- **时间片用完**：抢占式调度
- **高优先级进程就绪**：优先级抢占

### 调度目标
- **公平性**：合理分配CPU时间
- **效率**：最大化CPU利用率
- **响应时间**：最小化延迟
- **吞吐量**：单位时间完成任务数

## 经典调度算法

| 算法 | 原理 | 优点 | 缺点 | 适用场景 |
|------|------|------|------|----------|
| **FCFS** | 先来先服务 | 简单公平 | 护航效应 | 批处理系统 |
| **SJF** | 最短作业优先 | 等待时间短 | 长作业饥饿 | 已知执行时间 |
| **RR** | 时间片轮转 | 响应时间好 | 切换开销大 | 交互式系统 |
| **优先级** | 按优先级调度 | 灵活控制 | 可能饥饿 | 实时系统 |
| **多级反馈** | 多队列+动态调整 | 自适应强 | 实现复杂 | 通用系统 |

### 多级反馈队列特点
- **自动分类**：区分I/O密集型和CPU密集型进程
- **动态调整**：进程可在队列间移动
- **响应优化**：短进程快速完成
- **无需预知**：不需要预知执行时间

## 上下文切换

### 切换过程
1. **保存当前进程状态**：寄存器、程序计数器
2. **切换内存空间**：更新页表、刷新TLB
3. **切换内核栈**：更新栈指针
4. **恢复新进程状态**：加载寄存器

### 切换开销

| 开销类型 | 具体内容 | 时间成本 |
|----------|----------|----------|
| **直接开销** | 寄存器保存/恢复 | 几十个周期 |
| | 页表切换、TLB刷新 | 几百个周期 |
| | 缓存失效 | 数千个周期 |
| **间接开销** | 缓存局部性丢失 | 持续影响 |
| | 分支预测失效 | 重新学习 |
| | 内存预取失效 | 重新适应 |

### 优化方法
- **使用线程**：共享地址空间，减少内存切换
- **使用协程**：用户态切换，开销最小
- **CPU亲和性**：减少进程迁移
- **异步I/O**：避免阻塞切换

## Linux调度器

### CFS完全公平调度器
- **核心思想**：基于虚拟运行时间(vruntime)
- **数据结构**：红黑树维护就绪进程
- **调度策略**：选择vruntime最小的进程
- **公平性**：保证长期公平分配CPU时间

### 实时调度策略
- **SCHED_FIFO**：先进先出，不被同级抢占
- **SCHED_RR**：轮转调度，有时间片限制
- **SCHED_DEADLINE**：截止时间调度，硬实时

### 其他调度策略
- **SCHED_NORMAL**：普通进程，使用CFS
- **SCHED_BATCH**：批处理任务，降低交互性
- **SCHED_IDLE**：空闲任务，最低优先级

## 性能优化

### 分析工具
- **perf**：分析调度延迟和切换频率
- **top/htop**：查看CPU使用和进程状态
- **vmstat**：系统整体性能指标

### 优化策略
- **CPU亲和性**：绑定进程到特定CPU核心
- **NUMA优化**：考虑内存访问局部性
- **负载均衡**：合理分配任务到不同CPU

## 面试高频问题

### Q1: 时间片大小如何选择？
- **太小**：上下文切换开销大，系统吞吐量下降
- **太大**：响应时间差，退化为FCFS
- **经验值**：10-100毫秒，需要平衡响应时间和开销

### Q2: 如何解决优先级调度的饥饿问题？
1. **老化技术**：随时间增加低优先级进程的优先级
2. **多级反馈队列**：动态调整进程优先级
3. **公平调度**：保证每个进程最小CPU时间

### Q3: 上下文切换的主要开销是什么？
1. **直接开销**：寄存器保存恢复、页表切换
2. **间接开销**：缓存失效、分支预测失效
3. **持续影响**：缓存重新预热、内存访问延迟

### Q4: Linux CFS调度器的优势？
- **公平性**：基于虚拟运行时间保证公平
- **可扩展性**：红黑树O(log n)复杂度
- **自适应**：自动适应不同类型的工作负载
- **无饥饿**：保证每个进程都能获得CPU时间
