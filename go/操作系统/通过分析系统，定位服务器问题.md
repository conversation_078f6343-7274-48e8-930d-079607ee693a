# 服务器性能问题排查速查

## 核心排查思路
1.  **全局监控**: `top`看负载, `free`看内存, `iostat`看I/O, `nethogs`/`ss`看网络.
2.  **定位瓶颈**: CPU、内存、I/O、网络哪个是瓶颈？
3.  **深入进程**: `ps`看进程, `lsof`看文件, `strace`看系统调用.
4.  **分析日志**: 系统日志 (`/var/log/syslog`), 应用日志, 内核日志 (`dmesg`).

## 面试核心问答

### Q1: 服务器响应慢，如何排查？
1.  `top`: 查整体负载。`load average`高？`us`/`sy`/`wa`哪个高？
2.  `free -h`: 查内存。`available`是否过低？`swap`是否被使用？
3.  `iostat -x 1`: 查I/O。`%util`是否接近100%？`await`是否过高？
4.  `ss -s` / `nethogs`: 查网络。连接数、流量是否异常？
5.  `dmesg`: 查内核日志有无OOM等错误。
6.  **分析**：结合以上信息，定位是CPU、内存还是I/O瓶颈，再深入分析。

### Q2: 如何定位CPU使用率高的原因？
1.  `top` -> 按`P`: 按CPU使用率排序，找到最耗CPU的进程 (PID)。
2.  `top -H -p <PID>`: 查看该进程下哪个线程CPU高 (TID)。
3.  `pstack <TID>` / `jstack <PID>`: 打印线程栈，看在做什么。
4.  `strace -p <PID>`: 跟踪系统调用，看是否系统调用过多。
5.  `perf top -p <PID>`: 分析函数级CPU消耗。
6.  **结论**:
    - `us`高: 应用逻辑问题，优化代码/算法。
    - `sy`高: 系统调用频繁，查`strace`看是什么调用，考虑I/O或锁竞争。
    - `wa`高: I/O瓶颈，转去查I/O问题。

### Q3: 内存使用率高怎么处理？
1.  `free -h` 和 `top`: 确认`available`内存低，`swap`被使用。
2.  `ps aux --sort=-%mem | head`: 找出最耗内存的进程。
3.  **分析原因**:
    - **内存泄漏**: 用`valgrind`(C/C++)或`pprof`(Go)等工具分析。
    - **内存溢出 (OOM)**: `dmesg | grep OOM`查看内核日志。
    - **缓存过高**: 正常现象，但如果挤占过多应用内存，需排查。
4.  **解决方案**:
    - **治标**: 加内存、加Swap。
    - **治本**: 优化代码，修复内存泄漏，优化数据结构，引入缓存策略(Redis等)。

### Q4: I/O 繁忙如何排查？
1.  `iostat -x 1`: 查看`%util` (接近100%?)、`r/s`, `w/s` (读写频率)、`await` (等待时间)。
2.  `iotop`: 找出进行大量I/O操作的进程。
3.  `lsof -p <PID>`: 查看进程打开了哪些文件，定位被频繁读写的文件。
4.  `strace -e trace=io -p <PID>`: 观察`read`/`write`系统调用。
5.  **解决方案**:
    - **优化程序**: 减少I/O操作，使用缓存。
    - **优化数据库**: 慢查询优化，加索引。
    - **升级硬件**: 使用SSD。
    - **文件系统**: 选择合适的I/O调度器。

## 关键命令与指标解读

- **CPU (`top`)**:
    - `us`: 用户态CPU。高 -> 应用逻辑复杂。
    - `sy`: 内核态CPU。高 -> 系统调用多，I/O频繁。
    - `wa`: I/O等待。高 -> 磁盘/网络I/O瓶颈。
    - `load average`: 1, 5, 15分钟平均负载。大于CPU核数则过载。

- **内存 (`free -h`, `vmstat 1`)**:
    - `available`: 可用内存。
    - `swap`: 交换分区。`si`/`so` > 0 -> 内存压力大。
    - `buff/cache`: 缓存。系统会自动回收。

- **I/O (`iostat -x 1`)**:
    - `%util`: 设备繁忙率。 > 80% -> 瓶颈。
    - `await`: 平均I/O等待时间(ms)。

- **网络 (`ss`, `nethogs`)**:
    - `ss -s`: 连接统计，看`TIME_WAIT`等状态。
    - `nethogs`: 按进程统计网络带宽。