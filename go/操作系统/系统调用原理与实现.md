# 系统调用核心速查

## 1. 核心原理

*   **是什么**：用户程序请求内核服务的 **唯一接口**。
*   **为什么**：
    *   **安全**：隔离用户程序和硬件，通过权限检查保护系统资源。
    *   **抽象**：提供统一、稳定的接口，屏蔽底层硬件差异。
*   **如何工作（调用流程）**：
    1.  **用户态**：应用程序调用库函数（如 `read`）。
    2.  **陷入(Trap)**：库函数执行 `SYSCALL` (x86-64) 或 `int 0x80` (x86-32) 指令，CPU 从 **用户态 (Ring 3)** 切换到 **内核态 (Ring 0)**。
    3.  **内核态**：
        *   **保存上下文**：保存用户态寄存器等现场信息。
        *   通过 **系统调用号** 在 **系统调用表** 中查找并执行对应的内核函数。
        *   执行内核函数，期间会进行参数验证和权限检查。
    4.  **返回**：将结果存入寄存器，**恢复上下文**，切换回用户态，返回给应用程序。

## 2. 与函数调用的核心区别

| 对比项 | 系统调用 | 普通函数调用 |
|---|---|---|
| **执行空间** | 内核态 | 用户态 |
| **开销** | **高** (涉及2次上下文切换) | **低** |
| **安全性** | 内核保障，更安全 | 依赖程序自身逻辑 |
| **数据传递** | 用户态/内核态拷贝 | 栈或寄存器 |

## 3. 性能与安全

### 主要性能开销
*   **上下文切换**：保存/恢复寄存器、切换页表等，是主要开销来源。
*   **参数拷贝**：数据在用户态和内核态之间来回拷贝。

### 优化策略
*   **减少调用次数**：
    *   使用缓冲区，进行批量读写（Buffered I/O）。
    *   使用 `readv`/`writev` 等批量操作接口。
*   **避免内核态切换**:
    *   **vDSO (Virtual Dynamic Shared Object)**：内核将一些安全、频繁的调用（如 `gettimeofday`）映射到用户空间，程序在用户态直接执行，无需陷入内核，效率极高。
    *   **io_uring**：Linux 下最高性能的异步 I/O 接口，通过环形缓冲区与内核共享数据，极大减少系统调用次数。

### 安全保障
*   **参数验证**：严格检查用户传入的指针地址、数值范围，防止恶意攻击。
*   **权限检查**：基于 UID/GID 和 capabilities 判断操作是否被允许。

## 4. 常见系统调用与调试

### 常用示例
*   **文件操作**: `open`, `read`, `write`, `close`, `lseek`
*   **进程控制**: `fork`, `execve`, `wait4`, `exit`
*   **内存管理**: `brk`, `mmap`, `munmap`
*   **通信**: `pipe`, `socket`
*   **信息获取**: `getpid`, `gettimeofday`

### 调试工具
*   `strace <command>`: 跟踪程序执行的 **系统调用** 及其参数和返回值。
*   `ltrace <command>`: 跟踪程序调用的 **库函数**。
