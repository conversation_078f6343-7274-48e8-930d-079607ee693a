# 堆和栈：面试速查

## 核心结论
栈的访问效率远高于堆。

## 核心差异速览

| 维度 | 栈 (Stack) | 堆 (Heap) |
| :--- | :--- | :--- |
| **分配/回收** | O(1) 指针移动，自动 | 复杂算法，手动/GC |
| **内存布局** | 连续，后进先出 (LIFO) | 分散，可能碎片化 |
| **访问方式** | 直接访问 | 指针间接访问 |
| **缓存友好性** | ✅ 高 | ❌ 低 |
| **管理开销** | 极低 | 较高 |

## 为什么栈更快？
1.  **分配与回收快**: 栈上分配内存只是移动栈顶指针，释放时也是如此，开销极小。堆上分配（如 `malloc`）需要在空闲链表中寻找合适的内存块，回收时（如 `free`）还可能涉及复杂的内存合并，开销大。
2.  **缓存局部性好**: 栈上数据是连续存放的，且符合后进先出原则，这使得数据在时间和空间上都有很好的局部性。CPU 缓存能有效预读和命中数据，减少了对主内存的访问。堆上数据是离散分布的，容易导致缓存未命中（Cache Miss）。
3.  **访问开销低**: 访问栈上数据是直接通过栈指针加上一个偏移量，是一条汇编指令就能完成。访问堆上数据需要先获取指针，再通过指针解引用访问，多了一次内存访问。

## 如何选择？

**优先使用栈**，除非：

1.  **动态大小**: 编译时无法确定所需内存大小（例如，动态数组）。
2.  **生命周期长**: 需要在函数返回后依然存在的变量（例如，全局对象，函数返回的指针）。
3.  **大对象**: 数据量过大，可能导致栈溢出（Stack Overflow）。
4.  **跨线程共享**: 需要在多个线程间共享的数据。

## 性能优化建议
- **多用栈**: 尽可能将对象分配在栈上。
- **减少分配**: 使用对象池（`sync.Pool`）或内存池来复用对象，减少堆分配和 GC 压力。
- **数据结构紧凑**: 优化数据结构，使其更紧凑，提高缓存命中率。
- **避免频繁小对象分配**: 如果可能，将多个小对象合并成一个大对象进行一次性分配。