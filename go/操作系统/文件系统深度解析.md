# 文件系统核心知识速查

## 1. 核心概念

- **文件系统作用**: 文件存储、目录管理、空间管理、访问控制、元数据管理。
- **抽象层次**: `应用 -> VFS -> 具体文件系统 -> 块设备驱动 -> 物理设备`
- **VFS (虚拟文件系统)**:
  - **作用**: 提供统一接口，隐藏底层文件系统细节。
  - **核心结构**: `super_block` (文件系统信息), `inode` (文件元数据), `dentry` (目录项), `file` (打开的文件)。

## 2. `inode` 机制

- **定义**: 索引节点，存储文件除文件名外的所有元数据（如权限、大小、数据块指针等）。
- **特点**: 文件名与 inode 分离。文件名存储在目录中，目录项建立了 **文件名 -> inode 编号** 的映射。

## 3. 目录实现对比

| 方式 | 查找复杂度 | 优点 | 缺点 |
| :--- | :--- | :--- | :--- |
| 线性列表 | O(n) | 实现简单 | 查找慢，删除开销大 |
| 哈希表 | O(1) | 查找快 | 需要处理哈希冲突 |
| B+树 | O(log n) | 性能均衡，有序 | 实现复杂 |

## 4. 磁盘空间分配

- **连续分配**: 访问快（无寻道），但会产生外部碎片。
- **链式分配**: 无外部碎片，但随机访问慢（需遍历链表）。
- **索引分配**: 支持随机访问，无外部碎片，但大文件需要多级索引，占用额外空间。

## 5. 性能优化：缓存

- **页缓存 (Page Cache)**: 内核中用于缓存文件数据的内存区域。读写操作优先命中缓存。
- **目录项缓存 (Dentry Cache)**: 缓存目录项（`路径 -> inode` 的映射），加速路径查找。
- **inode 缓存**: 缓存 `inode` 信息。

## 6. 性能优化：I/O 策略

- **预读 (Read-ahead)**: 预测并提前读取顺序数据块，减少 I/O 等待。
- **延迟写 (Write-back/Delayed Write)**: 数据先写入页缓存，标记为"脏页"，后续由内核批量刷入磁盘。
- **I/O 调度**: 通过算法（如电梯算法）合并、排序 I/O 请求，减少磁头寻道时间。

## 7. 一致性与恢复

- **日志 (Journaling)**:
  - **机制**: 写前日志 (Write-Ahead Logging, WAL)，在执行数据/元数据变更前，先将操作写入日志。
  - **模式**:
    - **元数据日志**: 只记录元数据变更。性能好，但意外断电可能导致已写入的数据和元数据不匹配。
    - **数据日志 (完整日志)**: 记录所有变更。最安全，但开销巨大，性能较差。
- **fsck**: 系统崩溃后，用于检查和修复文件系统不一致性的工具。

## 8. 常见文件系统

| 文件系统 | 核心特点 | 适用场景 |
| :--- | :--- | :--- |
| ext4 | 成熟稳定，日志功能，延迟分配 | Linux 通用桌面和服务器 |
| XFS | 高性能，为大文件和目录优化 | 视频存储、大数据 |
| Btrfs | 写时复制 (CoW)，快照，内建卷管理 | 现代 Linux，容器，灵活存储 |
| ZFS | 极强的数据完整性校验，CoW，快照 | 企业级存储，NAS |
