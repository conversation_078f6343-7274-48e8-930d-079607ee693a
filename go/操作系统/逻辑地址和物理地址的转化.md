### 核心概念

- **逻辑地址 (虚拟地址):**
  - CPU 生成，进程视角下的地址。
  - 每个进程拥有独立的、从 0 开始的逻辑地址空间。

- **物理地址:**
  - 内存（RAM）中的实际地址。
  - 所有进程共享物理内存。

### 地址转换核心

- **内存管理单元 (MMU):** 硬件，负责将逻辑地址转换为物理地址。
- **快表 (TLB):** MMU 内的高速缓存，存储最近的页表项，加速转换。

### 主要转换方式

#### 1. 分页 (Paging)

- **原理:** 将逻辑和物理内存都划分为固定大小的块（页/页框）。
- **逻辑地址:** `页号` + `页内偏移`。
- **转换过程:**
  1. CPU 给出逻辑地址。
  2. MMU 从中提取 `页号`。
  3. MMU 查询**页表**，根据 `页号` 找到对应的 `物理页框号`。
  4. `物理页框号` + `页内偏移` 组合成最终的物理地址。

#### 2. 分段 (Segmentation)

- **原理:** 将逻辑地址空间划分为多个不同长度的段（如代码段、数据段）。
- **逻辑地址:** `段号` + `段内偏移`。
- **转换过程:**
  1. CPU 给出逻辑地址。
  2. MMU 从中提取 `段号`。
  3. MMU 查询**段表**，根据 `段号` 找到段的 `基址` 和 `段长`。
  4. 检查段内偏移是否越界（小于段长）。
  5. `段基址` + `段内偏移` 计算出物理地址。

#### 3. 段页式结合 (Segmentation with Paging)

- **原理:** 先分段，再对每个段进行分页。
- **转换过程:** 逻辑地址 → (段表) → 线性地址 → (页表) → 物理地址。

### 总结

| 特性 | 逻辑地址 | 物理地址 |
| --- | --- | --- |
| 来源 | CPU | 内存 |
| 视角 | 进程独立 | 系统共享 |
| 转换 | MMU (硬件) + TLB (缓存) | - |

**转换方式对比:**

| 方式 | 优点 | 缺点 |
| --- | --- | --- |
| **分页** | 内存利用率高，无外部碎片 | 内部碎片，页表占用额外内存 |
| **分段** | 逻辑清晰，易于共享和保护 | 外部碎片，内存利用率低 |