# 进程间通信（IPC）核心速查

## 各IPC方式对比

| 方式 | 核心特点 | 速度 | 数据量 | 同步 | 适用场景 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **共享内存** | 多进程映射同一块物理内存，零拷贝 | **最快** | 大 | **需要** | 高性能、大数据量交换 |
| **消息队列** | 内核维护的消息链表，自带同步 | 中等 | 中等 | 内置 | 任务分发、异步解耦 |
| **管道** | `匿名`: 父子进程, 单向 <br> `命名`: 任意进程, 双向 | 中等 | 小 | 无 | 简单流式数据传输 |
| **套接字** | 可用于本机或跨网络通信 (TCP/UDP) | 较慢 | 任意 | 无 | 网络/本地复杂通信 |
| **信号** | 异步通知机制，只传递信号，不传数据 | 快 | 无 | 无 | 事件通知、异常处理 |
| **信号量** | PV操作控制对资源的访问，是同步工具 | - | 无 | **是** | 进程/线程同步控制 |

## 如何选择IPC？

- **性能与数据量**
    - **最高性能/大数据**：共享内存 (+信号量/互斥锁)
    - **中等数据/需解耦**：消息队列
    - **小数据**：管道
    - **仅通知**：信号
- **进程关系**
    - **父子进程**：匿名管道
    - **同主机任意进程**：命名管道、消息队列、共享内存
    - **跨网络**：套接字

## 面试核心问题

### Q1: 共享内存为什么最快？
- **零拷贝**：数据无需在用户态和内核态之间拷贝。
- **无内核介入**：一旦映射完成，读写操作直接访问内存，无需内核干预。
- **最少系统调用**：除了初始设置，后续通信无系统调用。

### Q2: 共享内存如何同步？
- **信号量 (Semaphore)**：控制访问共享资源的进程数量。
- **互斥锁 (Mutex)**：保证任一时刻只有一个进程访问，实现互斥。
- **条件变量 (Condition Variable)**：与互斥锁配合，实现复杂的等待/通知逻辑。

### Q3: 消息队列 vs 共享内存？
- **消息队列**：
    - **优点**: 自带同步，使用简单，进程解耦。
    - **缺点**: 有拷贝开销，性能不如共享内存。
- **共享内存**：
    - **优点**: 性能最高，无拷贝开销。
    - **缺点**: 需手动同步，实现复杂。

### Q4: 管道的局限性？
1.  **方向**：匿名管道是单向的（半双工）。
2.  **关系**：匿名管道只能用于有亲缘关系的进程（父子/兄弟）。
3.  **大小**：缓冲区大小有限制。
4.  **格式**：是字节流，无消息边界，需自行定义协议。