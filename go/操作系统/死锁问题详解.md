# 死锁核心知识点速查

## 1. 什么是死锁？

两个或多个进程因争夺资源而造成的一种互相等待的现象，若无外力作用，都将无法推进。

## 2. 死锁的四个必要条件 (缺一不可)

- **互斥 (Mutual Exclusion)**: 资源一次只能被一个进程使用。
- **占有和等待 (Hold and Wait)**: 进程至少保持一个资源，并正在请求其它进程占有的资源。
- **不可剥夺 (No Preemption)**: 资源不能被强制剥夺，只能由占有者自愿释放。
- **循环等待 (Circular Wait)**: 存在一个进程-资源的循环等待链。

## 3. 死锁处理策略

### 策略一：死锁预防

通过破坏四个必要条件之一来预防死锁。

- **破坏互斥**: 允许多个进程共享资源（如：只读文件），但实用性有限。
- **破坏占有和等待**:
    - 一次性申请所有资源。
    - 申请不到新资源时，释放已有资源。
- **破坏不可剥夺**: 允许系统强行抢占资源。
- **破坏循环等待**: 对资源进行线性排序，按序申请。

### 策略二：死锁避免

在资源分配时，通过算法（如 **银行家算法**）判断本次分配是否会导致系统进入 **不安全状态**，从而避免死锁。

- **安全状态**: 系统能找到一个安全序列，使所有进程都能顺利执行完毕。
- **银行家算法核心**: 保证系统始终处于安全状态。只有当一次分配后系统仍是安全的，才分配资源。

### 策略三：死锁检测与恢复

允许死锁发生，但系统能检测到并从中恢复。

- **检测**: 使用 **资源分配图**，检测图中是否存在 **环路**。
- **恢复**:
    - **进程终止**: 终止所有或部分死锁进程。
    - **资源抢占**: 从一个或多个进程中抢占资源。

## 4. 常见死锁场景及解决方案

### 数据库死锁
- **原因**: 多个事务以不同顺序锁定多个资源（表、行）。
- **解决**:
    - **检测**: 数据库系统（如MySQL）会自动检测死锁（等待图）。
    - **恢复**: 回滚其中一个事务（通常是代价最小的）。
    - **预防**:
        - 约定锁顺序。
        - 缩短事务范围，尽快提交。
        - 使用更低的隔离级别（要评估风险）。
        - 添加锁等待超时。

### 多线程死锁 (开发中最常见)
- **原因**: 多个线程以不同顺序获取多个锁（互斥锁）。
- **解决/预防**:
    - **锁排序**: 所有线程按相同顺序获取锁。
    - **锁超时**: `try_lock` 或设置超时，获取失败后释放已持有的锁，然后重试。
    - **减少锁粒度**: 使用更细粒度的锁，减少锁的持有时间。
    - **避免嵌套锁**: 尽量避免在一个锁内获取另一个锁。
    - **使用无锁数据结构**: 如 `CAS` 原子操作。

## 5. 面试核心问题

**Q1: 死锁的四个必要条件？**
互斥、占有和等待、不可剥夺、循环等待。

**Q2: 如何预防死锁？**
破坏四个条件之一：资源共享（破坏互斥）、一次性申请资源（破坏占有等待）、允许抢占（破坏不可剥夺）、资源排序（破坏循环等待）。

**Q3: 银行家算法是用来做什么的？**
用于 **死锁避免**。它通过在资源分配前进行计算，确保系统不会进入不安全状态，从而避免死锁。

**Q4: 开发中如何避免死锁？**
1.  **锁排序**: 最常用，按固定顺序加锁。
2.  **锁超时**: 避免无限等待。
3.  **减少锁粒度/范围**: 尽快释放锁。
4.  **死锁检测工具**: 如 `go pprof` 可以帮助分析。

**Q5: MySQL 如何处理死锁？**
MySQL的InnoDB引擎有自动的死锁检测机制，发现死锁后，会自动回滚一个事务来解除死锁。
