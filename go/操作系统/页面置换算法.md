# 页面置换算法（面试精简版）

## 核心面试要点

### 1. 各算法精髓对比

| 算法 | 核心思想 | 优点 | 缺点 | 复杂度 |
|---|---|---|---|---|
| **FIFO** | 先进先出 | 简单公平 | Belady异常，效率不高 | O(1) |
| **LRU** | 最近最少使用 | 性能好，符合局部性原理 | 实现复杂，开销大 | O(1) with Hash+List |
| **Clock** | LRU的低开销近似 | 近似LRU，开销小 | 性能略差于LRU | O(1) |
| **OPT** | 未来最长时间不使用 | 理论最优，作为性能基准 | 无法实现 | - |
| **LFU**| 最不经常使用 | 考虑历史使用频率 | 无法适应程序行为变化 | O(logN) to O(1) |

### 2. 高频问题速记
*   **LRU如何高效实现?**
    *   **双向链表 + 哈希表**: 达到 O(1) 复杂度。哈希表存 `key -> 节点指针`，链表维护访问顺序（新访问的移到头部）。
*   **为什么LRU性能好?**
    *   **时间局部性原理**：一个数据项被访问后，在短时间内有很大可能性再次被访问。
*   **什么是Belady异常?**
    *   **现象**：为FIFO算法分配更多物理页框，缺页率反而可能上升。
    *   **原因**：FIFO未考虑页面访问历史，可能换出热点页。
    *   **不会出现的算法**：LRU、OPT 等栈式算法。
*   **什么是抖动 (Thrashing)? 如何避免?**
    *   **现象**：页面频繁换入换出，导致CPU大部分时间都在处理缺页中断，系统性能急剧下降。
    *   **原因**：进程所需内存(工作集) > 分配的物理内存(驻留集)。
    *   **避免**：
        *   使用工作集模型，监控缺页率。
        *   保证 `驻留集 >= 工作集`。
        *   当缺页率过高时，挂起部分进程或增加物理内存。
*   **实际操作系统用什么算法?**
    *   多数系统采用 **Clock算法** 或其改进版，作为LRU的低开销近似实现。
    *   **Linux**: 采用的是LRU的近似算法，将页面分为活跃和非活跃链表。
    *   **Windows**: 基于工作集(Working Set)的算法。

## 各算法核心点

### 1. FIFO (先进先出)
- **核心**: 维护一个队列，淘汰队首（最早进入）的页面。
- **问题**: Belady异常，会换出常用页面。

### 2. LRU (最近最少使用)
- **核心**: 淘汰最长时间未被使用的页面。
- **实现**:
    - **双向链表 + 哈希表**: 每次访问将页面移到链表头。O(1) 查找和更新。
- **优点**: 性能好，完美利用局部性原理。

### 3. Clock (时钟/最近未使用 NUR)
- **核心**: LRU的低成本近似实现。
- **机制**:
    - 页面组织成**环形链表**，有一个**访问位 (Reference Bit)**。
    - 页面被访问时，访问位置1。
    - 需要换页时，时钟指针扫描环形链表：
        - 访问位为 **0** -> **选中淘汰**。
        - 访问位为 **1** -> 置 **0**，指针后移，给页面第二次机会。
- **改进型 (Enhanced Clock)**: 增加**修改位 (Dirty Bit)**，优先淘汰未被修改的页面 `(访问位=0, 修改位=0)`，以减少磁盘I/O写操作。

### 4. OPT (最佳置换)
- **核心**: 淘汰在未来最长时间内不会被访问的页面。
- **价值**: 理论最优算法，作为衡量其他算法性能的**基准**，但无法实现。

### 5. LFU (最不经常使用)
- **核心**: 淘汰过去一段时间内访问次数最少的页面。
- **问题**:
    - **历史问题**: 早期访问频率高的页面可能长期占据内存。
    - **解决**: 可以通过定期衰减访问计数来缓解此问题。

## 关键概念摘要
- **缺页中断 (Page Fault)**: 访问的页面不在物理内存中，触发中断，操作系统从磁盘加载页面到内存。这是页面置换的主要开销来源。
- **工作集 (Working Set)**: 一个进程在某段时间窗口内，集中访问的页面集合。如果工作集能常驻内存，进程就能高效运行。
- **抖动 (Thrashing)**: 进程的工作集大小远大于分配给它的物理内存，导致不断发生缺页中断，系统性能急剧下降的现象。
