# 线程间通信与同步精简版

## 通信方式对比

| 方式 | 效率 | 复杂度 | 核心场景 | 关键点 |
|---|---|---|---|---|
| **共享内存** | 最高 | 高 | 高频数据交换 | 必须手动同步，易出错 |
| **消息队列** | 中等 | 中等 | 任务分发、解耦 | 有拷贝开销，天然同步 |
| **条件变量** | 高 | 高 | 复杂同步逻辑 | 与锁配合，防止虚假唤醒 |
| **信号量** | 高 | 低 | 控制并发资源数 | 简单有效的资源计数器 |
| **Channel (Go)** | 中等 | 低 | Go语言并发 | 类型安全，自带同步 |

## 核心通信与同步机制

- **共享内存**: 多个线程直接访问同一块内存。
  - **优点**: 效率最高（零拷贝）。
  - **缺点**: 需要手动加锁（如`Mutex`）来保证线程安全，易产生竞态条件。

- **消息队列**: 线程通过一个队列发送和接收消息。
  - **优点**: 解耦发送方和接收方，支持异步，天然同步。
  - **缺点**: 数据需要拷贝，有额外开销。

- **互斥锁 (Mutex)**: 保证同一时间只有一个线程能访问受保护的资源。
  - **特点**: 独占、阻塞式等待。是解决竞态条件最常用的工具，但要小心死锁。

- **读写锁 (RWLock)**: "读"共享，"写"独占。
  - **优点**: 在读多写少的场景下，比互斥锁有更高的并发性。
  - **缺点**: 可能导致"写者饥饿"。

- **条件变量 (Condition Variable)**: 允许线程等待某个条件成立。
  - **用法**: 必须与互斥锁配合使用，以原子方式检查条件并进入等待，防止竞态和虚假唤醒。
  - **场景**: 生产者-消费者模型。

- **自旋锁 (Spinlock)**: 获取不到锁时，线程不会睡眠，而是循环"忙等"。
  - **优点**: 避免了线程上下文切换的开销。
  - **缺点**: 浪费CPU。
  - **场景**: 锁持有时间极短，且在多核CPU上。

- **原子操作 (Atomic)**: 由CPU保证其不可分割的指令（如CAS）。
  - **优点**: 无锁化，性能高，无死锁风险。

## 面试核心要点

- **共享内存 vs 消息传递**
  - **共享内存**: 追求极致效率，数据量大。
  - **消息传递**: 追求安全、解耦和简化编程模型。

- **如何避免死锁？**
  1. **加锁顺序**: 所有线程按相同顺序获取锁。
  2. **超时放弃**: 尝试获取锁时，设置超时时间。
  3. **避免嵌套**: 尽量不持有多个锁。
  4. **死锁检测**: 使用工具或算法检测循环等待。

- **自旋锁 vs 互斥锁？**
  - **自旋锁**: 适用于多核系统，且锁的持有时间**极短**（纳秒/微秒级别）。
  - **互斥锁**: 适用于锁持有时间较长或不定的情况，会引起线程休眠和上下文切换。

- **为什么条件变量要和互斥锁一起用？**
  - **保护条件**: 互斥锁保证在检查和修改"条件"时不被其他线程干扰。
  - **防止虚假唤醒**: 从`wait`返回后，需要重新在锁的保护下检查条件是否真的满足。