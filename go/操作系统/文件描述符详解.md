# 文件描述符核心速查

> 面试速记版

## 1. 核心定义
- **文件描述符 (FD)**: 内核为I/O资源分配的**非负整数**，是进程访问该资源的**句柄**。
- **基本特性**:
  - **进程私有**: 每个进程有独立的FD表。
  - **`fork`继承**: 子进程继承父进程的FD表副本。
  - **标准FD**: `0` (stdin), `1` (stdout), `2` (stderr)。

## 2. 三层核心映射
`进程FD表` → `系统级文件表` → `inode表`

- **进程FD表 (Per-Process)**: 进程私有，`FD -> 文件表项指针`。
- **系统文件表 (System-wide)**: 内核共享，包含**文件偏移量(offset)**、状态标志、引用计数。
- **inode表 (System-wide)**: 内核共享，包含**文件元数据**(权限、大小)和磁盘块指针。

> **面试关键点**: `fork`后，父子进程的FD指向**同一个系统文件表项**，因此**共享文件偏移量**。`dup`/`dup2`同理。

## 3. 关键操作 & 限制
- **`open`/`socket`**: 创建一个FD。
- **`close`**: 关闭一个FD，递减文件表项引用计数。
- **`dup`/`dup2`**: 复制FD，指向同一文件表项。`dup2`可指定新FD号，用于**IO重定向**。
- **限制与错误**:
  - **错误**: `"Too many open files"` (FD耗尽)。
  - **查看/修改**: `ulimit -n` (会话级), `/etc/security/limits.conf` (永久)。
- **泄漏排查**:
  - `ls -l /proc/<PID>/fd/`: 查看某进程打开的FD。
  - `lsof -p <PID>`: 查看FD详情（包括文件、socket）。

## 4. I/O多路复用
**目标**: 单线程监控多个FD，高并发网络编程基石。

| 特性       | select                                 | poll                                   | epoll (Linux)                          |
| ---------- | -------------------------------------- | -------------------------------------- | -------------------------------------- |
| **FD限制** | 有 (FD_SETSIZE, 通常1024)              | 无                                     | 无                                     |
| **拷贝开销** | 每次都拷贝FD集合 (用户态↔内核态)       | 每次都拷贝FD集合 (用户态↔内核态)       | 仅`epoll_ctl`时拷贝，之后共享内存      |
| **扫描方式** | 线性扫描，轮询所有FD (O(n))            | 线性扫描，轮询所有FD (O(n))            | 事件驱动，只返回就绪FD (O(1))          |
| **适用场景** | 连接数少且固定                         | `select`的替代品，解决FD数量限制       | 高并发、大量连接 (Nginx)               |

### Epoll 核心模式
- **水平触发 (LT - Level Triggered)**: **默认模式**。只要FD就绪，每次`epoll_wait`都返回。
- **边缘触发 (ET - Edge Triggered)**: **高效模式**。仅当FD状态从"未就绪"变为"就绪"时通知一次。**必须一次性处理完所有数据**。
