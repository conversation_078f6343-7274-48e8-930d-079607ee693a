# CPU 调度算法核心笔记

## 核心调度算法速查

| 算法 | 核心思想 | 抢占式 | 优点 | 缺点 | 关键点/面试题 |
|---|---|---|---|---|---|
| **FCFS** <br/> 先来先服务 | 按到达顺序执行 | 否 | 公平、简单 | 平均等待时间长，**护航效应** (短进程等长进程) | 不适合交互系统 |
| **SJF** <br/> 最短作业优先 | 执行时间最短的优先 | 可选 (SRTF) | **平均等待时间最短**，吞吐量高 | 预测执行时间困难，可能导致**长作业饥饿** | 理论最优，实际难用 |
| **优先级调度** | 优先级高的先执行 | 可选 | 灵活，满足不同需求 | 可能导致**低优先级饥饿** | 如何解决饥饿？ **老化(Aging)** 技术 |
| **RR** <br/> 时间片轮转 | 每个进程固定时间片轮流执行 | 是 | 公平，**响应时间好** | 上下文切换有开销，周转时间可能较长 | **时间片大小选择**是关键，不能太大或太小 |
| **MLFQ** <br/> 多级反馈队列 | 多个优先级队列，进程可在队列间移动 | 是 | 综合性能好，无需预测执行时间 | 实现复杂 | **现代操作系统常用**，能兼顾响应时间和吞吐量 |

---

## 关键概念与面试题

### Q1: 抢占式 vs 非抢占式调度？
- **抢占式 (Preemptive)**:
  - **特点**: 操作系统可强制中断当前进程，分配CPU给其他进程 (如更高优先级进程就绪、时间片用完)。
  - **例子**: RR, SRTF (抢占式SJF), MLFQ。
  - **优点**: 响应快，适合交互式、实时系统。
- **非抢占式 (Non-preemptive)**:
  - **特点**: 进程只有在执行完毕或主动阻塞时才释放CPU。
  - **例子**: FCFS, SJF。
  - **优点**: 实现简单，上下文切换开销小。

### Q2: 什么是饥饿 (Starvation)？如何解决？
- **饥饿**: 在优先级调度中，低优先级进程可能长时间无法获得CPU时间。
- **解决方案**:
  - **老化 (Aging)**: 随等待时间增加，逐步提升进程的优先级。
  - **CFS等公平调度算法**: 从设计上保证每个进程获得成比例的CPU时间。

### Q3: 时间片轮转 (RR) 的时间片大小如何选择？
- **太小**: 上下文切换过于频繁，导致大量CPU时间浪费在调度上，系统效率降低。
- **太大**: 响应时间变长，如果时间片大到超过大部分进程的执行时间，会退化成 FCFS。
- **平衡点**: 一般选择 10-100ms，需在响应时间和切换开销间做权衡。

---

## 现代操作系统调度器简介

- **Linux CFS (Completely Fair Scheduler)**:
  - **目标**: 实现完全公平。
  - **核心**: 使用 **虚拟运行时间 (vruntime)** 记录进程的运行量。
  - **机制**: 每次选择 `vruntime` 最小的进程执行。使用**红黑树**来管理就绪队列，实现 O(log N) 的高效查找。

- **Windows**:
  - 采用 **32个优先级** 的多级反馈队列。
  - 对**前台窗口**的进程有优先级和时间片加成，以保证交互应用的流畅性。

- **macOS**:
  - 基于 Mach 内核的线程调度。
  - 引入 **QoS (服务质量)** 概念，根据任务类型（如用户交互、后台任务）进行分类调度。

---

## 实时调度 (可选)

- **RM (速率单调调度)**: 静态优先级，周期越短，优先级越高。
- **EDF (最早截止时间优先)**: 动态优先级，截止时间点越早，优先级越高。
- **LST (最少松弛时间优先)**: 动态优先级，松弛时间 (`截止时间 - 当前时间 - 剩余执行时间`) 越少，优先级越高。
