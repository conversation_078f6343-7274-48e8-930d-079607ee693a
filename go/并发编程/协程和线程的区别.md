# Goroutine与线程的区别

## 核心对比

| 特性 | Goroutine | 线程 |
|------|-----------|------|
| **调度方式** | 用户态调度(GPM模型) | 内核态调度 |
| **内存占用** | 初始2KB，可动态扩展 | 通常8MB固定栈 |
| **创建开销** | 极低，纳秒级 | 较高，微秒级 |
| **切换开销** | 用户态切换，开销小 | 内核态切换，开销大 |
| **数量限制** | 可创建百万级 | 受系统资源限制 |

## 详细区别

### 1. 调度机制
- **Goroutine**: Go运行时调度器(GPM)管理，协作式调度
- **线程**: 操作系统内核调度，抢占式调度

### 2. 内存模型
- **Goroutine**: 栈内存动态增长，初始仅2KB
- **线程**: 固定栈大小，通常8MB，浪费内存

### 3. 通信方式
- **Goroutine**: Channel通信，"通过通信来共享内存"
- **线程**: 共享内存+锁，"通过共享内存来通信"

### 4. 错误隔离
- **Goroutine**: panic只影响当前goroutine
- **线程**: 异常可能影响整个进程

## 适用场景

### Goroutine适合
- 高并发网络服务
- I/O密集型任务
- 需要大量并发单元的场景

### 线程适合
- CPU密集型计算
- 需要真正并行处理
- 与C库交互的场景

## 面试要点
1. **内存优势**: Goroutine初始栈仅2KB vs 线程8MB
2. **调度优势**: 用户态调度避免内核态切换开销
3. **通信模型**: Channel vs 锁的设计哲学差异
4. **并发数量**: Goroutine可轻松创建百万级并发