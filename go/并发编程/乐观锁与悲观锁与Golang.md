# 乐观锁与悲观锁在Go中的实现

## 基本概念

### 悲观锁
假设并发冲突频繁，操作前先加锁，确保独占访问。

### 乐观锁
假设并发冲突较少，操作时不加锁，提交时检查冲突。

## Go语言实现

### 1. 悲观锁实现

**Mutex互斥锁**
```go
var mu sync.Mutex
var count int

func increment() {
    mu.Lock()
    defer mu.Unlock()
    count++
}
```

**RWMutex读写锁**
```go
var rwMu sync.RWMutex
var data int

func readData() int {
    rwMu.RLock()
    defer rwMu.RUnlock()
    return data
}

func writeData(newData int) {
    rwMu.Lock()
    defer rwMu.Unlock()
    data = newData
}
```

### 2. 乐观锁实现

**CAS原子操作**
```go
var counter int64

func increment() {
    for {
        old := atomic.LoadInt64(&counter)
        new := old + 1
        if atomic.CompareAndSwapInt64(&counter, old, new) {
            break
        }
        // 可选：添加退避策略
        runtime.Gosched()
    }
}
```

**版本号机制**
```go
type Data struct {
    Value   int
    Version int64
}

func updateWithVersion(d *Data, newValue int) bool {
    oldVersion := atomic.LoadInt64(&d.Version)
    // 模拟业务处理
    newVersion := oldVersion + 1

    // CAS更新版本号
    if atomic.CompareAndSwapInt64(&d.Version, oldVersion, newVersion) {
        d.Value = newValue
        return true
    }
    return false // 需要重试
}
```

## 性能对比

| 特性 | 悲观锁 | 乐观锁 |
|------|--------|--------|
| 冲突处理 | 阻塞等待 | 重试机制 |
| 适用场景 | 写多读少 | 读多写少 |
| 性能 | 冲突时性能稳定 | 无冲突时性能更好 |
| 死锁风险 | 存在 | 无 |

## 面试要点
1. **选择依据**：根据读写比例和冲突频率选择
2. **CAS原理**：Compare-And-Swap的原子性保证
3. **ABA问题**：乐观锁可能遇到的经典问题
4. **性能权衡**：锁竞争vs重试开销