# 并发安全与竞态条件

## 竞态条件基础

### 什么是竞态条件
多个goroutine同时访问共享资源，且至少有一个是写操作，没有适当同步机制时产生的不确定结果。

**典型表现**：
- 计数器结果不准确
- 数据结构状态不一致
- 程序行为不可预测

### 竞态检测
使用Go内置的竞态检测器：
- **编译**: `go build -race`
- **运行**: `go run -race main.go`
- **测试**: `go test -race`

### 竞态条件示例
```go
// 不安全的计数器
type UnsafeCounter struct {
    count int
}

func (c *UnsafeCounter) Increment() {
    c.count++  // 非原子操作，存在竞态
}

// 并发访问会导致计数不准确
var counter = &UnsafeCounter{}
for i := 0; i < 1000; i++ {
    go counter.Increment()  // 最终结果可能小于1000
}
```

## 并发安全解决方案

### 1. 互斥锁（Mutex）
```go
type SafeCounter struct {
    mu    sync.Mutex
    value int
}

func (c *SafeCounter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.value++
}

func (c *SafeCounter) Value() int {
    c.mu.Lock()
    defer c.mu.Unlock()
    return c.value
}
```

### 2. 读写锁（RWMutex）
```go
type SafeMap struct {
    mu   sync.RWMutex
    data map[string]int
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()         // 写锁
    defer sm.mu.Unlock()
    sm.data[key] = value
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()        // 读锁
    defer sm.mu.RUnlock()
    value, exists := sm.data[key]
    return value, exists
}
```

### 3. 原子操作
```go
import "sync/atomic"

type AtomicCounter struct {
    value int64
}

func (c *AtomicCounter) Increment() {
    atomic.AddInt64(&c.value, 1)
}

func (c *AtomicCounter) Value() int64 {
    return atomic.LoadInt64(&c.value)
}

func (c *AtomicCounter) CompareAndSwap(old, new int64) bool {
    return atomic.CompareAndSwapInt64(&c.value, old, new)
}
```

**原子操作优势**：
- 性能比互斥锁更好
- 无锁编程，避免死锁
- 适合简单的数值操作

### 4. Channel同步
```go
type ChannelCounter struct {
    ch    chan int
    value int
}

func NewChannelCounter() *ChannelCounter {
    cc := &ChannelCounter{ch: make(chan int)}
    go cc.process()  // 启动处理goroutine
    return cc
}

func (cc *ChannelCounter) process() {
    for delta := range cc.ch {
        cc.value += delta
    }
}

func (cc *ChannelCounter) Increment() {
    cc.ch <- 1
}
```

**Channel优势**：
- 符合Go的CSP模型
- 天然的并发安全
- 适合复杂的状态管理

## 性能对比

### 同步机制性能排序
1. **原子操作** - 最快，适合简单数值操作
2. **互斥锁** - 中等，适合复杂临界区
3. **读写锁** - 读多写少场景优化
4. **Channel** - 最慢，但提供更强的语义保证

### 选择建议
- **简单计数**: 使用原子操作
- **复杂状态**: 使用互斥锁
- **读多写少**: 使用读写锁
- **数据传递**: 使用Channel

## 死锁避免

### 死锁产生条件
1. **互斥条件**: 资源不能共享
2. **占有且等待**: 持有资源的同时等待其他资源
3. **不可剥夺**: 资源不能被强制释放
4. **循环等待**: 形成资源等待环路

### 避免策略
1. **锁排序**: 所有goroutine按相同顺序获取锁
2. **超时机制**: 使用select和time.After实现超时
3. **避免嵌套锁**: 尽量避免同时持有多个锁

```go
// 锁排序避免死锁
func lockInOrder(mu1, mu2 *sync.Mutex) {
    mu1.Lock()
    defer mu1.Unlock()

    mu2.Lock()
    defer mu2.Unlock()

    // 临界区代码
}

// 超时机制
func tryLockWithTimeout(ch chan struct{}, timeout time.Duration) bool {
    select {
    case ch <- struct{}{}:
        return true
    case <-time.After(timeout):
        return false
    }
}
```

## 面试要点

### 核心问题
1. **什么是竞态条件？**
   - 多个goroutine同时访问共享资源且至少一个写操作
   - 没有适当同步导致不确定结果

2. **如何检测竞态条件？**
   - 使用`go run -race`启用竞态检测器
   - 在测试中使用`go test -race`

3. **同步机制选择？**
   - 原子操作：简单数值操作
   - 互斥锁：复杂临界区
   - 读写锁：读多写少
   - Channel：数据传递和状态管理

4. **如何避免死锁？**
   - 锁排序：统一获取锁的顺序
   - 超时机制：避免无限等待
   - 避免嵌套锁：减少复杂性

### 最佳实践
- 使用defer释放锁
- 尽量缩小临界区范围
- 优先使用原子操作处理简单数值
- 遵循"通过通信来共享内存"的原则
