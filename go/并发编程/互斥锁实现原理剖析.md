# Go互斥锁实现原理

## 基本结构

```go
type Mutex struct {
    state int32  // 锁状态
    sema  uint32 // 信号量
}
```

## 状态位设计

`state`字段的位含义：
- **bit 0**: 锁状态(0=未锁定, 1=已锁定)
- **bit 1**: 唤醒标志
- **bit 2**: 饥饿模式标志
- **bit 3-31**: 等待goroutine数量

## 加锁流程

### 1. 快速路径
```go
func (m *Mutex) Lock() {
    // 尝试CAS获取锁
    if atomic.CompareAndSwapInt32(&m.state, 0, mutexLocked) {
        return // 成功获取锁
    }
    // 进入慢速路径
    m.lockSlow()
}
```

### 2. 慢速路径
1. **自旋等待**: 在多核CPU上短暂自旋
2. **加入等待队列**: 自旋失败后进入等待
3. **信号量阻塞**: 调用`runtime_SemacquireMutex`

## 解锁流程

```go
func (m *Mutex) Unlock() {
    // 原子减1
    new := atomic.AddInt32(&m.state, -mutexLocked)
    if new != 0 {
        m.unlockSlow(new) // 有等待者需要唤醒
    }
}
```

## 公平性机制

### 正常模式
- 新来的goroutine与被唤醒的goroutine竞争
- 新来的更容易获得锁(已在CPU上运行)

### 饥饿模式
- 等待时间超过1ms进入饥饿模式
- 锁直接传递给等待队列头部的goroutine
- 避免长时间等待的goroutine饿死

## 性能优化

### 1. 自旋条件
- 多核CPU
- GOMAXPROCS > 1
- 至少有一个其他运行的P
- 当前P的本地队列为空

### 2. 自旋次数限制
```go
const active_spin = 4 // 最多自旋4次
```

### 3. 信号量优化
- 使用runtime内部信号量
- 避免系统调用开销

## 面试要点
1. **CAS操作**: 原子性保证无锁快速路径
2. **自旋策略**: 减少goroutine切换开销
3. **饥饿模式**: 保证公平性，防止饿死
4. **性能权衡**: 吞吐量vs公平性的平衡