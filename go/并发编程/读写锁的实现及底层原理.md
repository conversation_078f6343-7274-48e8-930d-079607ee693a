# sync.RWMutex 读写锁核心笔记

> **一句话总结**: RWMutex 适用于读多写少场景，允许多个读者并发以提高性能，但它不支持锁的升级和降级。

---

## 核心原理

`RWMutex` 的核心是 `readerCount` 计数器，它巧妙地表示了三种状态：

1.  **`readerCount > 0`**: 只有读者。值为当前读者数量。
2.  **`readerCount = 0`**: 无锁状态。
3.  **`readerCount < 0`**: 有写者。为了区分是"写者持有锁"还是"写者在等待"，`RWMutex` 会先将 `readerCount` 减去一个巨大常量 `rwmutexMaxReaders`，标记"有写者在等待"。此时，真实的读者数量是 `readerCount + rwmutexMaxReaders`。

- **写者优先**: 当有写者请求锁时，会先将 `readerCount` 变为负数，这会阻塞后续新的读锁请求，防止写者饿死。
- **信号量**: 使用 `writerSem` 和 `readerSem` 两个信号量来分别阻塞和唤醒等待的写者和读者 `goroutine`。

---

## vs. Mutex

| 特性 | `RWMutex` | `Mutex` |
| --- | --- | --- |
| **优势** | 读多写少时性能高，允许多读并发。 | 实现简单，开销小，任何场景下表现稳定。 |
| **劣势** | 写多或读写均衡时性能不如 `Mutex`。 | 读多场景下，所有读者串行，性能较低。 |

**选择策略**: 明确是"读多写少"场景，且读操作有一定耗时，才考虑使用 `RWMutex`。否则 `Mutex` 是更简单、更安全的选择。

---

## 关键注意事项

### 1. 死锁风险：不支持锁升级与降级

`RWMutex` 的锁粒度管理非常严格，**不允许在持有读锁或写锁时再获取另一种锁**。

- **锁升级 (Deadlock!)**:
  ```go
  // 错误：持有读锁时，请求写锁
  rw.RLock()
  rw.Lock()   // <-- Fatal: deadlock!
  ```
- **锁降级 (Deadlock!)**:
  ```go
  // 错误：持有写锁时，请求读锁
  rw.Lock()
  rw.RLock()  // <-- Fatal: deadlock!
  ```
> **原因**: `Lock` 会等待所有 `reader` 释放锁，但它自己已经持有了 `RLock`，无法释放。`RLock` 会等待 `writer` 释放锁，但它自己就在 `Lock` 内部，无法执行。这就造成了循环等待。

### 2. 写者饥饿问题

虽然 `RWMutex` 的设计倾向于"写者优先"，但在高并发、读操作极度频繁的场景下，写者仍可能需要等待大量的读者完成操作才能获得锁。

---

## 面试要点 Checklist

- [ ] **和 Mutex 的区别？** (读多写少 vs. 通用)
- [ ] **核心实现是什么？** (`readerCount` 状态、信号量)
- [ ] **为什么 `readerCount` 会是负数？** (标记有写者等待)
- [ ] **是写者优先还是读者优先？** (写者优先，阻塞新读者)
- [ ] **有什么使用陷阱？** (死锁：不能锁升级/降级)
- [ ] **什么场景下用它？** (读操作远多于写操作，且读操作耗时，并发能带来收益)