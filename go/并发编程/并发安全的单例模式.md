# 并发安全的单例模式

## 核心概念

单例模式确保一个类只有一个实例，并提供全局访问点。在并发环境下需要特别注意线程安全。

## 实现方式对比

### 1. 非线程安全版本（错误）
```go
type Singleton struct {
    data string
}

var instance *Singleton

// 错误：并发不安全
func GetInstance() *Singleton {
    if instance == nil {
        instance = &Singleton{data: "singleton"}
    }
    return instance
}
```

### 2. 互斥锁版本
```go
type Singleton struct {
    data string
}

var (
    instance *Singleton
    mu       sync.Mutex
)

func GetInstance() *Singleton {
    mu.Lock()
    defer mu.Unlock()
    
    if instance == nil {
        instance = &Singleton{data: "singleton"}
    }
    return instance
}
```

### 3. 双重检查锁定（错误示例）
```go
// 错误：Go中不推荐这种方式
func GetInstanceDoubleCheck() *Singleton {
    if instance == nil {
        mu.Lock()
        defer mu.Unlock()
        if instance == nil {
            instance = &Singleton{data: "singleton"}
        }
    }
    return instance
}
```

### 4. sync.Once版本（推荐）
```go
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

func GetInstance() *Singleton {
    once.Do(func() {
        instance = &Singleton{data: "singleton"}
    })
    return instance
}
```

### 5. 包初始化版本
```go
type Singleton struct {
    data string
}

var instance = &Singleton{data: "singleton"}

func GetInstance() *Singleton {
    return instance
}
```

## sync.Once详解

### 实现原理
```go
type Once struct {
    done uint32
    m    Mutex
}

func (o *Once) Do(f func()) {
    if atomic.LoadUint32(&o.done) == 0 {
        o.doSlow(f)
    }
}

func (o *Once) doSlow(f func()) {
    o.m.Lock()
    defer o.m.Unlock()
    if o.done == 0 {
        defer atomic.StoreUint32(&o.done, 1)
        f()
    }
}
```

### 特点
- **原子操作**: 使用原子操作检查状态
- **双重检查**: 锁内外都检查状态
- **只执行一次**: 保证函数只执行一次
- **并发安全**: 多goroutine安全调用

## 实际应用场景

### 1. 数据库连接池
```go
type DBPool struct {
    db *sql.DB
}

var (
    dbPool *DBPool
    dbOnce sync.Once
)

func GetDBPool() *DBPool {
    dbOnce.Do(func() {
        db, err := sql.Open("mysql", "connection_string")
        if err != nil {
            panic(err)
        }
        dbPool = &DBPool{db: db}
    })
    return dbPool
}
```

### 2. 配置管理器
```go
type Config struct {
    Host string
    Port int
    settings map[string]interface{}
}

var (
    config *Config
    configOnce sync.Once
)

func GetConfig() *Config {
    configOnce.Do(func() {
        config = &Config{
            Host: "localhost",
            Port: 8080,
            settings: make(map[string]interface{}),
        }
        config.loadFromFile("config.json")
    })
    return config
}

func (c *Config) loadFromFile(filename string) {
    // 加载配置文件逻辑
}
```

### 3. 日志记录器
```go
type Logger struct {
    file *os.File
    mu   sync.Mutex
}

var (
    logger *Logger
    loggerOnce sync.Once
)

func GetLogger() *Logger {
    loggerOnce.Do(func() {
        file, err := os.OpenFile("app.log", 
            os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
        if err != nil {
            panic(err)
        }
        logger = &Logger{file: file}
    })
    return logger
}

func (l *Logger) Log(message string) {
    l.mu.Lock()
    defer l.mu.Unlock()
    
    timestamp := time.Now().Format("2006-01-02 15:04:05")
    l.file.WriteString(fmt.Sprintf("[%s] %s\n", timestamp, message))
}
```

## 高级模式

### 1. 带参数的单例
```go
type Database struct {
    connectionString string
    db *sql.DB
}

var (
    database *Database
    dbOnce sync.Once
)

func GetDatabase(connectionString string) *Database {
    dbOnce.Do(func() {
        db, err := sql.Open("mysql", connectionString)
        if err != nil {
            panic(err)
        }
        database = &Database{
            connectionString: connectionString,
            db: db,
        }
    })
    return database
}
```

### 2. 可重置的单例
```go
type ResettableSingleton struct {
    data string
    mu   sync.RWMutex
}

var (
    resettableInstance *ResettableSingleton
    resettableOnce sync.Once
)

func GetResettableInstance() *ResettableSingleton {
    resettableOnce.Do(func() {
        resettableInstance = &ResettableSingleton{
            data: "initial",
        }
    })
    return resettableInstance
}

func ResetInstance() {
    if resettableInstance != nil {
        resettableInstance.mu.Lock()
        resettableInstance.data = "reset"
        resettableInstance.mu.Unlock()
    }
}
```

### 3. 泛型单例（Go 1.18+）
```go
type Singleton[T any] struct {
    instance T
    once     sync.Once
}

func (s *Singleton[T]) Get(factory func() T) T {
    s.once.Do(func() {
        s.instance = factory()
    })
    return s.instance
}

// 使用示例
var dbSingleton Singleton[*sql.DB]

func GetDB() *sql.DB {
    return dbSingleton.Get(func() *sql.DB {
        db, _ := sql.Open("mysql", "connection_string")
        return db
    })
}
```

## 性能对比

### 基准测试
```go
func BenchmarkMutexSingleton(b *testing.B) {
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            GetInstanceMutex()
        }
    })
}

func BenchmarkOnceSingleton(b *testing.B) {
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            GetInstanceOnce()
        }
    })
}

func BenchmarkInitSingleton(b *testing.B) {
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            GetInstanceInit()
        }
    })
}
```

### 性能结果
| 方式 | 性能 | 内存分配 | 并发安全 |
|------|------|----------|----------|
| 互斥锁 | 慢 | 无额外分配 | ✓ |
| sync.Once | 快 | 无额外分配 | ✓ |
| 包初始化 | 最快 | 无额外分配 | ✓ |

## 注意事项

### 1. 初始化失败处理
```go
var (
    instance *Singleton
    once     sync.Once
    initErr  error
)

func GetInstance() (*Singleton, error) {
    once.Do(func() {
        instance, initErr = newSingleton()
    })
    return instance, initErr
}

func newSingleton() (*Singleton, error) {
    // 可能失败的初始化逻辑
    return &Singleton{}, nil
}
```

### 2. 避免循环依赖
```go
// 错误：可能导致循环依赖
type ServiceA struct {
    serviceB *ServiceB
}

type ServiceB struct {
    serviceA *ServiceA
}

// 正确：使用接口解耦
type ServiceA struct {
    serviceB ServiceBInterface
}

type ServiceBInterface interface {
    DoSomething()
}
```

### 3. 测试友好的设计
```go
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

func GetInstance() *Singleton {
    once.Do(func() {
        instance = newSingleton()
    })
    return instance
}

func newSingleton() *Singleton {
    return &Singleton{data: "singleton"}
}

// 测试时重置单例
func ResetForTesting() {
    instance = nil
    once = sync.Once{}
}
```

## 面试要点

1. **线程安全**: 理解并发环境下的安全问题
2. **sync.Once**: 掌握Go推荐的单例实现方式
3. **性能对比**: 了解不同实现方式的性能差异
4. **实际应用**: 数据库连接池、配置管理等场景
5. **注意事项**: 初始化失败、循环依赖、测试友好
6. **设计权衡**: 懒加载vs预加载的选择

### 一句话总结
> Go中推荐使用sync.Once实现并发安全的单例模式，它提供了高性能的一次性初始化保证
