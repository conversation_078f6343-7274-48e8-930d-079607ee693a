# Go GPM 调度模型 (面试速查版)

> **核心思想**: GPM模型通过 **用户态协程(G)**、**逻辑处理器(P)** 和 **系统线程(M)** 的协作，实现了高效的、支持大规模并发的调度。它利用 **工作窃取** 机制实现负载均衡，避免了昂贵的内核态线程切换。

---

## 1. G-P-M 都是什么？

| 组件 | 角色 | 关键特性 |
| --- | --- | --- |
| **G (Goroutine)** | **协程 / 执行单元** | - 轻量级 (初始栈2KB)<br>- 保存了函数执行的上下文 |
| **P (Processor)** | **逻辑处理器 / 调度器** | - `GOMAXPROCS` 决定其数量<br>- 拥有一个G的**本地运行队列 (LRQ)**<br>- 是M和G之间的"中介" |
| **M (Machine)** | **系统线程** | - OS线程的包装<br>- 真正执行G代码的实体<br>- M必须绑定一个P才能执行G |

---

## 2. G 是如何被调度的？ (调度循环)

M 会按照以下顺序为 P 寻找可执行的 G：

1.  **从P的本地队列 (LRQ) 获取**: 无锁，最高效。
2.  **从全局队列 (GRQ) 获取**: 加锁，多个P共享，作为备份。
3.  **从其他P窃取 (Work Stealing)**: 从其他P的LRQ**偷一半**G过来，实现负载均衡。
4.  **从网络轮询器 (Netpoller) 获取**: 如果都没有，检查网络I/O是否准备就绪，唤醒相应G。

---

## 3. 关键机制解析

### 工作窃取 (Work Stealing)
- **目的**: 负载均衡，让空闲的P"偷"活干，提高CPU利用率。
- **触发**: P的本地队列为空。
- **过程**: 随机选择另一个P，从其本地队列的**队尾**偷走一半的G。

### 系统调用处理
- **目标**: 防止M因一个G的系统调用而阻塞整个P的调度。
- **阻塞调用 (e.g., 文件读写)**:
    1.  M与P**解绑**，M独自进入阻塞等待。
    2.  运行时会为P寻找一个**空闲的M**或**创建新M**，继续执行P队列中的其他G。
    3.  系统调用结束后，原来的M会尝试重新获取P或进入休眠。
- **非阻塞调用 (e.g., 网络读写)**:
    - 通过 `netpoller` (epoll/kqueue) 机制，仅注册事件，G被挂起，M不阻塞，可以继续执行其他G。

### 抢占调度 (Go 1.14+)
- **目的**: 防止某个G长时间占用CPU，导致其他G饥饿。
- **方式**:
    - **协作式抢占**: Goroutine在函数调用时，运行时会检查是否需要抢占。
    - **异步抢占**: 通过发送信号中断正在运行的G，让其重新进入队列，给其他G机会。

---

## 4. GOMAXPROCS 是什么？
- **定义**: `runtime.GOMAXPROCS` 设置了P的数量，即 **同时** 能有多少个G在运行。
- **默认值**: CPU的核数。
- **调优建议**:
    - **CPU密集型**: 保持默认值（等于CPU核数）即可。
    - **I/O密集型**: 可适当调大此值，因为当G因I/O阻塞时，M会与P分离，P可以调度其他G在新的M上运行，提高并发度。但过大也会增加调度开销。

---

## 5. 面试快问快答

**Q: GPM解决了什么问题？**
A: 解决了传统内核线程模型下，线程创建成本高、切换开销大的问题，用更轻量的Goroutine实现大规模并发。

**Q: G、P、M的数量关系是怎样的？**
A: M:N关系。通常G的数量远大于M和P。P的数量由 `GOMAXPROCS` 决定，M的数量通常略多于P（为了处理阻塞系统调用）。

**Q: 一个G阻塞了会发生什么？**
A: 如果是**系统调用阻塞**，M会和P解绑，P会寻找新的M继续执行其他G。如果是**channel或锁阻塞**，G会被挂起，M会去执行P中的下一个G，M本身不阻塞。

**Q: Go调度器是公平的吗？**
A: 不是绝对公平的。它优先保证吞吐量和效率（例如通过本地队列），并通过抢占机制避免极端不公平（饥饿）。
