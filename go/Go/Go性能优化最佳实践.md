# Go性能优化速查

## 内存优化

- **对象池**: `sync.Pool`复用临时对象，减少GC压力。
- **预分配slice/map**: `make([]T, 0, size)`，一次性分配足够容量，避免扩容。
- **避免接口(interface)装箱**: interface类型会引入额外分配和GC。
- **高效字符串拼接**: `strings.Builder` > `bytes.Buffer` > `+`。
- **减少`[]byte`与`string`转换**: 每次转换都涉及内存拷贝。
- **切片(slice)重用**: `slice = slice[:0]`，重置长度，复用底层数组。
- **警惕切片内存泄漏**: `copy`需要的部分，而不是对大切片做子切片引用。

## CPU优化

- **数据结构与算法**: 选择最优时间/空间复杂度的实现。
- **空间换时间**: 使用缓存（如map）存储计算结果，避免重复计算。
- **并行计算**: 利用多核CPU，将计算密集型任务并行化。
- **循环优化**:
    - **内联**: 简单函数自动内联，或手动展开减少函数调用开销。
    - **缓存友好**: 按内存布局顺序访问数据（如遍历二维数组按行遍历）。
- **分支预测**:
    - **减少分支**: 用位运算等代替`if/else`。
    - **优化热路径**: 将概率高的分支放前面。

## 并发优化

- **Goroutine池**: 控制并发Goroutine数量，复用Goroutine，减少创建/销毁开销。
- **减小锁粒度**: 只锁必要部分，减少锁竞争。
- **读写锁**: `sync.RWMutex`用于"读多写少"场景，提升并发度。
- **无锁编程**: 使用`sync/atomic`包中的原子操作处理简单计数、标志位等。
- **合理Channel缓冲**: 合理设置缓冲大小，避免不必要的阻塞。
- **非阻塞Channel**: `select`配合`default`实现非阻塞收发。
- **批量操作**: 减少Channel通信频率，一次传递多个数据。

## I/O优化

- **连接池**: 复用TCP连接（如数据库、Redis连接池）。
- **批量操作**: 批量读写数据库/缓存，减少网络I/O往返。
- **缓冲I/O**: `bufio`包提供带缓冲的Reader/Writer，减少系统调用。
- **内存映射(mmap)**: 适合大文件的读写，减少内存拷贝。

## 编译与分析

- **逃逸分析**: `go build -gcflags="-m"`，观察变量是否逃逸到堆上。
- **去除调试信息**: `-ldflags="-w -s"`，减小最终二进制文件大小。
- **性能剖析(pprof)**:
    - **CPU Profile**: 找出CPU热点函数。
    - **Memory Profile**: 分析常驻内存和内存泄漏。
    - **Block Profile**: 查看导致Goroutine阻塞的操作。
    - **Mutex Profile**: 分析锁竞争情况。
- **基准测试(Benchmark)**:
    - `go test -bench .`: 运行基准测试。
    - `-benchmem`: 查看内存分配情况。
- **竞态检测**: `go run -race`或`go test -race`，检测并发数据竞争。

## 常见陷阱

- **Goroutine泄漏**: 无缓冲channel阻塞、`select`中无`default`或`case`退出、死循环。
- **Timer/Ticker泄漏**: `time.NewTimer`/`time.NewTicker`忘记调用`Stop()`。
- **闭包引用泄漏**: 闭包意外引用了其生命周期之外的大对象。
- **`defer`在循环中的陷阱**: `defer`在函数退出时执行，循环中可能导致资源（如文件句柄）耗尽。
- **字符串拼接性能**: 热点路径中避免使用`+`，优先`strings.Builder`。
- **map并发不安全**: 多Goroutine同时读写原生`map`会panic，需加锁或使用`sync.Map`。

## 面试要点

### 1. 优化策略
- **如何定位性能瓶颈？**
  - **首先**：通过监控（Prometheus, Grafana）发现宏观指标异常（CPU、内存、延迟）。
  - **然后**：使用`pprof`进行细致剖析，定位到具体函数和代码行。
  - **最后**：编写`Benchmark`验证优化效果。
- **内存优化方法？**
  - 减少分配：`sync.Pool`, 预分配, 避免`string`/`[]byte`转换。
  - 避免泄漏：Goroutine/Timer泄漏, 闭包引用。
  - GC调优：`GOGC`, `GOMEMLIMIT`。
- **并发优化技巧？**
  - Goroutine池控制并发。
  - 锁：减小粒度, `RWMutex`, `atomic`无锁。
  - Channel：合理缓冲, `select`+`default`。

### 2. 工具使用
- **pprof**:
  - `import _ "net/http/pprof"` 开启HTTP服务。
  - `go tool pprof http://.../debug/pprof/profile?seconds=30` (CPU)
  - `go tool pprof http://.../debug/pprof/heap` (Memory)
- **Benchmark**:
  - `func BenchmarkXxx(b *testing.B)`
  - `b.ResetTimer()` / `b.StopTimer()`
  - `b.Run("sub-benchmark", ...)`
- **GC调优**:
  - `GOGC=100`: 默认值，当新分配内存是上次GC后存活内存的100%时触发GC。调大减少GC频率，调小更早回收。
  - `GOMEMLIMIT`: Go 1.19+，设置一个内存使用软上限。

### 3. 实际案例
- **字符串拼接优化？**
  - `+`: 每次都创建新字符串，性能最差。
  - `fmt.Sprintf`: 内部使用反射，较慢。
  - `strings.Join`: 适用于已有字符串切片。
  - `bytes.Buffer`: 动态字节缓冲区。
  - `strings.Builder`: `bytes.Buffer`的优化版，直接操作`[]byte`，避免`string`/`[]byte`转换。性能最好。
- **`sync.Map` vs `map`+`RWMutex`?**
  - `sync.Map`:
    - **优点**: 针对"读多写少，且key相对稳定"场景高度优化，无锁读取`read map`，分段锁写入`dirty map`。
    - **缺点**: 不提供`Len()`方法，遍历(`Range`)无法删除。
  - `map`+`RWMutex`:
    - **优点**: 通用，灵活，能满足所有`map`操作。
    - **缺点**: 所有读操作需要获取读锁，所有写操作需要获取写锁，竞争激烈时性能不如`sync.Map`。
- **slice扩容机制？**
  - 当`append`时，若容量不足，会重新分配内存。
  - 新容量策略（Go 1.18+）：
    - 旧容量 < 256: 新容量 = 2 * 旧容量
    - 旧容量 >= 256: 新容量逐渐过渡到约1.25倍增长
  - 扩容会复制所有旧元素到新内存地址。
