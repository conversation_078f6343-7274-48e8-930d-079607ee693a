# Go 运行时 (runtime) 简析

Go 运行时系统是 Go 语言的核心，负责 Goroutine 调度、内存管理(GC)、和系统调用封装。

### 1. Goroutine 调度

- **G-P-M 模型**:
  - **G (Goroutine)**: 轻量级执行单元（协程）。
  - **P (Processor)**: 逻辑处理器，拥有G的本地运行队列，是调度的核心。
  - **M (Machine)**: 系统线程，是G的执行载体。
- **调度流程**: P从本地或全局队列获取G，交给M执行。通过 `gopark` 挂起，`goready` 恢复。

### 2. 内存管理

- **内存分配**:
  - **TCMalloc-based**: Go的内存分配器基于TCMalloc思想，分为多级缓存 (`mcache`, `mcentral`, `mheap`) 来减少锁竞争。
  - **栈 (Stack)**: Goroutine拥有自己的栈，初始大小2KB，可动态伸缩。
- **垃圾回收 (GC)**:
  - **三色标记法**: 使用并发、混合写屏障（Hybrid Write Barrier）的三色标记-清除算法。
  - **STW (Stop-the-World)**: GC过程中仅在特定阶段（如启动、终止）需要短暂暂停所有Goroutine。

### 3. 系统调用

- **封装与代理**: 运行时封装了操作系统的调用，避免G直接与内核交互。
- **非阻塞**: 当G发起阻塞性系统调用（如网络IO），运行时会将M与P分离，让M去等待系统调用返回，而P可以去绑定其他M继续执行别的G，从而避免线程阻塞。

### 4. 运行时调度

- **抢占式调度**:
  - **协作式**: 在函数调用入口进行检查，防止G长时间运行不被切换。
  - **基于信号 (Go 1.14+)**: 对于没有函数调用的紧密循环，运行时会发送信号来中断G，强制调度。
- **工作窃取 (Work Stealing)**: 当P的本地队列为空时，它会从全局队列或其他P的队列中"窃取"G来执行，以实现负载均衡。

### 5. 同步与性能

- **同步原语**: 运行时内部使用 `mutex` 和 `sema` (信号量) 等机制来保证并发安全和实现G的同步。
- **性能优化**:
  - **自旋锁 (Spinlock)**: 在多核CPU上，当锁竞争不激烈时，通过短暂的CPU空转（自旋）等待锁释放，避免线程上下文切换。
  - **内存池 (sync.Pool)**: 提供可复用的临时对象池，以减轻GC压力。