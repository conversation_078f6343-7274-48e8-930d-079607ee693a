# Go Channel 面试速查

## 1. 核心精粹 (Core Essence)
- **核心哲学**: "不要通过共享内存来通信，而应通过通信来共享内存" (Do not communicate by sharing memory; instead, share memory by communicating.)
- **主要作用**: 实现 Goroutine 间的 **同步** 与 **通信**。
- **底层结构**: `hchan` 结构体，主要包含：
    - `qcount`: 环形缓冲区中元素个数
    - `dataqsiz`: 环形缓冲区大小
    - `buf`: 环形缓冲区指针
    - `sendq`/`recvq`: 发送/接收等待队列 (双向链表)
    - `lock`: 互斥锁，保证并发安全

## 2. Channel 类型与操作
### 类型
- **无缓冲 (同步)**: `make(chan T)`
    - 发送和接收必须同时就绪，否则阻塞。
    - 用于强同步和保证事件顺序。
- **有缓冲 (异步)**: `make(chan T, N)`
    - 缓冲区满前发送不阻塞，缓冲区空前接收不阻塞。
    - 用于解耦、流量削峰。

### 基本操作
- **发送**: `ch <- value`
- **接收**: `value := <-ch`
- **检查关闭**: `value, ok := <-ch` (`ok` 为 `false` 表示已关闭)
- **关闭**: `close(ch)`

### Select 多路复用
- **作用**: 同时监听多个 Channel。
- **规则**:
    - 随机选取一个已就绪的 `case` 执行。
    - 若都未就绪，执行 `default` (实现非阻塞)。
    - 若无 `default`，则阻塞。
- **常用场景**:
    - `case <-time.After(d)`: 超时控制。
    - `case <-ctx.Done()`: 优雅退出。

## 3. 常见陷阱 (The Gotchas)
| 操作 | Nil Channel | Closed Channel |
| :--- | :--- | :--- |
| **接收 `<-ch`** | 永久阻塞 | 立即返回零值 |
| **发送 `ch<-`** | 永久阻塞 | `panic` |
| **关闭 `close()`**| `panic` | `panic` (重复关闭) |

- **死锁 (Deadlock)**:
    - 单 goroutine 对无缓冲 channel 同时收发。
    - 多个 goroutine 循环等待对方持有的 channel。

## 4. 核心面试题 (Key Interview Questions)
### Channel vs. Mutex
- **Channel**: 适用于 **数据传递** 和 **事件通知**。是 goroutine 间的所有权转移。代码逻辑清晰。
- **Mutex**: 适用于保护 **共享状态**（临界区）。在纯粹的 state protection 场景下，性能可能更高。

### Select 实现原理
1.  **锁定**: `select` 会锁定所有涉及的 channel。
2.  **遍历检查**:
    - **第一次遍历 (非阻塞)**: 检查所有 `case` 的 channel 是否就绪（可读/可写）。
        - 如果有多个就绪，**伪随机**选择一个执行，然后解锁并返回。
        - 如果没有就绪的，进入下一步。
    - **第二次遍历 (阻塞)**:
        - 将当前 goroutine 打包成 `sudog`，加入到所有 `case` 对应 channel 的 `sendq` 或 `recvq` 等待队列中。
        - `gopark` 让当前 goroutine 休眠。
3.  **唤醒**: 当某个 channel 被操作（发送/接收），它会唤醒等待队列中的 goroutine。
4.  **善后**: `select` 被唤醒后，会从所有之前注册的等待队列中移除自身，然后执行对应 `case`。

### 经典设计模式
- **工作池 (Worker Pool)**: 通过 channel 分发任务，控制并发数量。
- **扇入/扇出 (Fan-in/Fan-out)**: 聚合或分发数据流。
- **管道 (Pipeline)**: 将多个处理阶段串联，每个阶段是一个 goroutine，通过 channel 连接。
- **发布订阅 (Pub-Sub)**: 一个生产者，多个消费者接收相同信息。
