# Go性能分析利器：pprof 速查手册

## 1. pprof 是什么？

Go 语言官方内置的性能分析工具，用于分析和定位程序性能瓶颈。

**核心分析类型：**
- **CPU Profile**: CPU 占用分析 (`/debug/pprof/profile`)
- **Memory Profile**: 内存分配分析 (`/debug/pprof/heap`)
- **Goroutine Profile**: Goroutine 泄漏与调度分析 (`/debug/pprof/goroutine`)
- **Block Profile**: 阻塞操作分析 (`/debug/pprof/block`)
- **Mutex Profile**: 锁竞争分析 (`/debug/pprof/mutex`)

---

## 2. 如何启用 pprof？

### 方式一：集成到 HTTP 服务 (最常用)
只需匿名导入 `net/http/pprof` 包，即可通过 `http://<addr>/debug/pprof/` 访问。

```go
import (
    "net/http"
    _ "net/http/pprof" // 关键: 匿名导入
)

func main() {
    go http.ListenAndServe("localhost:6060", nil)
    // ... your application logic
}
```

### 方式二：手动启动与采集
适用于非 HTTP 服务或需要精确控制分析范围的场景。

```go
// 采集 CPU
f, _ := os.Create("cpu.prof")
defer f.Close()
pprof.StartCPUProfile(f)
defer pprof.StopCPUProfile()

// 采集内存
f, _ := os.Create("mem.prof")
defer f.Close()
pprof.WriteHeapProfile(f)
```

---

## 3. 如何分析数据？

### 核心工具命令
```bash
# 启动 Web UI (推荐，非常直观)
go tool pprof -http=:8080 <profile_file_or_url>

# 命令行交互模式
go tool pprof <profile_file_or_url>
```

### 关键分析方法

| 场景           | 采集命令 (URL)                                    | 分析重点                                          |
| -------------- | ------------------------------------------------- | ------------------------------------------------- |
| **CPU 占用高** | `/debug/pprof/profile?seconds=30`                 | **火焰图 (Flame Graph)**、`top` 命令查看热点函数    |
| **内存泄漏**   | `/debug/pprof/heap`                               | `inuse_space` 指标、对比不同时间的快照              |
| **Goroutine 泄漏** | `/debug/pprof/goroutine`                          | Goroutine 总数、查看 `leaky` 的 Goroutine 调用栈 |
| **程序阻塞**   | `/debug/pprof/block`                              | `top` 查看阻塞最久的操作 (e.g., channel, select)  |

### 命令行常用命令 (`(pprof)`)
- `top10`: 显示资源占用前 10 的函数。`flat` 表示函数自身耗时，`cum` 表示函数自身+调用链累计耗时。
- `list <function_name>`: 显示指定函数的代码，并标注每行业务的耗时/耗内存情况。
- `web`: 生成函数调用关系图（需安装 graphviz）。
- `peek <function_name>`: 查看函数调用栈。

---

## 4. 面试核心要点

**Q1: pprof 能分析什么？**
A: CPU、内存、Goroutine、阻塞、锁竞争。

**Q2: 如何定位 CPU 性能瓶颈？**
A: 使用 `go tool pprof` 采集 CPU profile，通过 **火焰图** 可视化分析，找到图中最宽的函数（热点函数），再结合 `list` 命令分析具体代码。

**Q3: 如何检测内存泄漏？**
A: 多次采集 heap profile，对比 `inuse_space` 是否持续增长。通过 `go tool pprof -base=old.prof new.prof` 对比两次快照，找到新增的内存分配来源。

**Q4: Goroutine 泄漏怎么排查？**
A: 访问 `/debug/pprof/goroutine` 页面，观察 Goroutine 总数是否随时间异常增长。使用 `?debug=2` 参数可以查看所有 Goroutine 的创建位置调用栈，定位无法退出的 Goroutine。

**Q5: 什么是火焰图（Flame Graph）？**
A: 一种可视化性能分析工具。
- **Y 轴**: 调用栈深度，上层是下层的父函数。
- **X 轴**: CPU 占用时间。**函数块越宽，表示其 CPU 占用时间越长**。
- **核心价值**: 直观地展示 CPU 主要消耗在哪些函数的调用链上。

**Q6: 生产环境使用 pprof 有什么注意点？**
A:
1.  **按需开启**: 默认通过环境变量或配置开关关闭 pprof 端口。
2.  **对性能有开销**: 虽然 pprof 做了优化，但采集本身仍会消耗资源，尤其是 CPU Profile。应避免长时间、高频采集。
3.  **安全**: pprof 端口应只对内网或通过安全隧道访问，避免暴露在公网。
