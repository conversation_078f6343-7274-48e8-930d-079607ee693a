# Go GC 速查手册

### 一句话总结
> Go采用**三色标记并发清除算法**，通过**写屏障(Write Barrier)**保证并发安全，实现低延迟的自动内存管理。

---

### 核心问答

**1. Go的GC是如何工作的？**
- **核心算法**：三色标记并发清除。
- **阶段**：
    1. **标记准备 (Mark Setup)**: 开启写屏障，短暂停顿(STW)。
    2. **并发标记 (Concurrent Marking)**: 与用户代码并发执行，扫描并标记所有可达对象。
    3. **标记终止 (Mark Termination)**: 完成标记，关闭写屏障，短暂STW。
    4. **并发清除 (Concurrent Sweeping)**: 与用户代码并发执行，回收未标记的内存。
- **关键技术**：
    - **三色标记法**：
        - **白**：潜在的垃圾。
        - **灰**：已标记，但其引用的对象待扫描。
        - **黑**：已标记，且其引用的对象也已扫描，是存活对象。
    - **写屏障 (Write Barrier)**：在并发标记期间，监控内存修改，保证标记的正确性。Go 1.8后使用**混合写屏障(Hybrid Write Barrier)**，进一步减少STW。

**2. 如何减少GC压力？**
- **减少对象分配**：
    - 使用 `sync.Pool` 复用对象。
    - 预分配切片和map容量 (`make(T, size, cap)`)。
- **避免内存泄漏**：
    - 注意goroutine泄漏。
    - 全局变量和长生命周期对象中的引用。
    - CGO调用的内存需要手动管理。
- **调整GC策略**：
    - 调整 `GOGC` 环境变量，用内存换CPU。`GOGC=200` 表示堆增长200%时触发GC。

**3. STW时间过长怎么办？**
- **分析GC**：设置 `GODEBUG=gctrace=1` 环境变量，观察GC日志，了解GC耗时和频率。
- **分析内存**：使用 `pprof` 分析内存分配热点 (`go tool pprof -http=:8080 http://.../debug/pprof/heap`)。
- **优化代码**：
    - 针对pprof结果，优化内存分配。
    - 避免在循环中创建大量临时对象。
    - 将大对象拆分为小对象。

---

### Go GC 演进里程碑
- **v1.3**: 标记-清除 (Mark-Sweep)，全程STW。
- **v1.5**: **并发标记**，三色标记法引入，大幅减少STW。
- **v1.8**: **混合写屏障**，去除了重新扫描栈的过程，STW降至亚毫秒级。
- **v1.14+**: **异步抢占**，允许在循环中抢占，进一步降低延迟。