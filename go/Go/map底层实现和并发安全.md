# Go Map核心要点速查

## 一、核心概念

- **一句话总结**: `map`是Go内置的哈希表，通过桶数组实现，使用链表法解决哈希冲突，支持渐进式扩容，但非并发安全。
- **基本特性**: 引用类型、键值对存储、动态扩容、遍历无序。
- **零值**: `nil`。对`nil` map进行读操作是安全的（返回零值），但写操作会引发`panic`。
- **是否可比较**: 只能与`nil`比较，不能直接比较两个map。

## 二、底层原理

### 数据结构
- **`hmap`**: `map`的运行时表现形式，存储元数据如元素数量(`count`)、桶数量的对数(`B`)、哈希种子(`hash0`)、桶数组指针(`buckets`)等。
- **`bmap` (bucket)**:
    - **`tophash`**: 存储哈希值的高8位，用于快速定位。
    - **`keys/values`**: 分别连续存放8个key和8个value，优化内存局部性。
    - **`overflow`**: 指向溢出桶的指针，形成链表。

### 查找过程
1.  **计算哈希**: `hash(key)` -> `64`位哈希值。
2.  **定位桶**: 哈希值的**低`B`位**用于确定`bucket`索引。
3.  **快速定位**: 哈希值的**高`8`位**在`tophash`数组中查找匹配项。
4.  **遍历查找**: 若`tophash`匹配，则在`bucket`内（或其溢出桶链表）中完整比较`key`。

### 扩容机制
- **触发条件**:
    1.  **负载因子 > 6.5**: 元素数量 / 桶数量。触发**翻倍扩容**（桶数量 x2）。
    2.  **溢出桶过多**: 溢出桶数量超过阈值。触发**等量扩容**（桶数量不变，整理数据以减少溢出桶）。
- **渐进式扩容**:
    - **特点**: 将扩容开销分摊到多次访问中，避免长时间"Stop The World"。
    - **过程**: 每次`map`访问（读/写/删）会迁移1-2个旧桶的数据到新桶。扩容期间，数据可能存在于新旧两个桶中，读操作需要检查两者，写操作只在新桶进行。

## 三、并发安全

- **为何不安全**: 并发读写会破坏`map`内部结构，导致数据竞争。Go的运行时包含检测机制，在并发写入时会直接`panic`。

- **解决方案对比**:
    | 方案 | 优点 | 缺点 | 适用场景 |
    | :--- | :--- | :--- | :--- |
    | **`sync.RWMutex`** | 实现简单，易于理解 | 锁粒度大，竞争激烈时性能差 | 并发不高的场景 |
    | **`sync.Map`** | 官方优化，读写分离，性能高 | 接口特殊，不支持类型安全 | 读多写少，key相对稳定 |
    | **`Channel`** | 避免锁，实现简单 | 串行处理，可能成为性能瓶颈 | 需要将操作串行化的场景 |

## 四、性能与实践

- **性能优化**:
    - **预分配容量**: `make(map[T1]T2, size)`可有效避免多次扩容带来的性能开销。
    - **选择Key类型**: 整数或小`struct`作为`key`通常比`string`性能更好。
    - **内存泄漏**: `map`只增不缩。对于大量删除的场景，若想回收内存，需重建`map`。

- **有序遍历**:
    - `map`本身无序。要实现有序遍历，需将`key`拷贝到`slice`，对`slice`排序，然后按序访问`map`。
