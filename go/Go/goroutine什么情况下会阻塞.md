# Goroutine 阻塞核心场景与应对策略

## 一、核心阻塞场景

*   **Channel 操作**:
    *   **无缓冲 (unbuffered)**: 发送和接收必须同时准备好，否则阻塞。
    *   **有缓冲 (buffered)**: 缓冲区满时发送阻塞，缓冲区空时接收阻塞。

*   **锁竞争 (`sync.Mutex`)**:
    *   当锁已被其他 Goroutine 持有时，再次调用 `Lock()` 会阻塞。

*   **I/O 操作**:
    *   **网络 I/O**: 如 HTTP 请求、TCP 连接读写。
    *   **文件 I/O**: 如文件读写操作。
    *   **数据库 I/O**: 如执行 SQL 查询。

*   **系统调用**:
    *   执行阻塞式的系统调用，如某些文件或网络操作。
    *   CGO 调用外部 C 函数。

*   **同步原语**:
    *   `sync.WaitGroup.Wait()`: 阻塞，直到计数器归零。
    *   `sync.Cond.Wait()`: 阻塞，等待 `Signal` 或 `Broadcast` 通知。

*   **Select 语句**:
    *   当所有 `case` 的操作都无法立即执行，并且没有 `default` 分支时，`select` 整体阻塞。

*   **时间相关**:
    *   `time.Sleep()`: 主动挂起 Goroutine。
    *   `<-time.After()`: 阻塞，直到指定的定时器到期。

## 二、避免阻塞的核心策略

*   **为 Channel 设置缓冲区**: 提高吞吐量，减少因生产消费速度不均导致的阻塞。

*   **使用 `select` + `time.After` (超时控制)**:
    *   避免无限期等待，为阻塞操作设置一个最长等待时间。

*   **使用 `select` + `default` (非阻塞操作)**:
    *   尝试执行操作，如果会阻塞则立即执行 `default` 分支，不挂起 Goroutine。

*   **使用 `context` (取消操作)**:
    *   通过 `ctx.Done()` 向下游 Goroutine 广播取消信号，使其从阻塞中退出，避免资源泄露。

## 三、面试速记要点

1.  **最常见阻塞**: Channel、锁、I/O。
2.  **Channel 阻塞**: 无缓冲 vs 有缓冲（满/空）。
3.  **应对策略**: 超时 (`time.After`)、非阻塞 (`default`)、取消 (`context`)。
4.  **死锁**: 注意锁的获取顺序，以及 `select` 和 Channel 结合使用时可能产生的逻辑死锁。