# Go Interface 速查

## 1. 核心数据结构

- **空接口 `interface{}` -> `eface`**:
  ```go
  // 代表任意类型
  type eface struct {
      _type *_type         // 动态类型信息
      data  unsafe.Pointer // 数据指针
  }
  ```

- **非空接口 `interface{...}` -> `iface`**:
  ```go
  // 代表含方法的接口
  type iface struct {
      tab  *itab          // 接口表 (itab)
      data unsafe.Pointer // 数据指针
  }
  ```

- **接口表 `itab`**: 连接接口与具体类型
  ```go
  // 每个 (接口类型, 具体类型) 组合全局唯一
  type itab struct {
      inter *interfacetype // 接口类型
      _type *_type         // 具体类型
      hash  uint32         // 具体类型哈希，用于快速查找
      fun   [1]uintptr     // 方法函数指针列表，动态派发用
  }
  ```

## 2. 关键机制

- **接口赋值**:
  1.  **编译时**: 检查类型是否实现接口。
  2.  **运行时**: 创建 `iface` 或 `eface`。
  3.  `iface.tab` 指向一个全局唯一的 `itab`，代表(接口类型, 具体类型)对。`itab` 在首次赋值时被创建并缓存。
  4.  `data` 指向值的副本（小对象）或指针（大对象）。

- **方法调用**:
  - `iface.tab.fun[i]` 找到函数指针进行动态调用。
  - `iface.data` 作为方法接收者传入。

- **类型断言 `v, ok := i.(T)`**:
  - 比较 `iface.tab._type` 是否与目标类型 `T` 的类型信息一致。
  - `switch v := i.(type)` 也是基于 `_type` 匹配。

## 3. 面试核心 Q&A

- **接口的零值是什么?**
  - 是 `nil`。此时内部的 `_type` 和 `data` 字段都为 `nil`。
  - **陷阱**: 一个值为 `nil` 指针的接口，其自身不为 `nil`。
    ```go
    var p *int = nil
    var i interface{} = p // i != nil, 但 i 内部 data 为 nil
    ```

- **如何实现多态?**
  - 通过 `iface` 结构。它将统一的接口类型与不同的具体类型 `itab` 关联，调用时通过 `itab` 中的函数指针表实现动态派发。

- **空接口和非空接口的区别?**
  - **结构**: `eface` (空) 仅包含 `_type` 和 `data`。`iface` (非空) 额外包含一个 `itab` 指针，用于方法调用。
  - **用途**: `eface` 用于表示任意类型，`iface` 用于定义行为契约。

- **接口调用为什么有开销?**
  - **间接寻址**: 需通过 `itab` 查找方法地址，多一次内存跳转。
  - **无法内联**: 动态派发通常会阻止编译器内联优化。

- **性能优化建议?**
  - 性能关键路径避免使用接口。
  - 接口应小而专（方法数量少）。
  - 避免在循环中进行不必要的类型断言。

## 4. 一句话总结
> Go 接口通过 `iface` / `eface` 结构实现，其中 `iface` 内的 `itab` 存储了从具体类型到接口方法的映射，从而实现动态多态；而类型信息 `_type` 则用于支持类型断言。