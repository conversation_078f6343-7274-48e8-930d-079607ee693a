# 内存逃逸分析（面试速查版）

## 1. 核心定义

- **内存逃逸**: 本应分配在**栈**上的变量，被编译器分析后发现其生命周期不确定，因此被分配到**堆**上。
- **目的**: 减少GC压力，提升性能。
- **分析工具**: `go build -gcflags="-m" path/to/your/package`

---

## 2. 常见逃逸场景及优化

| 场景 | 现象 (导致逃逸) | 优化策略 |
|:---|:---|:---|
| **指针逃逸** | 方法返回局部变量的指针。`return &x` | 返回值本身，而不是指针。 |
| **接口赋值** | 将值赋给 `interface{}` 类型。 `var i interface{} = x` | 尽量使用具体类型，避免接口。 |
| **闭包引用** | 闭包函数引用了外部变量。 `func() { fmt.Println(x) }` | 通过函数参数传值。 `func(v int){...}(x)` |
| **动态扩容** | `slice` 或 `map` 容量不足，发生动态扩容。`append(s, ...)` | 预先分配足够容量。`make(..., capacity)` |
| **大对象** | 创建的对象超过一定大小 (e.g., 64KB)，直接在堆上分配。 | 避免创建大对象，或使用 `sync.Pool` 复用。 |
| **字符串拼接**| 循环中使用 `+` 拼接字符串。 `res += s` | 使用 `strings.Builder`。 |
| **goroutine参数** | 在`for`循环中启动的 goroutine 共享循环变量 `i`。 `go func() { use(i) }()` | 作为值传递给 goroutine。 `go func(v int) { use(v) }(i)` |

---

## 3. 栈 vs 堆

| 特性 | 栈 (Stack) | 堆 (Heap) |
|:---|:---|:---|
| **分配/回收** | 编译器管理，函数返回时自动回收 | GC管理，运行时回收 |
| **速度** | 非常快 | 较慢 |
| **GC压力** | 无 | 有，是GC的主要来源 |
| **建议** | **尽可能让变量分配在栈上** | **避免不必要的堆分配** |

---

## 4. 面试核心问题

1.  **什么是内存逃逸？**
    - 变量从栈分配转移到堆分配的现象。
2.  **为什么要关注内存逃逸？**
    - 堆分配较慢，且会给GC带来压力，频繁GC会影响程序性能。
3.  **如何发现内存逃逸？**
    - 编译时通过 `-gcflags="-m"` 查看逃逸分析日志。
4.  **举例说明几个逃逸场景？**
    - （从上面的表格中任选几个，例如：返回局部变量指针、闭包引用外部变量）
5.  **如何优化？**
    - （根据场景回答，例如：返回副本/值、预分配容量、使用`strings.Builder`）
