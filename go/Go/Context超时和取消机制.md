# Context超时和取消机制 (面试速查版)

## 核心概念

- **作用**: 在goroutine之间传递 **取消信号**、**超时时间** 和 **请求范围的值**。
- **核心接口**:
  ```go
  type Context interface {
      Deadline() (deadline time.Time, ok bool) // 获取截止时间
      Done() <-chan struct{}                   // 获取一个只读channel，当Context被取消时，该channel会被关闭
      Err() error                              // 返回Context被取消的原因
      Value(key interface{}) interface{}       // 获取与key关联的值
  }
  ```

## Context创建与派生

- **根Context**:
  - `context.Background()`: 顶层Context，所有派生Context的根。
  - `context.TODO()`: 不确定使用何种Context时的占位符。

- **派生Context**:
  - `context.WithCancel(parent)`: 创建一个可手动调用`cancel()`函数来取消的Context。
  - `context.WithTimeout(parent, duration)`: 创建一个在指定 **时长** 后自动取消的Context。
  - `context.WithDeadline(parent, time)`: 创建一个在指定 **时间点** 自动取消的Context。
  - `context.WithValue(parent, key, value)`: 创建一个携带键值对的Context。

## 传播机制

- **单向取消**: 父Context被取消时，其所有派生的子Context也会被 **自动取消**。
- **独立性**: 子Context被取消 **不会** 影响其父Context或兄弟Context。

## 最佳实践和常见误区

### 实践要点
1.  **首参传递**: `Context`应作为函数的第一个参数，命名为`ctx`。
2.  **显式传递**: 不要在结构体中存储`Context`，应在函数调用链中显式传递。
3.  **及时`cancel`**: 创建派生Context后，用`defer cancel()`确保相关资源被释放。
4.  **响应取消**: 在长时间运行的goroutine中，使用`select-case`监听`ctx.Done()`来及时退出。
5.  **`WithValue`慎用**: 只用于传递请求范围内的元数据（如request ID），不要用它来传递函数的可选参数。

### 常见误区
- 在结构体中存储`Context`。
- 忘记调用`cancel()`函数导致资源泄露。
- 在goroutine中不监听`ctx.Done()`，导致goroutine无法被取消。
- 滥用`WithValue`传递业务数据。

## 核心应用场景

- **HTTP服务器**: 控制请求处理的超时和取消。
- **数据库操作**: 如`database/sql`包，支持传入`Context`来控制查询超时。
- **分布式调用**: 在RPC或微服务调用中，传递`Context`以实现全链路超时和取消。
- **多goroutine协作**: 一个`cancel`信号可以优雅地关闭多个相关的goroutine。

---
### 一句话总结
> Context是Go语言并发编程的基石，它提供了一套标准的机制来管理goroutine的生命周期、传递取消信号和请求范围的数据。
