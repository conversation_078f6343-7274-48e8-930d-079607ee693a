# Defer 核心备忘

## 1. 核心原则

- **执行顺序**: **LIFO (后进先出)**。defer 像栈，后声明的先执行。
- **参数求值**: **立即求值**。defer 声明时，其参数的值就已经确定了。
- **执行时机**:
    - 函数**正常返回**前执行。
    - 函数**发生 panic** 时执行。
    - **`os.Exit()` 调用时不执行**。
- **与 `return` 关系**:
    1.  `return` 先计算返回值并存起来。
    2.  执行 `defer` 语句（可以修改**命名返回值**）。
    3.  函数带上最终的返回值退出。
- **常见用途**: 资源释放 (`file.Close()`)、锁管理 (`mu.Unlock()`)、错误恢复 (`recover`)。

## 2. 常见陷阱：循环变量

在循环中使用 `defer` 很容易出错，因为 `defer` 的函数体在循环结束后才执行，此时循环变量已经变成了最后的值。

```go
// 错误示例: 输出三次3
for i := 0; i < 3; i++ {
    // defer的闭包函数引用了外面的i
    // 循环结束时i=3, 3个defer执行时都读取这个i
    defer func() { fmt.Println(i) }()
}

// 正确解法: 通过函数传参，在defer声明时固定i的值
for i := 0; i < 3; i++ {
    defer func(val int) { fmt.Println(val) }(i)
}
// 输出: 2 1 0
```

## 3. 关键示例

### 修改命名返回值

```go
func deferReturn() (result int) { // result是命名返回值
    defer func() {
        result++ // defer可以读取并修改命名返回值
    }()
    return 5 // 1. result=5 -> 2. defer执行, result=6 -> 3. return 6
}
// deferReturn() 的结果是 6
```

### Panic 恢复

```go
func safeOperation() (err error) {
    defer func() {
        if r := recover(); r != nil {
            // 在panic后恢复，并设置错误信息
            err = fmt.Errorf("panic recovered: %v", r)
        }
    }()
    // ... 可能发生panic的操作
    panic("something went wrong")
    return nil
}
// safeOperation() 不会崩溃, 而是返回一个error
```

## 4. 底层简述

- 每个 Goroutine 都有一个 `_defer` 结构体的**链表**。
- `defer` 关键字会创建一个 `_defer` 对象并将其**头插**到链表中。
- 函数返回时，程序会从链表头开始依次执行所有 `_defer` 对象。
