### Golang CSP 并发模型核心（面试精简版）

- **核心思想**:
    - 基于 **CSP (Communicating Sequential Processes)** 理论。
    - **"不要通过共享内存来通信，而要通过通信来共享内存"** (Do not communicate by sharing memory; instead, share memory by communicating.)

- **两大基石**:
    1.  **Goroutine (执行单元)**:
        - Go 语言实现的轻量级线程，由 Go 运行时管理。
        - 开销极小（KB 级内存），可轻松创建成千上万个。
        - 使用 `go` 关键字启动。
    2.  **Channel (通信桥梁)**:
        - 类型安全的管道，是 Goroutine 之间通信和同步的主要方式。
        - **无缓冲 Channel**: 发送和接收操作是**同步阻塞**的。发送方会阻塞直到接收方准备好，反之亦然。这提供了强大的同步保证。
        - **有缓冲 Channel**: 仅在缓冲区满时发送阻塞，或在缓冲区空时接收阻塞。是一个异步的通信方式。

- **关键工具 - `select` 语句**:
    - 实现**多路复用**，可以同时监听多个 Channel 的读写操作。
    - 任何一个 `case` 准备就绪，就会执行。
    - 可以配合 `default` 子句实现**非阻塞**操作。
    - 可以配合 `time.After` 实现超时控制。

- **CSP 模型的优势**:
    - **简化并发**: 将复杂的共享内存和锁问题，转化为清晰的 Goroutine 间消息传递。
    - **提升安全性**: Channel 的所有权和同步机制，从设计上减少了数据竞争的风险。
    - **高性能与高伸缩性**: Goroutine 的轻量特性使得 Go 程序能够高效地处理海量并发连接。

- **与传统并发模型的对比**:
    - **传统模型 (如 Java/C++)**: 依赖共享内存和显式锁（`Mutex`, `Semaphore`）来同步，容易出错（死锁、数据竞争）。
    - **CSP 模型**: 依赖消息传递来同步，数据的所有权在 Goroutine 之间传递，逻辑更清晰，不易出错。