# Go Panic/Recover 快速参考

`panic`和`recover`是Go语言中处理严重错误的机制。主要用于处理不可恢复的、导致程序无法正常运行的异常情况。

### 1. `panic`

`panic`用于立即中断当前函数的执行，并开始一个向上传播的`panic`流程。

#### **触发条件**
- **显式调用**: `panic("custom error message")`
- **运行时错误**:
    - 数组/切片越界
    - 空指针解引用
    - 无效的类型断言
    - 除以零
    - 向已关闭的channel发送数据

#### **执行流程**
1.  **停止执行**: 当前函数立即停止。
2.  **执行`defer`**: 按后进先出（LIFO）的顺序执行当前函数中的`defer`语句。
3.  **向上传播**: `panic`传播到调用栈的上层函数。
4.  **重复**: 上层函数重复步骤1-3，直到当前goroutine的顶层，导致程序崩溃，或者被`recover`捕獲。

```go
// panic传播与defer执行顺序
func main() {
    defer fmt.Println("defer in main") // 3
    level1()
}
func level1() {
    defer fmt.Println("defer in level1") // 2
    level2()
}
func level2() {
    defer fmt.Println("defer in level2") // 1
    panic("panic in level2")
}
// 输出:
// defer in level2
// defer in level1
// defer in main
// panic: panic in level2
```

### 2. `recover`

`recover`是一个内置函数，用于捕获并处理`panic`，使程序从`panic`状态中恢复。

#### **使用规则**
- **必须在`defer`函数中调用**: `recover`只有在`defer`中才能生效。
- **捕获当前goroutine**: 只能捕获当前goroutine中的`panic`。
- **返回值**:
    - 如果有`panic`，返回`panic`的参数值。
    - 如果没有`panic`，返回`nil`。

```go
// 正确使用范例
func safeFunction() {
    defer func() {
        if r := recover(); r != nil {
            fmt.Printf("Recovered from panic: %v\n", r)
        }
    }()
    
    panic("something bad happened")
    fmt.Println("this won't be printed")
}
// safeFunction()调用后，程序会继续执行
```

### 3. 底层实现简介

- 每个goroutine维护一个`panic`链表（通过`_panic`结构体）。
- `panic`发生时，一个新的`_panic`实例被创建并添加到链表头部。
- `recover`从链表头取下`_panic`实例，并标记为已恢复，从而终止`panic`流程。
- `_panic`结构体包含`panic`的参数、调用栈等信息。

### 4. 最佳实践和使用场景

#### **何时使用 `panic`**
- **程序初始化失败**: 依赖的配置或服务不可用，导致程序无法启动。
- **出现不可恢复的内部错误**: 库或程序内部逻辑出现严重错误，不应该发生。
- **总结**: 用于处理那些一旦发生就无法也不应该继续运行的错误。

#### **何时使用 `recover`**
- **防止服务崩溃**: 在Web服务器的HTTP Handler中使用，防止单个请求的`panic`导致整个服务宕机。
- **库的稳定API**: 在库的公共API边界，捕获内部`panic`并作为`error`返回，对调用者隐藏实现细节。
- **测试**: 在测试代码中验证某个函数是否如预期那样`panic`。

#### **核心原则**
- **不要用`panic/recover`处理普通错误**: 这是`error`的职责。`panic/recover`类似于其他语言的`try/catch`，但Go文化不鼓励用它来处理可预期的错误。
- **`defer`中做好资源清理**: 即使`panic`发生，`defer`也会执行，适合放置解锁、关闭文件等操作。

### 5. 常见陷阱和注意事项

1.  **`recover`必须在`defer`中直接调用**:
    ```go
    // 错误用法: recover在defer调用的另一个函数中，无效
    defer func() {
        someOtherFunc() // 如果recover在someOtherFunc里，无法捕获
    }()

    // 错误用法: 直接调用recover，无效
    if r := recover(); r != nil { /* ... */ }
    ```

2.  **`recover`无法跨goroutine捕获`panic`**:
    - 一个goroutine中的`panic`只能被该goroutine自身的`defer recover`捕获。
    - `main` goroutine无法捕获其他goroutine的`panic`。

3.  **在`defer`中重新`panic`**:
    - `defer`中可以再次调用`panic`。
    - 如果`recover`捕获了`panic`后又重新`panic`，这个新的`panic`会替换旧的，并继续向上传播。

### 6. 性能影响

- `panic/recover`的性能开销远大于返回`error`。它需要展开调用栈，执行所有`defer`，开销较大。
- **避免在性能敏感的热点代码路径中使用**。

### 7. 面试核心问题

- **Q: `panic`、`defer`、`recover`的执行顺序是怎样的？**
  A: `panic`触发 -> `defer`按LIFO顺序执行 -> `defer`中的`recover`捕获`panic`。

- **Q: 为什么`recover`必须在`defer`中才能工作？**
  A: `panic`会立即停止当前函数的正常执行流程，只有`defer`的函数会保证被执行。因此，`recover`必须放在`defer`中，才能在`panic`发生时被调用。

- **Q: `panic`之后，`return`语句还会执行吗？**
  A: 不会。`panic`会中断正常的执行流程。

- **Q: 一个`panic`可以被多个`recover`捕获吗？**
  A: 不可以。`panic`一旦被`recover`捕获，`panic`流程就终止了，不会再向上传播。

- **Q: 在`defer`中再次`panic`会发生什么？**
  A: 新的`panic`会替换掉正在处理的旧`panic`。如果调用栈上层还有`defer recover`，它将捕获这个新的`panic`。
