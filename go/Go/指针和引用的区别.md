# 指针和引用的区别

## 基本概念

### 指针
- 指针是一个变量，存储另一个变量的内存地址
- 可以通过指针间接访问或修改其指向的变量
- 支持指针算术运算，可以改变指向的地址
- Go语言中使用`*`声明指针类型，`&`获取地址

### 引用
- 引用是变量的别名，是已存在变量的另一个名字
- 一旦初始化就不能改变指向
- C++中使用`&`声明引用，Java中对象变量本质上都是引用
- Go语言没有传统意义的引用，但有类似概念

## Go语言中的指针

### 基本使用
```go
func pointerExample() {
    var x int = 42
    var p *int = &x  // p是指向x的指针

    fmt.Println(x)   // 输出: 42
    fmt.Println(p)   // 输出: x的内存地址
    fmt.Println(*p)  // 输出: 42 (解引用)

    *p = 100         // 通过指针修改x的值
    fmt.Println(x)   // 输出: 100
}
```

### 指针的零值
```go
var p *int
fmt.Println(p == nil)  // 输出: true
```

## 函数参数传递

### 值传递 vs 指针传递
```go
// 值传递 - 不会修改原变量
func modifyValue(x int) {
    x = 100
}

// 指针传递 - 会修改原变量
func modifyPointer(x *int) {
    *x = 100
}

func main() {
    a := 42
    modifyValue(a)
    fmt.Println(a)    // 输出: 42 (未改变)

    modifyPointer(&a)
    fmt.Println(a)    // 输出: 100 (已改变)
}
```

### 结构体指针
```go
type Person struct {
    Name string
    Age  int
}

func updatePerson(p *Person) {
    p.Name = "Updated"  // Go自动解引用
    p.Age = 30
}

func main() {
    person := Person{Name: "Alice", Age: 25}
    updatePerson(&person)
    fmt.Println(person)  // 输出: {Updated 30}
}
```

## 切片、映射、通道的特殊性

### 引用类型
```go
func modifySlice(s []int) {
    s[0] = 100  // 会修改原切片
}

func modifyMap(m map[string]int) {
    m["key"] = 100  // 会修改原映射
}

func main() {
    slice := []int{1, 2, 3}
    modifySlice(slice)
    fmt.Println(slice)  // 输出: [100 2 3]

    m := map[string]int{"key": 1}
    modifyMap(m)
    fmt.Println(m)  // 输出: map[key:100]
}
```

## 内存安全

### 避免悬空指针
```go
func dangerousPointer() *int {
    x := 42
    return &x  // 危险：返回局部变量地址
}

func safePointer() *int {
    x := new(int)  // 在堆上分配
    *x = 42
    return x  // 安全：堆上的内存
}
```

### 指针比较
```go
func pointerComparison() {
    a := 42
    b := 42

    p1 := &a
    p2 := &b
    p3 := &a

    fmt.Println(p1 == p2)  // false (不同地址)
    fmt.Println(p1 == p3)  // true (相同地址)
    fmt.Println(*p1 == *p2) // true (相同值)
}
```

## 面试要点

1. **基本概念**: 指针存储地址，引用是别名
2. **Go语言特点**: 没有传统引用，但有类似概念
3. **内存安全**: 避免悬空指针，合理使用new和make
4. **性能考虑**: 大结构体使用指针传递避免拷贝
5. **引用类型**: 切片、映射、通道本身就是引用语义