# Slice 核心速查

> **一句话总结**: Slice是Go的动态数组，底层是 `指针(ptr)` + `长度(len)` + `容量(cap)` 的结构体，支持自动扩容和零拷贝切片。

---

## 1. 核心结构 vs 数组

| 特性     | Slice                                  | Array        |
| -------- | -------------------------------------- | ------------ |
| **类型** | 引用类型                               | 值类型       |
| **大小** | 动态可变                               | 固定         |
| **传递** | 传递引用（指针、长度、容量），开销小   | 复制整个数组 |
| **内部** | `struct{ ptr *array, len int, cap int }` | 连续内存块   |

---

## 2. 扩容机制 (append)

当 `append` 导致容量不足时，会触发扩容，规则如下：

1.  **计算新容量**:
    - 如果期望容量 > 2倍旧容量，新容量 = 期望容量。
    - 旧容量 < 1024: 新容量 = **2 \* 旧容量**
    - 旧容量 >= 1024: 新容量 = **1.25 \* 旧容量**
2.  **内存操作**:
    - 分配新的、更大的底层数组。
    - 将旧数据**复制**到新数组。
    - `slice` 的指针指向新数组。旧数组等待GC。

**性能提示**: 使用 `make([]T, 0, cap)` 预分配容量可显著减少扩容次数。

---

## 3. 常见陷阱与最佳实践

### 陷阱1: 共享底层数组

多个 `slice` 若源于同个 `array` 或 `slice`，则共享数据。修改一个会影响其他。

```go
arr := []int{1, 2, 3, 4, 5}
s1 := arr[1:3] // [2, 3]
s2 := arr[2:4] // [3, 4]

s1[1] = 99 // 修改 s1[1] (即 arr[2])
// arr -> [1, 2, 99, 4, 5]
// s1  -> [2, 99]
// s2  -> [99, 4] // s2 也被改变
```

### 陷阱2: 内存泄漏

从一个大 `slice` 创建的小 `slice` 会导致整个底层大数组无法被GC回收。

```go
// 反模式：smallSlice 引用了整个 1MB 的数组
bigSlice := make([]byte, 1024*1024) 
smallSlice := bigSlice[:10]

// 正确做法：只复制需要的数据，让大数组可以被回收
safeSlice := make([]byte, 10)
copy(safeSlice, bigSlice[:10])
```
**简便写法**: `safeSlice := append([]byte{}, bigSlice[:10]...)`

### 技巧: 三索引切片控制容量

使用 `s[low:high:max]` 可以限制新 `slice` 的容量，防止 `append` 意外修改原底层数组。
- `len` = `high` - `low`
- `cap` = `max` - `low`

```go
arr := []int{1, 2, 3, 4, 5}
s1 := arr[1:3:3] // [2, 3], len=2, cap=2 (cap = 3-1)

s1 = append(s1, 99) // 安全, append会触发扩容, 不会修改 arr
// s1 -> [2, 3, 99] (新底层数组)
// arr -> [1, 2, 3, 4, 5] (未受影响)
```

