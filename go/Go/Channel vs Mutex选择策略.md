# Channel vs Mutex选择策略

## 核心原则

### Go设计哲学
> "Don't communicate by sharing memory; share memory by communicating"
> 不要通过共享内存来通信，而要通过通信来共享内存

## 选择决策树

### 使用Channel的场景
1. **数据传递** - 需要在goroutine间传递数据
2. **流水线处理** - 数据需要经过多个处理阶段
3. **事件通知** - 需要通知其他goroutine某个事件发生
4. **协调控制** - 需要协调多个goroutine的执行顺序

### 使用Mutex的场景
1. **保护共享状态** - 多个goroutine需要访问同一个变量
2. **简单同步** - 只需要保证操作的原子性
3. **性能要求高** - 临界区很小，锁竞争不激烈
4. **缓存/计数器** - 简单的读写操作

## 具体对比

### 1. 计数器场景
```go
// Mutex方式 - 推荐
type Counter struct {
    mu    sync.Mutex
    value int
}

func (c *Counter) Increment() {
    c.mu.Lock()
    c.value++
    c.mu.Unlock()
}

// Channel方式 - 过度设计
type ChannelCounter struct {
    ch chan int
}

func (c *ChannelCounter) Increment() {
    c.ch <- 1  // 需要额外的goroutine处理
}
```

### 2. 数据传递场景
```go
// Channel方式 - 推荐
func producer(ch chan<- int) {
    for i := 0; i < 10; i++ {
        ch <- i
    }
    close(ch)
}

func consumer(ch <-chan int) {
    for data := range ch {
        process(data)
    }
}

// Mutex方式 - 复杂且容易出错
type SharedData struct {
    mu   sync.Mutex
    data []int
    done bool
}
```

### 3. 配置更新场景
```go
// Mutex方式 - 推荐
type Config struct {
    mu   sync.RWMutex
    data map[string]string
}

func (c *Config) Get(key string) string {
    c.mu.RLock()
    defer c.mu.RUnlock()
    return c.data[key]
}

func (c *Config) Set(key, value string) {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.data[key] = value
}
```

### 4. 工作池场景
```go
// Channel方式 - 推荐
type WorkerPool struct {
    jobs    chan Job
    results chan Result
}

func (wp *WorkerPool) worker() {
    for job := range wp.jobs {
        result := job.Process()
        wp.results <- result
    }
}
```

## 性能对比

### 基准测试结果
- **Mutex**: 适合高频简单操作，性能最好
- **Channel**: 适合数据传递，有额外开销
- **原子操作**: 适合简单数值操作，性能介于两者之间

### 内存使用
- **Mutex**: 内存占用最小
- **Channel**: 需要额外的缓冲区和调度开销

## 常见误区

### 1. 过度使用Channel
```go
// 错误：用Channel做简单的标志位
type Service struct {
    stopCh chan bool
}

// 正确：用原子操作或简单的bool+mutex
type Service struct {
    stopped int32  // 使用atomic操作
}
```

### 2. 过度使用Mutex
```go
// 错误：用Mutex做复杂的数据流
type Pipeline struct {
    mu   sync.Mutex
    data []interface{}
}

// 正确：用Channel构建流水线
func pipeline(input <-chan Data) <-chan Result {
    output := make(chan Result)
    go func() {
        defer close(output)
        for data := range input {
            output <- process(data)
        }
    }()
    return output
}
```

## 面试要点

### 核心问题
1. **什么时候用Channel？什么时候用Mutex？**
   - Channel：数据传递、流水线、事件通知
   - Mutex：保护共享状态、简单同步

2. **性能差异？**
   - Mutex：更快，适合高频操作
   - Channel：有调度开销，但提供更强的语义

3. **设计原则？**
   - 优先考虑Channel，除非性能要求极高
   - 简单的状态保护用Mutex
   - 复杂的协调用Channel

### 选择流程图
```
需要传递数据？
├─ 是 → 使用Channel
└─ 否 → 只是保护共享状态？
    ├─ 是 → 使用Mutex
    └─ 否 → 需要复杂协调？
        ├─ 是 → 使用Channel
        └─ 否 → 使用Mutex
```

### 一句话总结
> Channel用于通信和协调，Mutex用于保护共享状态，选择取决于具体的使用场景而非性能
