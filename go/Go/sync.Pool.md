# sync.Pool 快速参考

> **核心思想**: 通过复用临时对象，减少内存分配和GC压力。

---

### 工作原理
- **两级缓存**:
    - **本地池 (local pool)**: 每个处理器(P)一个，无锁访问，并发高效。
    - **全局池 (victim cache)**: 本地池间的备用，需要加锁。
- **GC 清理**: `sync.Pool` 中的对象会在两次GC之间被回收，不适合做持久化缓存。

---

### 核心API
- `New: func() interface{}`: 当池为空时，调用此函数创建新对象。
- `Get() interface{}`: 从池中获取对象。优先从本地池，再尝试全局池，最后调用`New`。
- `Put(x interface{})`: 将对象放回池中。

---

### 关键注意事项
- **必须重置状态**: `Get`到的对象可能包含旧数据，必须手动重置（如`buf.Reset()`）。
- **临时性**: GC会清空池，不能用于存储需要长期保留的对象。
- **内存泄漏风险**: 放入池中的对象不应持有未清理的外部资源引用。

---

### 适用场景
- **适合**:
    - 频繁创建和销毁的昂贵对象（如`[]byte` buffer）。
    - 生命周期短、状态易重置的对象。
- **不适合**:
    - TCP连接等长连接对象（因为GC会清理）。
    - 简单、创建成本低的对象。