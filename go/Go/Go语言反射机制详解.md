# Go语言反射核心要点（面试速查）

Go语言的反射（reflection）是在**运行时**检查、操作变量的**类型和值**的机制。它功能强大但性能开销较大，需谨慎使用。

### 1. 核心类型：`Type` 和 `Value`

反射功能主要由`reflect`包的`Type`和`Value`两个类型提供。

- **`reflect.TypeOf(i interface{})`**: 获取接口值的动态类型，返回`reflect.Type`。
- **`reflect.ValueOf(i interface{})`**: 获取接口值的动态值，返回`reflect.Value`。

```go
import (
    "fmt"
    "reflect"
)

func basicReflection() {
    var x float64 = 3.4
    t := reflect.TypeOf(x)  // t 是 float64 的 reflect.Type
    v := reflect.ValueOf(x) // v 是 3.4 的 reflect.Value
    
    fmt.Println("Type:", t)         // Type: float64
    fmt.Println("Value:", v)        // Value: 3.4
    fmt.Println("Kind:", v.Kind())  // Kind: float64, Kind是更基本的类型分类
}
```

### 2. 反射三定律

#### **定律1：接口值 -> 反射对象**
任何`interface{}`变量都可以通过`reflect.TypeOf()`和`reflect.ValueOf()`获取反射对象。

#### **定律2：反射对象 -> 接口值**
`reflect.Value`可以通过调用`.Interface()`方法，恢复为`interface{}`值。

```go
func law2() {
    v := reflect.ValueOf(3.4)
    y := v.Interface().(float64) // y is 3.4
    fmt.Println("Recovered value:", y)
}
```

#### **定律3：要修改反射对象，其值必须可设置（Settable）**
要修改一个`reflect.Value`，它必须是**可设置的**。可设置性意味着它代表的是一个具体的内存地址，而非一个值的拷贝。
要使`reflect.Value`可设置，必须传入一个指向变量的**指针**，然后调用`.Elem()`方法获取指针指向的值。

```go
func law3() {
    var x float64 = 3.4
    v := reflect.ValueOf(&x) // 传入指针，v 本身不可设置
    
    fmt.Println("v.CanSet():", v.CanSet()) // false
    
    // 调用 .Elem() 获取指针指向的元素，这个元素是可设置的
    elem := v.Elem()
    fmt.Println("elem.CanSet():", elem.CanSet()) // true
    
    elem.SetFloat(7.1) // 修改值
    fmt.Println("Modified x:", x) // x 的值变为 7.1
}
```

### 3. 常用操作

#### **操作结构体**
遍历字段、获取Tag、修改字段值是常见操作。

```go
type Person struct {
    Name string `json:"name" validate:"required"`
    Age  int    `json:"age"`
}

func structOps() {
    p := Person{Name: "Alice", Age: 30}
    t := reflect.TypeOf(p)
    
    // 遍历字段和Tag
    for i := 0; i < t.NumField(); i++ {
        field := t.Field(i)
        jsonTag := field.Tag.Get("json")
        fmt.Printf("Field: %s, JSON Tag: %s\n", field.Name, jsonTag)
    }

    // 修改字段值（必须传入指针）
    vPtr := reflect.ValueOf(&p)
    vElem := vPtr.Elem()
    vElem.FieldByName("Name").SetString("Bob")
    vElem.FieldByName("Age").SetInt(31)
    
    fmt.Printf("Modified: %+v\n", p) // {Name:Bob Age:31}
}
```

#### **动态调用方法**
可以根据方法名动态调用。

```go
type Calculator struct{}
func (c Calculator) Add(a, b int) int { return a + b }

func methodCall() {
    calc := Calculator{}
    v := reflect.ValueOf(calc)
    method := v.MethodByName("Add")
    
    args := []reflect.Value{reflect.ValueOf(10), reflect.ValueOf(20)}
    result := method.Call(args)
    
    fmt.Printf("Add(10, 20) = %v\n", result[0].Int()) // 30
}
```

### 4. 优缺点与风险

#### **优点（应用场景）**
- **序列化/反序列化**: `encoding/json`、`xml`等。
- **ORM框架**: 如`GORM`，实现数据库记录到结构体的映射。
- **依赖注入 (DI)**: 框架自动注入依赖。
- **编写通用代码**: 处理未知类型的函数，如通用的验证器、格式化工具。

#### **缺点与风险**
- **性能开销大**: 比直接调用慢10-100倍，涉及大量运行时类型检查，且无法被编译器优化。
- **类型不安全**: 编译时无法发现类型错误，错误会推迟到运行时以`panic`形式出现。
- **代码可读性差**: 反射代码逻辑复杂，不易理解和维护。

### 5. 面试注意事项

- **性能**: 明确知道反射很慢，不应在性能敏感路径上使用。可缓存`reflect.Type`和`reflect.Method`的结果来优化。
- **可设置性**: 一定要区分值传递和指针传递。只有通过指针获取的`Elem`才是可修改的。
- **零值**: `reflect.Value`的零值是无效的（`IsValid()`为`false`），在其上调用任何方法都会`panic`。
- **Kind vs. Type**: `Type`是具体类型（如`main.Person`），`Kind`是类型的种类（如`struct`）。在写通用逻辑时，通常检查`Kind`。
- **反射不是银弹**: 在Go中，应首选静态类型。只有在必要时（如编写通用框架）才使用反射。
