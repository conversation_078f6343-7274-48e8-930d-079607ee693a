# WaitGroup 核心面试要点

### 1. 核心用法
- **`Add(delta int)`**: 增加计数器。
- **`Done()`**: 减少计数器 (等价于 `Add(-1)`)。
- **`Wait()`**: 阻塞，直到计数器归零。
- **关键实践**: 在启动 goroutine **前**调用 `Add`，在 goroutine 中使用 `defer wg.Done()` 确保调用。

### 2. 基本代码模型
```go
var wg sync.WaitGroup
for i := 0; i < 5; i++ {
    wg.Add(1)  // 启动前增加计数
    go func(id int) {
        defer wg.Done()  // 任务完成时减少计数
        // ... do work ...
    }(i)
}
wg.Wait()  // 等待所有goroutine完成
```

### 3. 常见陷阱及避坑
- **计数器不匹配**: `Add` 和 `Done` 调用次数必须相等，否则会导致死锁（`Done` 调用次数少）或 panic（`Done` 调用次数多）。
- **在goroutine内调用`Add`**: `wg.Wait()` 可能在 `wg.Add(1)` 执行前就已返回，导致主程序提前退出。
- **重复使用`WaitGroup`**: `Wait` 返回后若要复用，必须重新创建 `WaitGroup` 实例。

### 4. 进阶模式
- **超时等待**: 结合 channel 和 `select` 实现。
  ```go
  done := make(chan struct{})
  go func() {
      wg.Wait()
      close(done)
  }()

  select {
  case <-done:
      // 正常完成
  case <-time.After(timeout):
      // 超时
  }
  ```
- **错误收集**: 结合 error channel 从多个 goroutine 中安全地收集错误。

### 5. 与其他并发原语对比
- **vs. Channel**:
    - `WaitGroup`: 用于等待一组任务完成，不关心结果。
    - `Channel`: 用于在 goroutine 之间传递数据或同步。
- **vs. Context**:
    - `WaitGroup`: 用于等待任务组**完成**。
    - `Context`: 用于通知任务组**取消**或**超时**。
    - **组合使用**: `Context` 用于控制生命周期，`WaitGroup` 用于确保所有 goroutine 都已完全退出。

### 一句话总结
> `WaitGroup` 是 Go 中用于**等待一组并发任务全部完成**的同步原语，其核心是**计数器**机制。
