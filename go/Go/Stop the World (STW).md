### **Stop-the-World (STW) 核心概念**

**定义**: 在垃圾回收 (GC) 期间，暂停所有 Goroutine，以确保内存回收的一致性和正确性。

**为何必要 (Why)**:
- **防止数据竞争**: 避免 GC 过程中因内存修改导致的引用错误。
- **保证一致性**: 确保 GC 在一个稳定的内存快照上操作。

**发生阶段 (When)**:
- Go 的 GC 在**标记准备（Mark Setup）**和**标记终止（Mark Termination）**阶段会发生 STW。清理阶段是并发的。

**主要影响 (Impact)**:
- **应用暂停**: 导致应用短暂停止，影响实时性和吞吐量。
- **用户体验**: 在高并发或大堆内存场景下，可能导致延迟增加。

**Go 的优化策略 (Optimizations)**:
- **三色标记法**: 采用并发标记算法，GC 的大部分标记工作与业务逻辑并发执行，极大缩短 STW 时间。
- **混合写屏障 (Hybrid Write Barrier)**: 配合三色标记法，保证并发标记的正确性。
- **增量回收**: 将 GC 分为多个步骤，穿插在程序运行中。
- **`GOGC` 调优**: 通过环境变量控制 GC 触发频率。

**监控与调试 (Monitoring)**:
- **GC 日志**: `GODEBUG=gctrace=1`
- **性能分析**: `pprof`
- **运行时统计**: `runtime.ReadMemStats`