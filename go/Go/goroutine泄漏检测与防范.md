# Goroutine 泄漏快速参考

## 1. 什么是 Goroutine 泄漏？
Goroutine 创建后无法正常退出，持续占用内存和 CPU 调度资源，最终可能导致程序性能下降或崩溃。**核心原则：任何启动的 goroutine 都必须有明确的退出路径。**

## 2. 常见泄漏场景
- **Channel 阻塞**：
  - **发送阻塞**：向无缓冲或已满的 Channel 发送数据，但没有接收者。
  - **接收阻塞**：从 Channel 读取数据，但没有发送者，也无 `close`信号。
- **死循环**：Goroutine 内的循环没有退出条件或永远无法满足退出条件。
- **`sync.WaitGroup` 误用**：
  - `Add` 的数量多于 `Done` 的数量，导致 `Wait` 永久阻塞。
  - Goroutine 在调用 `Done` 之前就 `panic` 了。
- **资源等待无超时**：Goroutine 阻塞等待网络、数据库或其他外部资源，但没有设置超时机制。
- **Context 未正确传递或处理**：父 Goroutine 的 `Context` 被取消，但子 Goroutine 未监听 `ctx.Done()` 事件以退出。

## 3. 如何检测 Goroutine 泄漏？
- **`pprof` 工具 (最常用)**
  - **开启服务**: `import _ "net/http/pprof"` 并监听端口 `http.ListenAndServe("localhost:6060", nil)`。
  - **Web 查看**: 访问 `http://localhost:6060/debug/pprof/goroutine?debug=1` 可直接查看所有 goroutine 的堆栈信息。
  - **命令行分析**: `go tool pprof http://localhost:6060/debug/pprof/goroutine`。
- **`goleak` 库**
  - 在单元测试中自动检测泄漏。
  - 使用非常简单：`defer goleak.VerifyNone(t)`。
- **手动检查**
  - 在代码关键点前后打印 `runtime.NumGoroutine()` 的数量，对比是否存在非预期的增长。
- **监控告警**
  - 使用 Prometheus 等监控系统，持续追踪 `go_goroutines` 指标，设置阈值进行告警。

## 4. 如何防范 Goroutine 泄漏？ (最佳实践)
- **核心：使用 `context` 控制生命周期**
  - 将 `context.Context` 作为函数调用的第一个参数。
  - 在 Goroutine 中使用 `select` 语句监听 `ctx.Done()` channel，一旦收到信号立即返回。
  - 在主流程结束或不再需要时，务必调用 `cancel()` 函数。
- **设置合理超时**
  - 对所有外部调用（HTTP、RPC、数据库）和可能阻塞的操作，使用 `context.WithTimeout` 或 `context.WithDeadline`。
- **正确使用 Channel**
  - **优先使用带缓冲的 Channel**：可以缓解临时的生产-消费速度不匹配问题。
  - **使用 `select` + `default`**：避免在 channel 操作上无限期阻塞。
  - **优雅关闭**：通过 `close` 一个专用的 `done` channel 来通知多个 goroutine 停止，而不是关闭数据 channel。
- **善用 `defer` 确保清理**
  - `defer cancel()`
  - `defer wg.Done()`
  - `defer resource.Release()`
- **处理 `panic`**
  - 在 goroutine 的顶层使用 `recover`，防止单个 goroutine 崩溃导致 `wg.Done()` 等清理逻辑未被执行。
