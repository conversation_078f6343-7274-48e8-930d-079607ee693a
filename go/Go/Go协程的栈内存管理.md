### Go 协程栈内存管理核心要点

Go 协程的栈管理是其高并发能力的关键，核心特点是**动态调整**和**高效内存利用**。

**核心机制：**

*   **初始栈小 (2KB):**
    *   相比线程默认栈 (通常 >1MB)，极大降低了创建 Goroutine 的内存成本。
    *   使得创建成千上万的 Goroutine 成为可能。

*   **动态增长:**
    *   **时机:** 函数调用时，若当前栈空间不足，则触发增长。
    *   **过程:** 分配一块更大的新栈，将旧栈内容拷贝过去，然后释放旧栈。
    *   **优点:** 按需分配，避免浪费，对开发者透明。

*   **动态收缩:**
    *   **时机:** Goroutine 执行完成后，如果栈使用率降低，GC 会在特定时机进行收缩。
    *   **目的:** 释放长时间运行但峰值已过的 Goroutine 占用的多余内存。

*   **栈溢出保护:**
    *   每个 Goroutine 栈末尾都有一个**保护区 (Guard Page)**。
    *   访问该区域会触发运行时恐慌 (panic)，防止非法内存访问，比传统栈溢出更安全。

**总结优势:**

*   **高并发:** 低内存占用，轻松支持海量 Goroutine。
*   **高效率:** 动态伸缩，内存使用率高。
*   **简单安全:** 开发者无需关心栈大小，运行时自动管理，并提供溢出保护。