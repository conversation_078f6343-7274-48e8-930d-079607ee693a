### `go func()` Goroutine 创建与调度流程

**1. Goroutine 创建与入队**
- **创建**: `go func()` 语句创建一个新的 Goroutine (G) 对象。
- **入队**:
    - 新创建的 G 会优先被放入当前逻辑处理器 (P) 的 **本地运行队列** (Local Run Queue)。
    - 如果 P 的本地队列已满，G 会被放入 **全局运行队列** (Global Run Queue)。

**2. Goroutine 调度与执行**
- **调度单元**: 调度器以 M (操作系统线程) 为单位执行 G，每个 M 必须绑定一个 P。
- **执行顺序**: M 会按照以下顺序寻找可执行的 G：
    1.  从其绑定的 P 的 **本地队列** 中弹出一个 G。
    2.  如果本地队列为空，尝试从 **全局队列** 中获取一批 G。
    3.  如果全局队列也为空，会尝试从其他 P 的本地队列中 **"窃取"** (Work Stealing) 一半的 G 来执行。
- **持续执行**: M 会在一个循环中不断地寻找并执行 G。

**3. 阻塞处理 (以系统调用为例)**
- **阻塞与解绑**: 当 G 执行阻塞性系统调用时，其所在的 M 也会随之阻塞。此时，Go `runtime` 会将这个 M 与其绑定的 P **解绑**。
- **调度接管**: `runtime` 会寻找一个空闲的 M，或者创建一个新的 M，来接管这个 P，继续执行 P 队列中剩余的 G，从而避免整个线程被阻塞。
- **调用后恢复**: 当系统调用结束后，原来的 G 会被放回运行队列（通常是全局队列），等待下一次被调度。阻塞的 M 会被释放或重新调度。

这个过程展示了 Go 调度器如何通过 G-P-M 模型、本地/全局队列和工作窃取机制，实现高效的并发调度和资源利用。