在 Golang 的运行时系统中，`gopark` 和 `goready` 是两个关键的函数，它们在 Goroutine 的调度和状态管理中起着重要作用。这两个函数在 Go 的调度模型中用于管理 Goroutine 的挂起（park）和恢复（ready）操作。

### `gopark` 和 `goready` 核心概念

**`gopark`：挂起 Goroutine**
*   **核心作用**: 将当前 Goroutine 从运行状态切换到**等待状态** (`_Gwaiting`)，并让出 CPU 执行权。
*   **触发场景**: 当 Goroutine 因特定条件需要阻塞时，调度器会调用 `gopark`。常见场景包括：
    *   Channel 读/写操作阻塞。
    *   等待互斥锁 (`sync.Mutex`)。
    *   网络 I/O 等待。
    *   `time.Sleep`。
*   **执行流程**:
    1.  设置 Goroutine 状态为 `_Gwaiting`。
    2.  将 Goroutine 与当前的工作线程（M）分离。
    3.  调用调度器 (`schedule`)，选择另一个可运行的 Goroutine 来执行。

**`goready`：唤醒 Goroutine**
*   **核心作用**: 将处于**等待状态**的 Goroutine 重新标记为**可运行状态** (`_Grunnable`)，并放入运行队列中等待调度。
*   **触发场景**: 当 Goroutine 等待的条件满足时，`goready` 会被调用来唤醒它。例如：
    *   Channel 的另一端准备好了数据或接收空间。
    *   锁被释放。
    *   网络 I/O 操作完成。
*   **执行流程**:
    1.  将被唤醒的 Goroutine 状态设置为 `_Grunnable`。
    2.  将其放入处理器（P）的本地运行队列。如果队列已满，则放入全局运行队列。
    3.  等待调度器后续将其调度到某个工作线程（M）上执行。

### **总结**

| 函数 | 作用 | 状态流转 | 目的 |
| :--- | :--- | :--- | :--- |
| `gopark` | 挂起当前G | `_Grunning` -> `_Gwaiting` | 让出CPU，等待事件 |
| `goready`| 唤醒目标G | `_Gwaiting` -> `_Grunnable` | 条件满足，准备再运行 |

这两个函数是 Go 调度模型（G-P-M）协作的核心，它们实现了非阻塞式的 Goroutine 调度，避免了内核级线程切换的高昂开销，是 Go 高并发性能的关键。