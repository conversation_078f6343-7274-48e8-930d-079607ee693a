<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>陈啸天 - 简历</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        :root {
            --primary: #444;
            --link: #007bff;
            --text-main: #444;
            --text-secondary: #666;
        }
        body, h1, h2, h3, p, ul {
            margin: 0;
            padding: 0;
            font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-weight: normal;
        }
        body {
            color: var(--text-main);
            font-size: 15px; /* Base font size increased for readability */
            line-height: 1.5; /* 增加行高，提升可读性 */
        }
        .resume {
            width: 800px;
            margin: 30px auto;
            padding: 15px;
        }

        /* Header */
        .header {
            text-align: center;
        }
        .header h1 {
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 2px; /* 增加字间距 */
        }
        .header .contact {
            display: flex; /* 使用flex布局 */
            justify-content: center;
            align-items: center;
            gap: 10px; /* 控制间距 */
            font-size: 14px;
            color: var(--text-secondary);
        }
        .header .contact span:not(:last-child)::after {
            content: '|'; /* 用伪元素添加分隔符 */
            margin-left: 10px;
            color: #ddd;
        }
        .header .subtitle {
            display: flex;
            justify-content: center; /* 居中 */
            align-items: center;
            gap: 30px; /* 使用gap控制间距 */
            color: var(--text-secondary);
        }

        /* Section */
        .section {
            margin-bottom: 10px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600; /* 使用字重代替bold */
            margin-bottom: 5px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0; /* 分割线 */
            display: flex;
            align-items: center;
        }
        
        ul {
            list-style-type: '✧ ';
        }

        /* Education */
        .education-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .education-entry .date {
            font-weight: 600;
            flex-shrink: 0; /* 防止日期换行 */
        }
        .education-entry .details {
            display: flex;
            justify-content: space-between;
            flex-grow: 1;
            margin-left: 50px; /* 保持一个清晰的距离 */
        }
        .education-entry .school { font-weight: 600; }
        .education-entry .major { font-weight: normal; }
        .education-entry .degree { font-weight: normal; }

        /* Skills */
        .skill-category {
            margin-bottom: 10px;
        }
        .skill-category p {
            font-weight: 600;
            margin-bottom: 8px;
        }
        .skill-category ul {
            margin-left: 20px;
        }

        /* Experience / Projects */
        .entry {
            margin-bottom: 15px;
        }
        .entry-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 8px;
        }
        .entry-header .date {
            font-weight: 600;
            color: var(--text-main);
        }
        .entry-header > div {
             display: flex;
             align-items: baseline;
             gap: 0.8em;
        }
        .entry-header .role {
            color: var(--text-secondary);
            font-weight: 600;
            /* margin-left: 1em; */ /* Removed for gap */
        }
        .entry-details {
            margin-top: 5px;
            padding-left: 5px; /* 整体内缩进，与标题对齐 */
        }
        .entry-details p {
            margin-bottom: 5px;
        }
        .entry-details ul {
            padding-left: 20px;
        }

        a {
            color: var(--link);
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .resume strong {
            color: var(--primary);
        }

    </style>
</head>
<body>
    <div class="resume">
        <header class="header">
            <h1>陈啸天</h1>
            <div class="contact">
                <span><i class="fas fa-phone"></i> <a href="tel:18015528893">18015528893</a></span>
                <span><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></span>
            </div>
            <div class="subtitle">
                <span>北京</span>
                <span>AIGC 应用</span>
                <span>在职</span>
            </div>
        </header>

        <section class="section">
            <h2 class="section-title">个人简介</h2>
            <p>专注文本类 AIGC 应用，具备从 0 到 1 构建 AI 长/短篇网文生产体系的经验。带领团队实现规模化生产。个人也有 AI 网文创作和 AI 自媒体号的经验，期望在 AIGC 方向继续深耕，推动内容创作与 AI 技术的深度融合。</p>
        </section>

        <section class="section">
            <h2 class="section-title">核心技能</h2>
            <div class="skill-category">
                <p>AIGC 网文</p>
                <ul>
                    <li>成功落地 AI 长/短篇网文规模化产线，月产能<strong> 200 </strong>本，成本<strong>降低至 5%</strong>，代表作品番茄小说在读量<strong> 50 </strong>万。</li>
                    <li>具备从 0 到 1 构建 AI 内容生产体系的能力，擅长攻克 AI 长篇创作核心技术难题。</li>
                </ul>
            </div>
            <div class="skill-category">
                <p>内容全链路技术经验</p>
                <ul>
                    <li>熟悉内容创作、内容处理和内容分发全链路，了解内容管线、内容池和推荐系统。</li>
                    <li>熟悉大型爬虫系统架构与逆向风控技术，具备日均处理资讯与视频内容 <strong>5000 </strong>万+的规模化内容获取能力。</li>
                </ul>
            </div>
            <div class="skill-category">
                <p>团队管理与业务创新</p>
                <ul>
                    <li>具备跨学科的sense、知识和实践的积累，擅长整合内容、数据、AI 等不同领域的能力，产出业务方案。</li>
                    <li><strong>10+</strong> 人跨 AI/内容/数据领域团队管理经验。</li>
                </ul>
            </div>
            <div class="skill-category">
                <p>技术栈</p>
                <ul>
                    <li>编程语言：Python、Golang</li>
                    <li>架构能力：微服务化、API 网关、可观测体系</li>
                    <li>AI 技术：prompt、workflow、mcp server</li>
                    <li>数据技术：爬虫系统、内容供需分析、内容理解和特征</li>
                </ul>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">教育背景</h2>
            <div class="education-entry">
                <span class="date">2014.9 - 2018.6</span>
                <div class="details">
                    <span class="school">大连理工大学</span>
                    <span class="major">工商管理</span>
                    <span class="degree">本科</span>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">工作经历</h2>
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2023.6 - 至今</span>
                    <div>
                        <span class="company">喜马拉雅 · 原创自营内容部</span>
                        <span class="role">负责人</span>
                    </div>
                </div>
                <div class="entry-details">
                    <p><strong>核心职责:</strong> 负责 AI 原创网文业务，通过 AI 创新解决版权引入成本高、真人原创效率低的业务痛点。</p>
                    <p><strong>主要工作成果:</strong></p>
                    <ul>
                        <li><strong>业务规划:</strong> 基于对公司版权痛点的分析，提出 AI 原创网文规模化自生产方案，构建自有版权的 AI 内容供给体系。</li>
                        <li><strong>团队建设:</strong> 组建<strong> 10+ </strong>人跨 AI/内容/数据领域团队，建立跨部门协作机制，推动部门业务发展获得公司认可。</li>
                        <li><strong>协作模式:</strong> 设计喜播学员精修、主播共创等新的内容生产协作模式，建立<strong> 500 </strong>人规模的外部协作人才库。</li>
                        <li><strong>商业验证:</strong> Q1 上架<strong> 547 </strong>张专辑，日均 UG 达<strong> 3 </strong>万，目标 25 年站内 UG 指数/会员收入达到新品的 20%。</li>
                    </ul>
                </div>
            </div>
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2021.5 - 2023.5</span>
                    <div>
                        <span class="company">腾讯视频 · 体育平台研发中心</span>
                        <span class="role">高级后台开发</span>
                    </div>
                </div>
                <div class="entry-details">
                    <p><strong>核心职责:</strong> 负责体育后台架构升级与技术体系建设，支撑日均 <strong>10 </strong>亿+ 流量的稳定运行。</p>
                    <ul>
                        <li><strong>架构升级:</strong> 主导体育后台微服务化改造，重构<strong> 106 </strong>个接口，覆盖 <strong>93%</strong> 流量；QPS 提升 <strong>100%</strong>，响应时间降低 <strong>57%</strong>，可用性提升至 <strong>99.99%</strong>。</li>
                        <li><strong>容灾体系:</strong> 设计全链路降级容灾方案，实现<strong> 3 </strong>分钟内触发告警、<strong> 10 </strong>分钟内定位问题，保障大型赛事期间系统稳定性。</li>
                        <li><strong>研发效能:</strong> 搭建全链路灰度、接口录制回放等基础能力，建设多环境泳道的测试环境治理体系，实现自动化数据构造，成本从<strong> 1–2 </strong>天降低至<strong>分钟级</strong>，环境类 bug 降至 0。</li>
                    </ul>
                </div>
            </div>
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2019.4 - 2021.4</span>
                    <div>
                        <span class="company">一点资讯 · 内容智能部</span>
                        <span class="role">负责人</span>
                    </div>
                </div>
                <div class="entry-details">
                    <p><strong>核心职责:</strong> 负责内容智能技术体系建设，通过全网内容获取与智能分析，提升平台内容供给效率。</p>
                    <ul>
                        <li><strong>全网内容池:</strong> 构建覆盖 <strong>30+</strong> 主流平台的内容获取体系，日均更新 <strong>5000 </strong>万+ 内容，自媒体作者覆盖率达到 <strong>95%</strong>。</li>
                        <li><strong>智能分析:</strong> 建立内容、作者分级体系和特征挖掘能力，为平台提供原创识别、相似度比对等核心服务。</li>
                        <li><strong>业务优化:</strong> 通过数据驱动内容供给策略优化，实现 rctr 提升 <strong>18.4%</strong>，用户人均时长增加 <strong>148 </strong>秒，显著提升平台竞争力。</li>
                    </ul>
                </div>
            </div>
             <div class="entry">
                <div class="entry-header">
                    <span class="date">2018.7 - 2019.4</span>
                    <div>
                        <span class="company">百度视频 · 技术平台1部</span>
                        <span class="role">数据研发</span>
                    </div>
                </div>
                <div class="entry-details">
                    <p><strong>核心职责:</strong> 负责视频内容数据处理与机器智能剪辑业务，构建自动化视频生产能力。</p>
                    <ul>
                        <li><strong>数据处理:</strong> 建设长短视频、PGC 视频全业务线数据爬取与收录体系，保障平台内容供给稳定性。</li>
                        <li><strong>机器剪辑:</strong> 设计并实现机器剪辑视频业务完整技术方案，日均生产 <strong>1000+</strong> 条视频，提升内容生产效率。</li>
                        <li><strong>系统重构:</strong> 重构视频处理流程调度系统，大幅提升处理透明度和系统稳定性，为业务快速发展奠定基础。</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">项目经历</h2>
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2024.1 - 至今</span>
                    <div>
                         <span class="company">AI 原创长/短篇网文规模化生产项目</span>
                         <span class="role">负责人</span>
                    </div>
                </div>
                <div class="entry-details">
                    <p><strong>背景:</strong> 攻克 AI 长/短篇创作的核心技术难题，构建从 0 到 1 的 AI 网文自动化产线，实现规模化商业应用。</p>
                    <ul>
                        <li><strong>第一阶段（0-1 建产线）:</strong> 创新提出融合网文理论、数据案例与 AI 工作流的技术路线，攻克 AI 在长/短篇网文的逻辑接续、情感表达、人设一致性等业界技术难题，成功落地 AI 网文产线。产出质量稳定在市场 <strong>90 </strong>分位以上，代表作品《让你管账号》在读量 <strong>50 </strong>万。</li>
                        <li><strong>第二阶段（1-N 扩规模）:</strong> 设计模块化写作技术方案，建设剧情单元素材库，采用状态算法管理续接逻辑，实现 AI 自动化写作。配套开发 AI 工具集和质检体系，确保规模化后质量稳定。月产能突破 <strong>200 </strong>本，成本降低至行业 <strong>5%</strong>。</li>
                        <li><strong>有声制作:</strong> 设计多线并行制作策略，通过内容分级实现智能匹配，建立 TTS/AI 制作人/真人主播三条制作线。Q1 上架 <strong>547 </strong>张专辑，日均 DAU<strong> 10 </strong>万+。</li>
                    </ul>
                </div>
            </div>
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2021.8 - 2022.6</span>
                    <div>
                         <span class="company">体育接入层升级</span>
                         <span class="role">负责人</span>
                    </div>
                </div>
                <div class="entry-details">
                    <p><strong>背景:</strong> 体育后台最初是 PHP 单体应用，集中在一个高度复杂的大项目中。随着业务发展和技术架构的演进，单体应用逐渐演变为接入层的角色，存在框架老，代码乱，性能差，运营难等问题。</p>
                     <ul>
                         <li>项目一期，主导体育 PHP 接入层改造方案的设计和评审，将架构分层为 API 网关，接口适配层和领域层。以体育核心接口比赛/内容底层页、推荐信息流的重构为标杆案例，沉淀出通用的代码框架和组件，并提供了从代码设计，到正确性保证，再到灰度上线的全流程指引。</li>
                         <li>项目二期，以网关为起点建立全链路可观测体系。设计并落地一整套限流，降级和全链路过载保护的服务容灾方案。</li>
                         <li>收益：在该方案的指引下，体育在过去的一年中重构了 <strong>106 </strong>个接口，覆盖体育 <strong>93%</strong> 的流量，核心接口 QPS 提升 <strong>1 </strong>倍+，响应时间降低 <strong>57%</strong>，实现接口告警 <strong>3 </strong>分钟内触发，问题定位 <strong>10 </strong>分钟内完成，可用性提升至 <strong>99.99%</strong>。</li>
                     </ul>
                </div>
            </div>
            
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2020.11 - 2021.4</span>
                    <div>
                         <span class="company">内容和作者特征挖掘</span>
                         <span class="role">负责人</span>
                    </div>
                </div>
                <div class="entry-details">
                     <ul>
                        <li>支持站内外内容的点赞数、评论数等动态指标的分钟级监控。捕获竞品的 Push、热榜等信号。</li>
                        <li>理解内容主体和分类，挖掘内容的时效、原创、地域、稀缺度等属性，分析评论情感，多维度打分，建立全面的内容分级体系。</li>
                        <li>聚合作者近期的内容特征和表现数据，分析作者成长趋势，建设作者画像。挖掘出各领域的潜力作者，为自媒体平台提供价值线索。并提供创作者覆盖度、站内外表现对比等基础能力。</li>
                        <li>基于内容的站内外表现和基础特征，沉淀冷启池、高热池。通过长期实验，优化平台的内容供给策略，并在分发环节提供了热度信号，大幅提升内容分发效率。rctr 提升 <strong>18.4%</strong>，人均时长增加 <strong>148 </strong>秒。</li>
                     </ul>
                </div>
            </div>
            <div class="entry">
                <div class="entry-header">
                    <span class="date">2020.4 - 2020.10</span>
                     <div>
                         <span class="company">全网内容池</span>
                         <span class="role">负责人</span>
                    </div>
                </div>
                <div class="entry-details">
                     <ul>
                        <li>覆盖大内容平台、垂类 TOP、传统新闻门户在内的 <strong>30+</strong> 个站点，提供海量内容，每日更新量 <strong>5000 </strong>万+，主流站点自媒体作者覆盖率达到 <strong>95%</strong>。</li>
                        <li>基于 Airflow 分布式调度框架，定义出细分任务和组合链路，细分任务是最小粒度的爬取目标，组合链路将细分任务灵活地拼接，满足具体的爬取需求，增加可复用性。大幅提高开发效率和可维护性。</li>
                        <li>针对反爬和风控，设计一系列反反爬服务和策略，如代理 IP 模块、Cookie 模块、验证码识别、手机群控和浏览器集群等，突破公众号、小红书、抖音等主流平台。</li>
                        <li>设计了一套海量内容存储方案，包括去重、冷热分离和动态指标拉链等。</li>
                        <li>爬虫系统 PaaS 平台化，针对不同场景的内容获取需求，提供个性化爬取链路配置、任务调度和内容回传能力。</li>
                     </ul>
                </div>
            </div>
        </section>

    </div>

 </body>
</html> 